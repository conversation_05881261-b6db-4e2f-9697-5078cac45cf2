tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.viewSelector={},function(e,t){"use strict";const i=e(document);t.selectors={viewSelector:'[data-js="tribe-events-view-selector"]',viewSelectorTabsClass:".tribe-events-c-view-selector--tabs",viewSelectorButton:'[data-js="tribe-events-view-selector-button"]',viewSelectorButtonActiveClass:".tribe-events-c-view-selector__button--active",viewSelectorListContainer:'[data-js="tribe-events-view-selector-list-container"]'},t.deinitAccordion=function(e,t){tribe.events.views.accordion.deinitAccordion(0,e),tribe.events.views.accordion.deinitAccordionA11yAttrs(e,t),t.css("display","")},t.initAccordion=function(e,t,i){tribe.events.views.accordion.initAccordion(e)(0,t),tribe.events.views.accordion.initAccordionA11yAttrs(t,i)},t.deinitViewSelectorAccordion=function(e){const i=e.find(t.selectors.viewSelectorButton),n=e.find(t.selectors.viewSelectorListContainer);t.deinitAccordion(i,n),i.removeClass(t.selectors.viewSelectorButtonActiveClass.className())},t.initViewSelectorAccordion=function(e){const i=e.find(t.selectors.viewSelectorButton),n=e.find(t.selectors.viewSelectorListContainer);t.initAccordion(e,i,n)},t.initState=function(e){e.find(t.selectors.viewSelector).data("tribeEventsState",{mobileInitialized:!1,desktopInitialized:!1})},t.deinitViewSelector=function(e){t.deinitViewSelectorAccordion(e)},t.initViewSelector=function(e){const i=e.find(t.selectors.viewSelector);if(i.length){const n=i.data("tribeEventsState");if(i.hasClass(t.selectors.viewSelectorTabsClass.className())){const o=e.data("tribeEventsState").isMobile;o&&!n.mobileInitialized?(t.initViewSelectorAccordion(e),n.desktopInitialized=!1,n.mobileInitialized=!0,i.data("tribeEventsState",n)):o||n.desktopInitialized||(t.deinitViewSelectorAccordion(e),n.mobileInitialized=!1,n.desktopInitialized=!0,i.data("tribeEventsState",n))}else n.mobileInitialized||n.desktopInitialized||(t.initViewSelectorAccordion(e),n.desktopInitialized=!0,n.mobileInitialized=!0,i.data("tribeEventsState",n))}},t.handleViewSelectorButtonClick=function(e){e.data.target.toggleClass(t.selectors.viewSelectorButtonActiveClass.className())},t.handleClick=function(i){if(!Boolean(e(i.target).closest(t.selectors.viewSelector).length)){const e=i.data.container.find(t.selectors.viewSelector),n=e.find(t.selectors.viewSelectorButton);if(n.hasClass(t.selectors.viewSelectorButtonActiveClass.className())){const i=e.find(t.selectors.viewSelectorListContainer);n.removeClass(t.selectors.viewSelectorButtonActiveClass.className()),tribe.events.views.accordion.closeAccordion(n,i)}}},t.handleResize=function(e){t.initViewSelector(e.data.container)},t.unbindEvents=function(e){i.off("click",t.handleClick),e.off("resize.tribeEvents",t.handleResize).find(t.selectors.viewSelectorButton).off("click",t.handleViewSelectorButtonClick)},t.bindEvents=function(e){const n=e.find(t.selectors.viewSelectorButton);i.on("click",{container:e},t.handleClick),e.on("resize.tribeEvents",{container:e},t.handleResize),n.on("click",{target:n},t.handleViewSelectorButtonClick)},t.deinit=function(e,i,n){const o=e.data.container;t.deinitViewSelector(o),t.unbindEvents(o),o.off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,i,n,o){n.find(t.selectors.viewSelector).length&&(t.initState(n),t.initViewSelector(n),t.bindEvents(n),n.on("beforeAjaxSuccess.tribeEvents",{container:n},t.deinit))},t.ready=function(){i.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.viewSelector),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.viewSelector={}