
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":4,"vtp_isAutoEnabled":true,"vtp_autoPhoneEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoAddressEnabled":true,"vtp_autoEmailEnabled":true,"vtp_isAutoCollectPiiEnabledFlag":true,"tag_id":4},{"function":"__ccd_ads_first","priority":3,"vtp_instanceDestinationId":"AW-10837089199","tag_id":9},{"function":"__ccd_em_page_view","priority":2,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"AW-10837089199","tag_id":8},{"function":"__ccd_ads_conv_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\"}]}}]}},{\"booleanExpressionValue\":{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":5,\"args\":[{\"stringValue\":\"page_view\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}},{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\/contact\/shop-local\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\/contact\/shop-local\"}]}}]}}]}}]}","conversionLabel","1CGHCL3k5qcYEK-3w68o"],["map","matchingRules","{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\"}]}}]}},{\"booleanExpressionValue\":{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":5,\"args\":[{\"stringValue\":\"page_view\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}},{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\/checkout\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\/checkout\"}]}}]}}]}}]}","conversionLabel","jQenCLO-3acYEK-3w68o"],["map","matchingRules","{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\"}]}}]}},{\"booleanExpressionValue\":{\"type\":2,\"args\":[{\"booleanExpressionValue\":{\"type\":5,\"args\":[{\"stringValue\":\"page_view\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}},{\"booleanExpressionValue\":{\"type\":1,\"args\":[{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"http:\/\/segishop.com\/cart\"}]}},{\"booleanExpressionValue\":{\"type\":6,\"args\":[{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"hitData\",\"page_location\"]}},{\"stringValue\":\"https:\/\/segishop.com\/cart\"}]}}]}}]}}]}","conversionLabel","GorECOW93acYEK-3w68o"]],"vtp_instanceDestinationId":"AW-10837089199","tag_id":7},{"function":"__rep","vtp_containerId":"AW-10837089199","vtp_remoteConfig":["map","enhanced_conversions",["map"]],"tag_id":1},{"function":"__ccd_ads_last","priority":0,"vtp_instanceDestinationId":"AW-10837089199","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",4]],[["if",1],["add",0,5,3,2,1]]]
},
"runtime":[ [50,"__ccd_ads_conv_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.processAsNewEvent"]],[52,"e",["require","internal.registerCcdCallback"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g",[15,"__module_gtagSchema"]],[52,"h","conversion"],["e",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"i"],[22,[30,[21,[2,[15,"i"],"getMetadata",[7,[17,[15,"f"],"K"]]],[15,"h"]],[2,[15,"i"],"getMetadata",[7,[17,[15,"f"],"AH"]]]],[46,[53,[36]]]],[52,"j",[8,"preHit",[15,"i"]]],[52,"k",[2,[15,"i"],"getEventName",[7]]],[22,[20,[15,"k"],[17,[15,"g"],"M"]],[46,[53,[2,[15,"i"],"setEventName",[7,[17,[15,"g"],"O"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"j"]],[46,[53,[52,"m",["b",[15,"i"]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"f"],"L"],[15,"h"]]],[2,[15,"m"],"setHitData",[7,[17,[15,"g"],"DG"],[17,[15,"l"],"conversionLabel"]]],["d",[15,"m"]]]]]]]],[22,[20,[15,"k"],[17,[15,"g"],"M"]],[46,[53,[2,[15,"i"],"setEventName",[7,[15,"k"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ads_first",[46,"a"],[50,"e",[46,"f"],[2,[15,"c"],"B",[7,[15,"f"]]],[2,[15,"d"],"A",[7,[15,"f"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_webPrivacyTasks"]],[52,"d",[15,"__module_taskConversionAutoDataAnalysis"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],["e",[15,"f"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ads_last",[46,"a"],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_metadataSchema"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",[15,"__module_adwordsHitType"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],[52,"g",[2,[15,"f"],"getMetadata",[7,[17,[15,"c"],"K"]]]],[22,[1,[20,[15,"g"],[17,[15,"e"],"B"]],[28,[2,[15,"f"],"getHitData",[7,[17,[15,"d"],"DG"]]]]],[46,[53,[2,[15,"f"],"abort",[7]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[17,[15,"g"],"G"],true],[43,[15,"t"],[17,[15,"g"],"AG"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmPageViewActivity"]],[52,"g",[15,"__module_metadataSchema"]],[52,"h","ae_block_history"],[52,"i","page_view"],[52,"j","isRegistered"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[15,"k"]]],[22,[2,[15,"e"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnHistoryChange"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[8,"interval",1000,"useV2EventName",true]],[52,"q",["m",[15,"p"]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"j"],true]],["l","gtm.historyChange-v2",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.oldUrl"]],[22,[20,[16,[15,"s"],"gtm.newUrl"],[15,"u"]],[46,[36]]],[52,"v",[16,[15,"s"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"v"],"pushState"],[21,[15,"v"],"popstate"]],[21,[15,"v"],"replaceState"]],[46,[53,[36]]]],[52,"w",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"w"],"page_location",[16,[15,"s"],"gtm.newUrl"]],[43,[15,"w"],"page_referrer",[15,"u"]]]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"x"],"deferrable",true]]]],["r",[15,"x"]],["o",["n"],[15,"i"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EP"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AC"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CY"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AD"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",0],[52,"c",1],[52,"d",2],[52,"e",3],[52,"f",4],[52,"g",5],[52,"h",6],[52,"i",7],[52,"j",8],[52,"k",9],[52,"l",10],[52,"m",12],[52,"n",13],[52,"o",16],[52,"p",17],[52,"q",19],[52,"r",20],[52,"s",21],[52,"t",22],[52,"u",23],[52,"v",24],[52,"w",25],[52,"x",26],[52,"y",27],[52,"z",28],[52,"aA",29],[52,"aB",30],[52,"aC",31],[52,"aD",32],[52,"aE",33],[52,"aF",34],[52,"aG",35],[52,"aH",36],[52,"aI",37],[52,"aJ",38],[52,"aK",39],[52,"aL",40],[52,"aM",41],[52,"aN",47],[52,"aO",42],[52,"aP",43],[52,"aQ",44],[52,"aR",45],[52,"aS",46],[52,"aT",48],[52,"aU",49],[52,"aV",52],[52,"aW",53],[52,"aX",54],[52,"aY",56],[52,"aZ",58],[52,"bA",59],[52,"bB",60],[52,"bC",62],[52,"bD",63],[52,"bE",66],[52,"bF",68],[52,"bG",69],[52,"bH",71],[52,"bI",72],[52,"bJ",75],[52,"bK",78],[52,"bL",83],[52,"bM",84],[52,"bN",86],[52,"bO",87],[52,"bP",88],[52,"bQ",89],[52,"bR",90],[52,"bS",91],[52,"bT",92],[52,"bU",93],[52,"bV",94],[52,"bW",95],[52,"bX",96],[52,"bY",97],[52,"bZ",100],[52,"cA",101],[52,"cB",102],[52,"cC",103],[52,"cD",104],[52,"cE",106],[52,"cF",107],[52,"cG",108],[52,"cH",109],[52,"cI",111],[52,"cJ",112],[52,"cK",113],[52,"cL",114],[52,"cM",115],[52,"cN",116],[52,"cO",117],[52,"cP",118],[52,"cQ",119],[52,"cR",120],[52,"cS",121],[52,"cT",122],[52,"cU",123],[52,"cV",125],[52,"cW",126],[52,"cX",127],[52,"cY",128],[52,"cZ",129],[52,"dA",130],[52,"dB",131],[52,"dC",132],[52,"dD",133],[52,"dE",134],[52,"dF",135],[52,"dG",136],[52,"dH",137],[52,"dI",138],[52,"dJ",139],[52,"dK",140],[52,"dL",141],[52,"dM",142],[52,"dN",143],[52,"dO",144],[52,"dP",145],[52,"dQ",146],[52,"dR",147],[52,"dS",148],[52,"dT",149],[52,"dU",152],[52,"dV",153],[52,"dW",154],[52,"dX",155],[52,"dY",156],[52,"dZ",157],[52,"eA",158],[52,"eB",159],[52,"eC",160],[52,"eD",162],[52,"eE",163],[52,"eF",164],[52,"eG",165],[52,"eH",167],[52,"eI",168],[52,"eJ",169],[52,"eK",170],[52,"eL",171],[52,"eM",174],[52,"eN",175],[52,"eO",176],[52,"eP",177],[52,"eQ",178],[52,"eR",180],[52,"eS",182],[52,"eT",183],[52,"eU",185],[52,"eV",186],[52,"eW",187],[52,"eX",188],[52,"eY",189],[52,"eZ",190],[52,"fA",191],[52,"fB",192],[52,"fC",193],[52,"fD",194],[52,"fE",195],[52,"fF",196],[52,"fG",197],[52,"fH",198],[52,"fI",199],[52,"fJ",200],[52,"fK",201],[36,[8,"E",[15,"f"],"F",[15,"g"],"EV",[15,"eW"],"EX",[15,"eY"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"EH",[15,"eI"],"P",[15,"q"],"FG",[15,"fH"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"EC",[15,"eD"],"AK",[15,"aL"],"AL",[15,"aM"],"FC",[15,"fD"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AM",[15,"aN"],"AS",[15,"aT"],"FA",[15,"fB"],"AT",[15,"aU"],"EW",[15,"eX"],"AV",[15,"aW"],"AW",[15,"aX"],"AU",[15,"aV"],"AX",[15,"aY"],"AY",[15,"aZ"],"EG",[15,"eH"],"AZ",[15,"bA"],"EJ",[15,"eK"],"EL",[15,"eM"],"EY",[15,"eZ"],"BA",[15,"bB"],"EE",[15,"eF"],"EP",[15,"eQ"],"BB",[15,"bC"],"BC",[15,"bD"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"EZ",[15,"fA"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"EQ",[15,"eR"],"BJ",[15,"bK"],"EN",[15,"eO"],"BK",[15,"bL"],"EM",[15,"eN"],"BL",[15,"bM"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"EF",[15,"eG"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"BM",[15,"bN"],"BN",[15,"bO"],"FH",[15,"fI"],"CA",[15,"cB"],"CB",[15,"cC"],"FF",[15,"fG"],"CC",[15,"cD"],"ER",[15,"eS"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"ED",[15,"eE"],"CN",[15,"cO"],"CM",[15,"cN"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"FI",[15,"fJ"],"EO",[15,"eP"],"FB",[15,"fC"],"CT",[15,"cU"],"ET",[15,"eU"],"EK",[15,"eL"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"EU",[15,"eV"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"EI",[15,"eJ"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"],"DG",[15,"dH"],"DH",[15,"dI"],"DI",[15,"dJ"],"DJ",[15,"dK"],"DK",[15,"dL"],"DL",[15,"dM"],"DM",[15,"dN"],"FJ",[15,"fK"],"DN",[15,"dO"],"DO",[15,"dP"],"FD",[15,"fE"],"FE",[15,"fF"],"DP",[15,"dQ"],"B",[15,"c"],"D",[15,"e"],"C",[15,"d"],"DQ",[15,"dR"],"DR",[15,"dS"],"DS",[15,"dT"],"DT",[15,"dU"],"DU",[15,"dV"],"A",[15,"b"],"DV",[15,"dW"],"DW",[15,"dX"],"DX",[15,"dY"],"DY",[15,"dZ"],"DZ",[15,"eA"],"ES",[15,"eT"],"EA",[15,"eB"],"EB",[15,"eC"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_adwordsHitType",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","call_conversion"],[52,"c","conversion"],[52,"d","floodlight"],[52,"e","ga_conversion"],[52,"f","landing_page"],[52,"g","page_view"],[52,"h","remarketing"],[52,"i","user_data_lead"],[52,"j","user_data_web"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_webPrivacyTasks",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"f"],[52,"g",["b"]],[65,"h",[7,[17,[15,"c"],"DA"],[17,[15,"c"],"BC"],[17,[15,"c"],"DJ"]],[46,[53,[2,[15,"f"],"setHitData",[7,[15,"h"],[16,[15,"g"],[15,"h"]]]]]]]],[50,"e",[46,"f"],[52,"g",["b"]],[22,[16,[15,"g"],[17,[15,"c"],"BG"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"BG"],[16,[15,"g"],[17,[15,"c"],"BG"]]]]]]],[22,[16,[15,"g"],[17,[15,"c"],"BF"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"BF"],[16,[15,"g"],[17,[15,"c"],"BF"]]]]]]]],[52,"b",["require","internal.getPrivacyStrings"]],[52,"c",[15,"__module_gtagSchema"]],[36,[8,"B",[15,"e"],"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskConversionAutoDataAnalysis",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"o",[46,"p"],[22,[28,[1,[17,[15,"e"],"enableConversionAutoDataAnalysis"],[20,[2,[15,"p"],"getMetadata",[7,[17,[15,"i"],"K"]]],[17,[15,"b"],"B"]]]],[46,[53,[36]]]],[52,"q",[7]],[65,"r",[15,"n"],[46,[53,[52,"s",["c",[17,[15,"r"],"modelKey"]]],[52,"t",["f",[15,"s"]]],[22,[28,[30,[20,[15,"t"],"string"],[20,[15,"t"],"number"]]],[46,[6]]],[22,[28,["j",[17,[15,"r"],"regexp"],[2,["h",[15,"s"]],"replace",[7,["d","\\s","g"],""]]]],[46,[53,[6]]]],[2,[15,"q"],"push",[7,[17,[15,"r"],"googleAnalysisKey"]]]]]],[22,[28,[17,[15,"q"],"length"]],[46,[36]]],[2,[15,"p"],"mergeHitDataForKey",[7,[17,[15,"g"],"BD"],[8,"cad",[2,[15,"q"],"join",[7,"."]]]]]],[52,"b",[15,"__module_adwordsHitType"]],[52,"c",["require","copyFromDataLayer"]],[52,"d",["require","internal.createRegex"]],[52,"e",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"f",["require","getType"]],[52,"g",[15,"__module_gtagSchema"]],[52,"h",["require","makeString"]],[52,"i",[15,"__module_metadataSchema"]],[52,"j",["require","internal.testRegex"]],[52,"k",["d","^(?:[1-9][\\d.,]*)|(?:0?[.,]\\d+)$"]],[52,"l",["d","^[A-Za-z]{3}$"]],[52,"m",["d","^.+$"]],[52,"n",[7,[8,"modelKey","ecommerce.value","googleAnalysisKey",1,"regexp",[15,"k"]],[8,"modelKey","ecommerce.currency","googleAnalysisKey",31,"regexp",[15,"l"]],[8,"modelKey","ecommerce.currencyCode","googleAnalysisKey",33,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.value","googleAnalysisKey",2,"regexp",[15,"k"]],[8,"modelKey","ecommerce.purchase.currency","googleAnalysisKey",39,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.transaction_id","googleAnalysisKey",68,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.actionField.revenue","googleAnalysisKey",3,"regexp",[15,"k"]],[8,"modelKey","ecommerce.purchase.actionField.currency","googleAnalysisKey",41,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.actionField.id","googleAnalysisKey",62,"regexp",[15,"m"]],[8,"modelKey","ecommerce.cart.currencyCode","googleAnalysisKey",45,"regexp",[15,"l"]],[8,"modelKey","ecommerce.transaction_id","googleAnalysisKey",61,"regexp",[15,"m"]],[8,"modelKey","common_model.order.id","googleAnalysisKey",69,"regexp",[15,"m"]],[8,"modelKey","common_model.order.total_amounts.revenue","googleAnalysisKey",10,"regexp",[15,"k"]],[8,"modelKey","common.currency","googleAnalysisKey",42,"regexp",[15,"l"]],[8,"modelKey","orderConversions.currency","googleAnalysisKey",43,"regexp",[15,"l"]],[8,"modelKey","eventModel.value","googleAnalysisKey",4,"regexp",[15,"k"]],[8,"modelKey","eventModel.currency","googleAnalysisKey",34,"regexp",[15,"l"]],[8,"modelKey","eventModel.transaction_id","googleAnalysisKey",64,"regexp",[15,"m"]],[8,"modelKey","context.localization.currency_code","googleAnalysisKey",35,"regexp",[15,"l"]],[8,"modelKey","leadsHookData.googleConversion.value","googleAnalysisKey",15,"regexp",[15,"k"]],[8,"modelKey","leadsHookData.googleConversion.currency","googleAnalysisKey",44,"regexp",[15,"l"]],[8,"modelKey","orderData.attributes.order_number","googleAnalysisKey",74,"regexp",[15,"m"]],[8,"modelKey","order.id","googleAnalysisKey",75,"regexp",[15,"m"]],[8,"modelKey","transaction.id","googleAnalysisKey",76,"regexp",[15,"m"]],[8,"modelKey","transactionTotal","googleAnalysisKey",5,"regexp",[15,"k"]],[8,"modelKey","value","googleAnalysisKey",6,"regexp",[15,"k"]],[8,"modelKey","totalValue","googleAnalysisKey",7,"regexp",[15,"k"]],[8,"modelKey","ecomm_totalvalue","googleAnalysisKey",8,"regexp",[15,"k"]],[8,"modelKey","price","googleAnalysisKey",9,"regexp",[15,"k"]],[8,"modelKey","conversionValue","googleAnalysisKey",11,"regexp",[15,"k"]],[8,"modelKey","ihAmount","googleAnalysisKey",12,"regexp",[15,"k"]],[8,"modelKey","wp_conversion_value","googleAnalysisKey",13,"regexp",[15,"k"]],[8,"modelKey","revenue","googleAnalysisKey",14,"regexp",[15,"k"]],[8,"modelKey","currency","googleAnalysisKey",32,"regexp",[15,"l"]],[8,"modelKey","transactionCurrency","googleAnalysisKey",36,"regexp",[15,"l"]],[8,"modelKey","currencyCode","googleAnalysisKey",37,"regexp",[15,"l"]],[8,"modelKey","ihCurrency","googleAnalysisKey",38,"regexp",[15,"l"]],[8,"modelKey","CurrCode","googleAnalysisKey",40,"regexp",[15,"l"]],[8,"modelKey","transactionId","googleAnalysisKey",63,"regexp",[15,"m"]],[8,"modelKey","transaction_id","googleAnalysisKey",65,"regexp",[15,"m"]],[8,"modelKey","order_id","googleAnalysisKey",66,"regexp",[15,"m"]],[8,"modelKey","orderId","googleAnalysisKey",67,"regexp",[15,"m"]],[8,"modelKey","ihConfirmID","googleAnalysisKey",70,"regexp",[15,"m"]],[8,"modelKey","wp_order_id","googleAnalysisKey",71,"regexp",[15,"m"]],[8,"modelKey","orderID","googleAnalysisKey",72,"regexp",[15,"m"]],[8,"modelKey","id","googleAnalysisKey",73,"regexp",[15,"m"]]]],[36,[8,"A",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"Y"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"BU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"BU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"AG"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"O"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__ccd_ads_conv_marking":{"2":true}
,
"__ccd_ads_first":{"2":true,"4":true}
,
"__ccd_ads_last":{"2":true,"4":true}
,
"__ccd_em_page_view":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__ogt_1p_data_v2":{"2":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__ccd_ads_conv_marking":{}
,
"__ccd_ads_first":{"read_data_layer":{"allowedKeys":"any"}}
,
"__ccd_ads_last":{}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}


}



,"security_groups":{
"google":[
"__ccd_ads_conv_marking"
,
"__ccd_ads_first"
,
"__ccd_ads_last"
,
"__ccd_em_page_view"
,
"__e"
,
"__ogt_1p_data_v2"

]


}



};




var ba,ca=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&da(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;da(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ma;
if(typeof Object.setPrototypeOf=="function")ma=Object.setPrototypeOf;else{var oa;a:{var pa={a:!0},qa={};try{qa.__proto__=pa;oa=qa.a;break a}catch(a){}oa=!1}ma=oa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ra=ma,sa=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(ra)ra(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.tq=b.prototype},k=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ta=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:ta(k(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.tq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.rr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map={};this.D={}};Ca.prototype.get=function(a){return this.map["dust."+a]};Ca.prototype.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};Ca.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ca.prototype.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Da=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ca.prototype.Aa=function(){return Da(this,1)};Ca.prototype.yc=function(){return Da(this,2)};Ca.prototype.Xb=function(){return Da(this,3)};var Ea=function(){};Ea.prototype.reset=function(){};var Ga=function(a,b){this.R=a;this.parent=b;this.D=this.J=void 0;this.Rc=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=new Ca};Ga.prototype.add=function(a,b){Ha(this,a,b,!1)};var Ha=function(a,b,c,d){if(!a.Rc)if(d){var e=a.values;e.set(b,c);e.D["dust."+b]=!0}else a.values.set(b,c)};Ga.prototype.set=function(a,b){this.Rc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Ga.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Ga.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ia=function(a){var b=new Ga(a.R,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};Ga.prototype.te=function(){return this.R};Ga.prototype.eb=function(){this.Rc=!0};var Ja=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.vm=a;this.Yl=c===void 0?!1:c;this.debugInfo=[];this.D=b};sa(Ja,Error);var Ka=function(a){return a instanceof Ja?a:new Ja(a,void 0,!0)};function La(a,b){for(var c,d=k(b),e=d.next();!e.done&&!(c=Ma(a,e.value),c instanceof Ba);e=d.next());return c}function Ma(a,b){try{var c=k(b),d=c.next().value,e=ta(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ka(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Na=function(){this.J=new Ea;this.D=new Ga(this.J)};ba=Na.prototype;ba.te=function(){return this.J};ba.execute=function(a){return this.Lj([a].concat(ua(ya.apply(1,arguments))))};ba.Lj=function(){for(var a,b=k(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ma(this.D,c.value);return a};ba.lo=function(a){var b=ya.apply(1,arguments),c=Ia(this.D);c.D=a;for(var d,e=k(b),f=e.next();!f.done;f=e.next())d=Ma(c,f.value);return d};ba.eb=function(){this.D.eb()};var Oa=function(){this.Da=!1;this.ba=new Ca};ba=Oa.prototype;ba.get=function(a){return this.ba.get(a)};ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.yc=function(){return this.ba.yc()};ba.Xb=function(){return this.ba.Xb()};ba.eb=function(){this.Da=!0};ba.Rc=function(){return this.Da};function Pa(){for(var a=Qa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Sa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Qa,Ta;function Ua(a){Qa=Qa||Sa();Ta=Ta||Pa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Qa[m],Qa[n],Qa[p],Qa[q])}return b.join("")}
function Va(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ta[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Qa=Qa||Sa();Ta=Ta||Pa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Wa={};function Ya(a,b){Wa[a]=Wa[a]||[];Wa[a][b]=!0}function Za(){Wa.GTAG_EVENT_FEATURE_CHANNEL=$a}function ab(a){var b=Wa[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Ua(c.join("")).replace(/\.+$/,"")}function cb(){for(var a=[],b=Wa.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function db(){}function eb(a){return typeof a==="function"}function fb(a){return typeof a==="string"}function gb(a){return typeof a==="number"&&!isNaN(a)}function hb(a){return Array.isArray(a)?a:[a]}function jb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function kb(a,b){if(!gb(a)||!gb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function lb(a,b){for(var c=new mb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function nb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ob(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function pb(a){return Math.round(Number(a))||0}function qb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function rb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function sb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function tb(){return new Date(Date.now())}function ub(){return tb().getTime()}var mb=function(){this.prefix="gtm.";this.values={}};mb.prototype.set=function(a,b){this.values[this.prefix+a]=b};mb.prototype.get=function(a){return this.values[this.prefix+a]};mb.prototype.contains=function(a){return this.get(a)!==void 0};
function vb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function wb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function xb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function yb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function zb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Ab(a,b){var c=l;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Bb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Cb=/^\w{1,9}$/;function Db(a,b){a=a||{};b=b||",";var c=[];nb(a,function(d,e){Cb.test(d)&&e&&c.push(d)});return c.join(b)}function Eb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Fb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Gb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Hb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ib=globalThis.trustedTypes,Jb;function Kb(){var a=null;if(!Ib)return a;try{var b=function(c){return c};a=Ib.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Lb(){Jb===void 0&&(Jb=Kb());return Jb};var Mb=function(a){this.D=a};Mb.prototype.toString=function(){return this.D+""};function Nb(a){var b=a,c=Lb(),d=c?c.createScriptURL(b):b;return new Mb(d)}function Ob(a){if(a instanceof Mb)return a.D;throw Error("");};var Pb=wa([""]),Qb=va(["\x00"],["\\0"]),Rb=va(["\n"],["\\n"]),Sb=va(["\x00"],["\\u0000"]);function Tb(a){return a.toString().indexOf("`")===-1}Tb(function(a){return a(Pb)})||Tb(function(a){return a(Qb)})||Tb(function(a){return a(Rb)})||Tb(function(a){return a(Sb)});var Ub=function(a){this.D=a};Ub.prototype.toString=function(){return this.D};var Vb=function(a){this.Qp=a};function Wb(a){return new Vb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Xb=[Wb("data"),Wb("http"),Wb("https"),Wb("mailto"),Wb("ftp"),new Vb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Yb(a){var b;b=b===void 0?Xb:b;if(a instanceof Ub)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Vb&&d.Qp(a))return new Ub(a)}}var Zb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function $b(a){var b;if(a instanceof Ub)if(a instanceof Ub)b=a.D;else throw Error("");else b=Zb.test(a)?a:void 0;return b};function ac(a,b){var c=$b(b);c!==void 0&&(a.action=c)};function bc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var cc=function(a){this.D=a};cc.prototype.toString=function(){return this.D+""};var ec=function(){this.D=dc[0].toLowerCase()};ec.prototype.toString=function(){return this.D};function fc(a,b){var c=[new ec];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof ec)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var hc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function ic(a){return a===null?"null":a===void 0?"undefined":a};var l=window,jc=window.history,y=document,kc=navigator;function lc(){var a;try{a=kc.serviceWorker}catch(b){return}return a}var mc=y.currentScript,nc=mc&&mc.src;function oc(a,b){var c=l[a];l[a]=c===void 0?b:c;return l[a]}function pc(a){return(kc.userAgent||"").indexOf(a)!==-1}function qc(){return pc("Firefox")||pc("FxiOS")}function rc(){return(pc("GSA")||pc("GoogleApp"))&&(pc("iPhone")||pc("iPad"))}function sc(){return pc("Edg/")||pc("EdgA/")||pc("EdgiOS/")}
var tc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},uc={onload:1,src:1,width:1,height:1,style:1};function vc(a,b,c){b&&nb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function wc(a,b,c,d,e){var f=y.createElement("script");vc(f,d,tc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Nb(ic(a));f.src=Ob(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=y.getElementsByTagName("script")[0]||y.body||y.head;r.parentNode.insertBefore(f,r)}return f}
function xc(){if(nc){var a=nc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function yc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=y.createElement("iframe"),h=!0);vc(g,c,uc);d&&nb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=y.body&&y.body.lastChild||y.body||y.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function zc(a,b,c,d){return Ac(a,b,c,d)}function Bc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Cc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function A(a){l.setTimeout(a,0)}function Dc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Ec(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Fc(a){var b=y.createElement("div"),c=b,d,e=ic("A<div>"+a+"</div>"),f=Lb(),g=f?f.createHTML(e):e;d=new cc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof cc)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Ic(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Jc(a,b,c){var d;try{d=kc.sendBeacon&&kc.sendBeacon(a)}catch(e){Ya("TAGGING",15)}d?b==null||b():Ac(a,b,c)}function Kc(a,b){try{return kc.sendBeacon(a,b)}catch(c){Ya("TAGGING",15)}return!1}var Lc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Mc(a,b,c,d,e){if(Nc()){var f=Object.assign({},Lc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=l.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Hh)return e==null||e(),!1;if(b){var h=
Kc(a,b);h?d==null||d():e==null||e();return h}Oc(a,d,e);return!0}function Nc(){return typeof l.fetch==="function"}function Pc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Qc(){var a=l.performance;if(a&&eb(a.now))return a.now()}
function Rc(){var a,b=l.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Sc(){return l.performance||void 0}function Tc(){var a=l.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Ac=function(a,b,c,d){var e=new Image(1,1);vc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Oc=Jc;function Uc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Vc(a,b){return this.evaluate(a)===this.evaluate(b)}function Wc(a,b){return this.evaluate(a)||this.evaluate(b)}function Xc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Yc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Zc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=l.location.href;d instanceof Oa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var $c=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,ad=function(a){if(a==null)return String(a);var b=$c.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},bd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},cd=function(a){if(!a||ad(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!bd(a,"constructor")&&!bd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
bd(a,b)},dd=function(a,b){var c=b||(ad(a)=="array"?[]:{}),d;for(d in a)if(bd(a,d)){var e=a[d];ad(e)=="array"?(ad(c[d])!="array"&&(c[d]=[]),c[d]=dd(e,c[d])):cd(e)?(cd(c[d])||(c[d]={}),c[d]=dd(e,c[d])):c[d]=e}return c};function ed(a){if(a==void 0||Array.isArray(a)||cd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function fd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var gd=function(a){a=a===void 0?[]:a;this.ba=new Ca;this.values=[];this.Da=!1;for(var b in a)a.hasOwnProperty(b)&&(fd(b)?this.values[Number(b)]=a[Number(b)]:this.ba.set(b,a[b]))};ba=gd.prototype;ba.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof gd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
ba.set=function(a,b){if(!this.Da)if(a==="length"){if(!fd(b))throw Ka(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else fd(a)?this.values[Number(a)]=b:this.ba.set(a,b)};ba.get=function(a){return a==="length"?this.length():fd(a)?this.values[Number(a)]:this.ba.get(a)};ba.length=function(){return this.values.length};ba.Aa=function(){for(var a=this.ba.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
ba.yc=function(){for(var a=this.ba.yc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};ba.Xb=function(){for(var a=this.ba.Xb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};ba.remove=function(a){fd(a)?delete this.values[Number(a)]:this.Da||this.ba.remove(a)};ba.pop=function(){return this.values.pop()};ba.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};
ba.shift=function(){return this.values.shift()};ba.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new gd(this.values.splice(a)):new gd(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};ba.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};ba.has=function(a){return fd(a)&&this.values.hasOwnProperty(a)||this.ba.has(a)};ba.eb=function(){this.Da=!0;Object.freeze(this.values)};ba.Rc=function(){return this.Da};
function hd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var id=function(a,b){this.functionName=a;this.se=b;this.ba=new Ca;this.Da=!1};ba=id.prototype;ba.toString=function(){return this.functionName};ba.getName=function(){return this.functionName};ba.getKeys=function(){return new gd(this.Aa())};ba.invoke=function(a){return this.se.call.apply(this.se,[new jd(this,a)].concat(ua(ya.apply(1,arguments))))};ba.Jb=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};ba.get=function(a){return this.ba.get(a)};
ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.yc=function(){return this.ba.yc()};ba.Xb=function(){return this.ba.Xb()};ba.eb=function(){this.Da=!0};ba.Rc=function(){return this.Da};var kd=function(a,b){id.call(this,a,b)};sa(kd,id);var ld=function(a,b){id.call(this,a,b)};sa(ld,id);var jd=function(a,b){this.se=a;this.M=b};
jd.prototype.evaluate=function(a){var b=this.M;return Array.isArray(a)?Ma(b,a):a};jd.prototype.getName=function(){return this.se.getName()};jd.prototype.te=function(){return this.M.te()};var md=function(){this.map=new Map};md.prototype.set=function(a,b){this.map.set(a,b)};md.prototype.get=function(a){return this.map.get(a)};var nd=function(){this.keys=[];this.values=[]};nd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};nd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function od(){try{return Map?new md:new nd}catch(a){return new nd}};var pd=function(a){if(a instanceof pd)return a;if(ed(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};pd.prototype.getValue=function(){return this.value};pd.prototype.toString=function(){return String(this.value)};var rd=function(a){this.promise=a;this.Da=!1;this.ba=new Ca;this.ba.set("then",qd(this));this.ba.set("catch",qd(this,!0));this.ba.set("finally",qd(this,!1,!0))};ba=rd.prototype;ba.get=function(a){return this.ba.get(a)};ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.yc=function(){return this.ba.yc()};ba.Xb=function(){return this.ba.Xb()};
var qd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new kd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof kd||(d=void 0);e instanceof kd||(e=void 0);var f=Ia(this.M),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new pd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new rd(h)})};rd.prototype.eb=function(){this.Da=!0};rd.prototype.Rc=function(){return this.Da};function sd(a,b,c){var d=od(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof gd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof rd)return g.promise.then(function(u){return sd(u,b,1)},function(u){return Promise.reject(sd(u,b,1))});if(g instanceof Oa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof kd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=td(u[w],b,c);var x=new Ga(b?b.te():new Ea);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof pd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function td(a,b,c){var d=od(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ob(g)){var m=new gd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(cd(g)){var p=new Oa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new kd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=sd(this.evaluate(u[w]),b,c);return f((0,this.M.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new pd(g)};return f(a)};var ud={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof gd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new gd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new gd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new gd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ka(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ka(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=hd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new gd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=hd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var vd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},wd=new Ba("break"),xd=new Ba("continue");function yd(a,b){return this.evaluate(a)+this.evaluate(b)}function zd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Ad(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof gd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ka(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=sd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ka(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(vd.hasOwnProperty(e)){var m=2;m=1;var n=sd(f,void 0,m);return td(d[e].apply(d,n),this.M)}throw Ka(Error("TypeError: "+e+" is not a function"));}if(d instanceof gd){if(d.has(e)){var p=d.get(String(e));if(p instanceof kd){var q=hd(f);return p.invoke.apply(p,[this.M].concat(ua(q)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(ud.supportedMethods.indexOf(e)>=
0){var r=hd(f);return ud[e].call.apply(ud[e],[d,this.M].concat(ua(r)))}}if(d instanceof kd||d instanceof Oa||d instanceof rd){if(d.has(e)){var t=d.get(e);if(t instanceof kd){var u=hd(f);return t.invoke.apply(t,[this.M].concat(ua(u)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof kd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof pd&&e==="toString")return d.toString();throw Ka(Error("TypeError: Object has no '"+
e+"' property."));}function Bd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.M;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Cd(){var a=ya.apply(0,arguments),b=Ia(this.M),c=La(b,a);if(c instanceof Ba)return c}function Dd(){return wd}function Ed(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Fd(){for(var a=this.M,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ha(a,c,d,!0)}}}function Gd(){return xd}function Hd(a,b){return new Ba(a,this.evaluate(b))}function Id(a,b){for(var c=ya.apply(2,arguments),d=new gd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.M.add(a,this.evaluate(g))}function Jd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Kd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof pd,f=d instanceof pd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Ld(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Md(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=La(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Nd(a,b,c){if(typeof b==="string")return Md(a,function(){return b.length},function(f){return f},c);if(b instanceof Oa||b instanceof rd||b instanceof gd||b instanceof kd){var d=b.Aa(),e=d.length;return Md(a,function(){return e},function(f){return d[f]},c)}}function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Nd(function(h){g.set(d,h);return g},e,f)}
function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Nd(function(h){var m=Ia(g);Ha(m,d,h,!0);return m},e,f)}function Qd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Nd(function(h){var m=Ia(g);m.add(d,h);return m},e,f)}function Rd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){g.set(d,h);return g},e,f)}
function Td(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Ia(g);Ha(m,d,h,!0);return m},e,f)}function Ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Ia(g);m.add(d,h);return m},e,f)}
function Sd(a,b,c){if(typeof b==="string")return Md(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof gd)return Md(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ka(Error("The value is not iterable."));}
function Vd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof gd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.M,h=this.evaluate(d),m=Ia(g);for(e(g,m);Ma(m,b);){var n=La(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=Ia(g);e(m,p);Ma(p,c);m=p}}
function Wd(a,b){var c=ya.apply(2,arguments),d=this.M,e=this.evaluate(b);if(!(e instanceof gd))throw Error("Error: non-List value given for Fn argument names.");return new kd(a,function(){return function(){var f=ya.apply(0,arguments),g=Ia(d);g.D===void 0&&(g.D=this.M.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new gd(h));var r=La(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function Xd(a){var b=this.evaluate(a),c=this.M;if(Yd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Zd(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ka(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Oa||d instanceof rd||d instanceof gd||d instanceof kd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:fd(e)&&(c=d[e]);else if(d instanceof pd)return;return c}function $d(a,b){return this.evaluate(a)>this.evaluate(b)}function ae(a,b){return this.evaluate(a)>=this.evaluate(b)}
function be(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof pd&&(c=c.getValue());d instanceof pd&&(d=d.getValue());return c===d}function ce(a,b){return!be.call(this,a,b)}function de(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=La(this.M,d);if(e instanceof Ba)return e}var Yd=!1;
function ee(a,b){return this.evaluate(a)<this.evaluate(b)}function fe(a,b){return this.evaluate(a)<=this.evaluate(b)}function ge(){for(var a=new gd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function he(){for(var a=new Oa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ie(a,b){return this.evaluate(a)%this.evaluate(b)}
function je(a,b){return this.evaluate(a)*this.evaluate(b)}function ke(a){return-this.evaluate(a)}function le(a){return!this.evaluate(a)}function me(a,b){return!Kd.call(this,a,b)}function ne(){return null}function oe(a,b){return this.evaluate(a)||this.evaluate(b)}function pe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function qe(a){return this.evaluate(a)}function re(){return ya.apply(0,arguments)}function se(a){return new Ba("return",this.evaluate(a))}
function te(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ka(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof kd||d instanceof gd||d instanceof Oa)&&d.set(String(e),f);return f}function ue(a,b){return this.evaluate(a)-this.evaluate(b)}
function ve(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function we(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function xe(a){var b=this.evaluate(a);return b instanceof kd?"function":typeof b}function ye(){for(var a=this.M,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function ze(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=La(this.M,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=La(this.M,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ae(a){return~Number(this.evaluate(a))}function Be(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ce(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function De(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Ee(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Fe(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ge(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function He(){}
function Ie(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Ja&&h.Yl))throw h;var e=Ia(this.M);a!==""&&(h instanceof Ja&&(h=h.vm),e.add(a,new pd(h)));var f=this.evaluate(c),g=La(e,f);if(g instanceof Ba)return g}}function Je(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ja&&f.Yl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Le=function(){this.D=new Na;Ke(this)};Le.prototype.execute=function(a){return this.D.Lj(a)};var Ke=function(a){var b=function(c,d){var e=new ld(String(c),d);e.eb();a.D.D.set(String(c),e)};b("map",he);b("and",Uc);b("contains",Xc);b("equals",Vc);b("or",Wc);b("startsWith",Yc);b("variable",Zc)};var Ne=function(){this.J=!1;this.D=new Na;Me(this);this.J=!0};Ne.prototype.execute=function(a){return Oe(this.D.Lj(a))};var Pe=function(a,b,c){return Oe(a.D.lo(b,c))};Ne.prototype.eb=function(){this.D.eb()};
var Me=function(a){var b=function(c,d){var e=String(c),f=new ld(e,d);f.eb();a.D.D.set(e,f)};b(0,yd);b(1,zd);b(2,Ad);b(3,Bd);b(56,Ee);b(57,Be);b(58,Ae);b(59,Ge);b(60,Ce);b(61,De);b(62,Fe);b(53,Cd);b(4,Dd);b(5,Ed);b(68,Ie);b(52,Fd);b(6,Gd);b(49,Hd);b(7,ge);b(8,he);b(9,Ed);b(50,Id);b(10,Jd);b(12,Kd);b(13,Ld);b(67,Je);b(51,Wd);b(47,Od);b(54,Pd);b(55,Qd);b(63,Vd);b(64,Rd);b(65,Td);b(66,Ud);b(15,Xd);b(16,Zd);b(17,Zd);b(18,$d);b(19,ae);b(20,be);b(21,ce);b(22,de);b(23,ee);b(24,fe);b(25,ie);b(26,je);b(27,
ke);b(28,le);b(29,me);b(45,ne);b(30,oe);b(32,pe);b(33,pe);b(34,qe);b(35,qe);b(46,re);b(36,se);b(43,te);b(37,ue);b(38,ve);b(39,we);b(40,xe);b(44,He);b(41,ye);b(42,ze)};Ne.prototype.te=function(){return this.D.te()};function Oe(a){if(a instanceof Ba||a instanceof kd||a instanceof gd||a instanceof Oa||a instanceof rd||a instanceof pd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Qe=function(a){this.message=a};function Re(a){a.Br=!0;return a};var Se=Re(function(a){return typeof a==="string"});function Te(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Qe("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Ue(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Ve=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function We(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Te(e)+c}a<<=2;d||(a|=32);return c=""+Te(a|b)+c}
function Xe(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+We(1,1)+Te(d<<2|e));var f=a.Xl,g=a.wr,h="4"+c+(f?""+We(2,1)+Te(f):"")+(g?""+We(12,1)+Te(g):""),m,n=a.Mj;m=n&&Ve.test(n)?""+We(3,2)+n:"";var p,q=a.Ij;p=q?""+We(4,1)+Te(q):"";var r;var t=a.ctid;if(t&&b){var u=We(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var x=v[1];r=""+u+Te(1+x.length)+(a.km||0)+x}}else r="";var z=a.sq,B=a.oe,C=a.Oa,F=a.Fr,G=h+m+p+r+(z?""+We(6,1)+Te(z):"")+(B?""+We(7,3)+Te(B.length)+
B:"")+(C?""+We(8,3)+Te(C.length)+C:"")+(F?""+We(9,3)+Te(F.length)+F:""),I;var K=a.Zl;K=K===void 0?{}:K;for(var U=[],Q=k(Object.keys(K)),na=Q.next();!na.done;na=Q.next()){var T=na.value;U[Number(T)]=K[T]}if(U.length){var aa=We(10,3),Y;if(U.length===0)Y=Te(0);else{for(var V=[],ka=0,ja=!1,la=0;la<U.length;la++){ja=!0;var Ra=la%6;U[la]&&(ka|=1<<Ra);Ra===5&&(V.push(Te(ka)),ka=0,ja=!1)}ja&&V.push(Te(ka));Y=V.join("")}var Xa=Y;I=""+aa+Te(Xa.length)+Xa}else I="";var Fa=a.wm;return G+I+(Fa?""+We(11,3)+Te(Fa.length)+
Fa:"")};var Ye=function(){function a(b){return{toString:function(){return b}}}return{Vm:a("consent"),bk:a("convert_case_to"),dk:a("convert_false_to"),ek:a("convert_null_to"),fk:a("convert_true_to"),gk:a("convert_undefined_to"),Gq:a("debug_mode_metadata"),Ha:a("function"),Di:a("instance_name"),oo:a("live_only"),po:a("malware_disabled"),METADATA:a("metadata"),so:a("original_activity_id"),Xq:a("original_vendor_template_id"),Wq:a("once_on_load"),ro:a("once_per_event"),zl:a("once_per_load"),Zq:a("priority_override"),
gr:a("respected_consent_types"),Il:a("setup_tags"),rh:a("tag_id"),Pl:a("teardown_tags")}}();var vf;var wf=[],xf=[],yf=[],zf=[],Af=[],Bf,Cf,Df;function Ef(a){Df=Df||a}
function Ff(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)wf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)zf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)yf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Gf(p[r])}xf.push(p)}}
function Gf(a){}var Hf,If=[],Jf=[];function Kf(a,b){var c={};c[Ye.Ha]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Lf(a,b,c){try{return Cf(Mf(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Nf(a){var b=a[Ye.Ha];if(!b)throw Error("Error: No function name given for function call.");return!!Bf[b]}
var Mf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Of(a[e],b,c));return d},Of=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Of(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=wf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Ye.Di]);try{var m=Mf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Pf(m,{event:b,index:f,type:2,
name:h});Hf&&(d=Hf.Mo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Of(a[n],b,c)]=Of(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Of(a[q],b,c);Df&&(p=p||Df.Np(r));d.push(r)}return Df&&p?Df.Ro(d):d.join("");case "escape":d=Of(a[1],b,c);if(Df&&Array.isArray(a[1])&&a[1][0]==="macro"&&Df.Op(a))return Df.bq(d);d=String(d);for(var t=2;t<a.length;t++)ff[a[t]]&&(d=ff[a[t]](d));return d;
case "tag":var u=a[1];if(!zf[u])throw Error("Unable to resolve tag reference "+u+".");return{hm:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Ye.Ha]=a[1];var w=Lf(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Pf=function(a,b){var c=a[Ye.Ha],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Bf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&If.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&zb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=wf[q];break;case 1:r=zf[q];break;default:n="";break a}var t=r&&r[Ye.Di];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Jf.indexOf(c)===-1){Jf.push(c);
var x=ub();u=e(g);var z=ub()-x,B=ub();v=vf(c,h,b);w=z-(ub()-B)}else if(e&&(u=e(g)),!e||f)v=vf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ed(u)?(Array.isArray(u)?Array.isArray(v):cd(u)?cd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Qf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Qf,Error);Qf.prototype.getMessage=function(){return this.message};function Rf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Rf(a[c],b[c])}};function Sf(){return function(a,b){var c;var d=Tf;a instanceof Ja?(a.D=d,c=a):c=new Ja(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Tf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)gb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Uf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Vf(a),f=0;f<xf.length;f++){var g=xf[f],h=Wf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<zf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Wf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Vf(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Lf(yf[c],a));return b[c]}};function Xf(a,b){b[Ye.bk]&&typeof a==="string"&&(a=b[Ye.bk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Ye.ek)&&a===null&&(a=b[Ye.ek]);b.hasOwnProperty(Ye.gk)&&a===void 0&&(a=b[Ye.gk]);b.hasOwnProperty(Ye.fk)&&a===!0&&(a=b[Ye.fk]);b.hasOwnProperty(Ye.dk)&&a===!1&&(a=b[Ye.dk]);return a};var Yf=function(){this.D={}},$f=function(a,b){var c=Zf.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function ag(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Qf(c,d,g);}}
function bg(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));ag(e,b,d,g);ag(f,b,d,g)}}}};var fg=function(){var a=data.permissions||{},b=cg.ctid,c=this;this.J={};this.D=new Yf;var d={},e={},f=bg(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});nb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw dg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};nb(h,function(p,q){var r=eg(p,q);n[p]=r.assert;d[p]||(d[p]=r.U);r.Vl&&!e[p]&&(e[p]=r.Vl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw dg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},gg=function(a){return Zf.J[a]||function(){}};
function eg(a,b){var c=Kf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=dg;try{return Pf(c)}catch(d){return{assert:function(e){throw new Qf(e,{},"Permission "+e+" is unknown.");},U:function(){throw new Qf(a,{},"Permission "+a+" is unknown.");}}}}function dg(a,b,c){return new Qf(a,b,c)};var hg=!1;var ig={};ig.Nm=qb('');ig.ap=qb('');function ng(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var og=[],pg={};function qg(a){return og[a]===void 0?!1:og[a]};var rg=[];function sg(a){switch(a){case 1:return 0;case 38:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 12;case 197:return 13;case 114:return 10;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function tg(a,b){rg[a]=b;var c=sg(a);c!==void 0&&(og[c]=b)}function D(a){tg(a,!0)}D(39);D(34);D(35);D(36);
D(56);D(145);D(153);D(144);D(120);
D(58);D(5);D(111);
D(139);D(87);D(92);D(117);
D(159);D(132);D(20);
D(72);D(113);D(154);
D(116);tg(23,!1),D(24);pg[1]=ng('1',6E4);pg[3]=ng('10',1);
pg[2]=ng('',50);D(29);ug(26,25);
D(9);D(91);
D(123);D(157);
D(158);D(71);D(136);D(127);D(27);D(69);D(135);
D(95);D(86);D(38);D(103);
D(112);D(63);D(152);
D(101);D(122);D(121);
D(108);D(134);D(115);D(96);
D(31);D(22);
D(97);D(19);D(12);
D(28);
D(90);
D(59);D(13);
D(163);D(167);
D(175);D(176);D(180);D(182);
D(185);D(187);
D(192);

function E(a){return!!rg[a]}
function ug(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?D(b):D(a)};var wg={},xg=(wg.uaa=!0,wg.uab=!0,wg.uafvl=!0,wg.uamb=!0,wg.uam=!0,wg.uap=!0,wg.uapv=!0,wg.uaw=!0,wg);
var Fg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Dg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Eg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?zb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Eg=/^[a-z$_][\w-$]*$/i,Dg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Gg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Hg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Ig(a,b){return String(a).split(",").indexOf(String(b))>=0}var Jg=new mb;function Kg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Jg.get(e);f||(f=new RegExp(b,d),Jg.set(e,f));return f.test(a)}catch(g){return!1}}function Lg(a,b){return String(a).indexOf(String(b))>=0}
function Mg(a,b){return String(a)===String(b)}function Ng(a,b){return Number(a)>=Number(b)}function Og(a,b){return Number(a)<=Number(b)}function Pg(a,b){return Number(a)>Number(b)}function Qg(a,b){return Number(a)<Number(b)}function Rg(a,b){return zb(String(a),String(b))};var Yg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Zg={Fn:"function",PixieMap:"Object",List:"Array"};
function $g(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Yg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof kd?n="Fn":m instanceof gd?n="List":m instanceof Oa?n="PixieMap":m instanceof rd?n="PixiePromise":m instanceof pd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Zg[n]||n)+", which does not match required type ")+
((Zg[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=k(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof kd?d.push("function"):g instanceof gd?d.push("Array"):g instanceof Oa?d.push("Object"):g instanceof rd?d.push("Promise"):g instanceof pd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ah(a){return a instanceof Oa}function bh(a){return ah(a)||a===null||ch(a)}
function dh(a){return a instanceof kd}function eh(a){return dh(a)||a===null||ch(a)}function fh(a){return a instanceof gd}function gh(a){return a instanceof pd}function hh(a){return typeof a==="string"}function ih(a){return hh(a)||a===null||ch(a)}function jh(a){return typeof a==="boolean"}function kh(a){return jh(a)||ch(a)}function lh(a){return jh(a)||a===null||ch(a)}function mh(a){return typeof a==="number"}function ch(a){return a===void 0};function nh(a){return""+a}
function oh(a,b){var c=[];return c};function ph(a,b){var c=new kd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ka(g);}});c.eb();return c}
function qh(a,b){var c=new Oa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];eb(e)?c.set(d,ph(a+"_"+d,e)):cd(e)?c.set(d,qh(a+"_"+d,e)):(gb(e)||fb(e)||typeof e==="boolean")&&c.set(d,e)}c.eb();return c};function rh(a,b){if(!hh(a))throw H(this.getName(),["string"],arguments);if(!ih(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Oa;return d=qh("AssertApiSubject",
c)};function sh(a,b){if(!ih(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof rd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Oa;return d=qh("AssertThatSubject",c)};function th(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.M,e=0;e<b.length;++e)c.push(sd(b[e],d));return td(a.apply(null,c))}}function uh(){for(var a=Math,b=vh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=th(a[e].bind(a)))}return c};function wh(a){return a!=null&&zb(a,"__cvt_")};function xh(a){var b;return b};function yh(a){var b;return b};function zh(a){try{return encodeURI(a)}catch(b){}};function Ah(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Bh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Ch=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Bh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Bh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Eh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Ch(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Dh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Dh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Eh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Kg(d(c[0]),d(c[1]),!1);case 5:return Mg(d(c[0]),d(c[1]));case 6:return Rg(d(c[0]),d(c[1]));case 7:return Hg(d(c[0]),d(c[1]));case 8:return Lg(d(c[0]),d(c[1]));case 9:return Qg(d(c[0]),d(c[1]));case 10:return Og(d(c[0]),d(c[1]));case 11:return Pg(d(c[0]),d(c[1]));case 12:return Ng(d(c[0]),d(c[1]));case 13:return Ig(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Fh(a){if(!ih(a))throw H(this.getName(),["string|undefined"],arguments);};function Gh(a,b){if(!mh(a)||!mh(b))throw H(this.getName(),["number","number"],arguments);return kb(a,b)};function Hh(){return(new Date).getTime()};function Ih(a){if(a===null)return"null";if(a instanceof gd)return"array";if(a instanceof kd)return"function";if(a instanceof pd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Jh(a){function b(c){return function(d){try{return c(d)}catch(e){(hg||ig.Nm)&&a.call(this,e.message)}}}return{parse:b(function(c){return td(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(sd(c))}),publicName:"JSON"}};function Kh(a){return pb(sd(a,this.M))};function Lh(a){return Number(sd(a,this.M))};function Mh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Nh(a,b,c){var d=null,e=!1;return e?d:null};var vh="floor ceil round max min abs pow sqrt".split(" ");function Oh(){var a={};return{pp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Hm:function(b,c){a[b]=c},reset:function(){a={}}}}function Ph(a,b){return function(){return kd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Qh(a,b){if(!hh(a))throw H(this.getName(),["string","any"],arguments);}
function Rh(a,b){if(!hh(a)||!ah(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Sh={};var Th=function(a){var b=new Oa;if(a instanceof gd)for(var c=a.Aa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof kd)for(var f=a.Aa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Sh.keys=function(a){$g(this.getName(),arguments);if(a instanceof gd||a instanceof kd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof rd)return new gd(a.Aa());return new gd};
Sh.values=function(a){$g(this.getName(),arguments);if(a instanceof gd||a instanceof kd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof rd)return new gd(a.yc());return new gd};
Sh.entries=function(a){$g(this.getName(),arguments);if(a instanceof gd||a instanceof kd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof rd)return new gd(a.Xb().map(function(b){return new gd(b)}));return new gd};
Sh.freeze=function(a){(a instanceof Oa||a instanceof rd||a instanceof gd||a instanceof kd)&&a.eb();return a};Sh.delete=function(a,b){if(a instanceof Oa&&!a.Rc())return a.remove(b),!0;return!1};function J(a,b){var c=ya.apply(2,arguments),d=a.M.D;if(!d)throw Error("Missing program state.");if(d.hq){try{d.Wl.apply(null,[b].concat(ua(c)))}catch(e){throw Ya("TAGGING",21),e;}return}d.Wl.apply(null,[b].concat(ua(c)))};var Uh=function(){this.J={};this.D={};this.O=!0;};Uh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Uh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Uh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:eb(b)?ph(a,b):qh(a,b)};function Vh(a,b){var c=void 0;return c};function Wh(){var a={};
return a};var L={m:{Na:"ad_personalization",V:"ad_storage",W:"ad_user_data",ja:"analytics_storage",bc:"region",ka:"consent_updated",wg:"wait_for_update",jn:"app_remove",kn:"app_store_refund",ln:"app_store_subscription_cancel",mn:"app_store_subscription_convert",nn:"app_store_subscription_renew",on:"consent_update",kk:"add_payment_info",lk:"add_shipping_info",Md:"add_to_cart",Nd:"remove_from_cart",mk:"view_cart",Wc:"begin_checkout",Od:"select_item",hc:"view_item_list",Fc:"select_promotion",jc:"view_promotion",
mb:"purchase",Pd:"refund",ub:"view_item",nk:"add_to_wishlist",pn:"exception",qn:"first_open",rn:"first_visit",ra:"gtag.config",Ab:"gtag.get",sn:"in_app_purchase",Xc:"page_view",tn:"screen_view",un:"session_start",vn:"source_update",wn:"timing_complete",xn:"track_social",Qd:"user_engagement",yn:"user_id_update",Fe:"gclid_link_decoration_source",Ge:"gclid_storage_source",kc:"gclgb",nb:"gclid",pk:"gclid_len",Rd:"gclgs",Sd:"gcllp",Td:"gclst",za:"ads_data_redaction",He:"gad_source",Ie:"gad_source_src",
Yc:"gclid_url",qk:"gclsrc",Je:"gbraid",Ud:"wbraid",Fa:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Ke:"allow_direct_google_requests",Dg:"allow_display_features",Eg:"allow_enhanced_conversions",Lb:"allow_google_signals",ob:"allow_interest_groups",zn:"app_id",An:"app_installer_id",Bn:"app_name",Cn:"app_version",Mb:"auid",Dn:"auto_detection_enabled",Zc:"aw_remarketing",Th:"aw_remarketing_only",Fg:"discount",Gg:"aw_feed_country",Hg:"aw_feed_language",wa:"items",Ig:"aw_merchant_id",rk:"aw_basket_type",
Le:"campaign_content",Me:"campaign_id",Ne:"campaign_medium",Oe:"campaign_name",Pe:"campaign",Qe:"campaign_source",Re:"campaign_term",Nb:"client_id",sk:"rnd",Uh:"consent_update_type",En:"content_group",Gn:"content_type",Ob:"conversion_cookie_prefix",Se:"conversion_id",Qa:"conversion_linker",Vh:"conversion_linker_disabled",bd:"conversion_api",Jg:"cookie_deprecation",pb:"cookie_domain",qb:"cookie_expires",wb:"cookie_flags",dd:"cookie_name",Pb:"cookie_path",ib:"cookie_prefix",Gc:"cookie_update",Vd:"country",
Va:"currency",Wh:"customer_buyer_stage",Te:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",Ue:"custom_map",Zh:"gcldc",ed:"dclid",tk:"debug_mode",qa:"developer_id",Hn:"disable_merchant_reported_purchases",fd:"dc_custom_params",In:"dc_natural_search",uk:"dynamic_event_settings",vk:"affiliation",Kg:"checkout_option",ai:"checkout_step",wk:"coupon",Ve:"item_list_name",bi:"list_name",Jn:"promotions",We:"shipping",di:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",Ng:"enhanced_conversions",
xk:"enhanced_conversions_automatic_settings",Og:"estimated_delivery_date",ei:"euid_logged_in_state",Xe:"event_callback",Kn:"event_category",Qb:"event_developer_id_string",Ln:"event_label",gd:"event",Pg:"event_settings",Qg:"event_timeout",Mn:"description",Nn:"fatal",On:"experiments",fi:"firebase_id",Wd:"first_party_collection",Rg:"_x_20",nc:"_x_19",yk:"fledge_drop_reason",zk:"fledge",Ak:"flight_error_code",Bk:"flight_error_message",Ck:"fl_activity_category",Dk:"fl_activity_group",gi:"fl_advertiser_id",
Ek:"fl_ar_dedupe",Ye:"match_id",Fk:"fl_random_number",Gk:"tran",Hk:"u",Sg:"gac_gclid",Xd:"gac_wbraid",Ik:"gac_wbraid_multiple_conversions",Jk:"ga_restrict_domain",hi:"ga_temp_client_id",Pn:"ga_temp_ecid",hd:"gdpr_applies",Kk:"geo_granularity",Hc:"value_callback",oc:"value_key",qc:"google_analysis_params",Yd:"_google_ng",Zd:"google_signals",Lk:"google_tld",Ze:"gpp_sid",af:"gpp_string",Tg:"groups",Mk:"gsa_experiment_id",bf:"gtag_event_feature_usage",Nk:"gtm_up",Ic:"iframe_state",cf:"ignore_referrer",
ii:"internal_traffic_results",Ok:"_is_fpm",Jc:"is_legacy_converted",Kc:"is_legacy_loaded",Ug:"is_passthrough",jd:"_lps",xb:"language",Vg:"legacy_developer_id_string",Ra:"linker",ae:"accept_incoming",rc:"decorate_forms",na:"domains",Lc:"url_position",df:"merchant_feed_label",ef:"merchant_feed_language",ff:"merchant_id",Pk:"method",Qn:"name",Qk:"navigation_type",hf:"new_customer",Wg:"non_interaction",Rn:"optimize_id",Rk:"page_hostname",jf:"page_path",Wa:"page_referrer",Bb:"page_title",Sk:"passengers",
Tk:"phone_conversion_callback",Sn:"phone_conversion_country_code",Uk:"phone_conversion_css_class",Tn:"phone_conversion_ids",Vk:"phone_conversion_number",Wk:"phone_conversion_options",Un:"_platinum_request_status",Vn:"_protected_audience_enabled",kf:"quantity",Xg:"redact_device_info",ji:"referral_exclusion_definition",Jq:"_request_start_time",Sb:"restricted_data_processing",Wn:"retoken",Xn:"sample_rate",ki:"screen_name",Mc:"screen_resolution",Xk:"_script_source",Yn:"search_term",rb:"send_page_view",
kd:"send_to",ld:"server_container_url",lf:"session_duration",Yg:"session_engaged",li:"session_engaged_time",sc:"session_id",Zg:"session_number",nf:"_shared_user_id",pf:"delivery_postal_code",Kq:"_tag_firing_delay",Lq:"_tag_firing_time",Mq:"temporary_client_id",mi:"_timezone",ni:"topmost_url",Zn:"tracking_id",oi:"traffic_type",Xa:"transaction_id",uc:"transport_url",Yk:"trip_type",nd:"update",Cb:"url_passthrough",Zk:"uptgs",qf:"_user_agent_architecture",rf:"_user_agent_bitness",tf:"_user_agent_full_version_list",
uf:"_user_agent_mobile",vf:"_user_agent_model",wf:"_user_agent_platform",xf:"_user_agent_platform_version",yf:"_user_agent_wow64",Ya:"user_data",ri:"user_data_auto_latency",si:"user_data_auto_meta",ui:"user_data_auto_multi",wi:"user_data_auto_selectors",xi:"user_data_auto_status",Tb:"user_data_mode",ah:"user_data_settings",Sa:"user_id",Ub:"user_properties",al:"_user_region",zf:"us_privacy_string",Ga:"value",bl:"wbraid_multiple_conversions",rd:"_fpm_parameters",Bi:"_host_name",ql:"_in_page_command",
rl:"_ip_override",vl:"_is_passthrough_cid",vc:"non_personalized_ads",Ni:"_sst_parameters",mc:"conversion_label",Ba:"page_location",Rb:"global_developer_id_string",md:"tc_privacy_string"}};var Xh={},Yh=(Xh[L.m.ka]="gcu",Xh[L.m.kc]="gclgb",Xh[L.m.nb]="gclaw",Xh[L.m.pk]="gclid_len",Xh[L.m.Rd]="gclgs",Xh[L.m.Sd]="gcllp",Xh[L.m.Td]="gclst",Xh[L.m.Mb]="auid",Xh[L.m.Fg]="dscnt",Xh[L.m.Gg]="fcntr",Xh[L.m.Hg]="flng",Xh[L.m.Ig]="mid",Xh[L.m.rk]="bttype",Xh[L.m.Nb]="gacid",Xh[L.m.mc]="label",Xh[L.m.bd]="capi",Xh[L.m.Jg]="pscdl",Xh[L.m.Va]="currency_code",Xh[L.m.Wh]="clobs",Xh[L.m.Te]="vdltv",Xh[L.m.Xh]="clolo",Xh[L.m.Yh]="clolb",Xh[L.m.tk]="_dbg",Xh[L.m.Og]="oedeld",Xh[L.m.Qb]="edid",Xh[L.m.yk]=
"fdr",Xh[L.m.zk]="fledge",Xh[L.m.Sg]="gac",Xh[L.m.Xd]="gacgb",Xh[L.m.Ik]="gacmcov",Xh[L.m.hd]="gdpr",Xh[L.m.Rb]="gdid",Xh[L.m.Yd]="_ng",Xh[L.m.Ze]="gpp_sid",Xh[L.m.af]="gpp",Xh[L.m.Mk]="gsaexp",Xh[L.m.bf]="_tu",Xh[L.m.Ic]="frm",Xh[L.m.Ug]="gtm_up",Xh[L.m.jd]="lps",Xh[L.m.Vg]="did",Xh[L.m.df]="fcntr",Xh[L.m.ef]="flng",Xh[L.m.ff]="mid",Xh[L.m.hf]=void 0,Xh[L.m.Bb]="tiba",Xh[L.m.Sb]="rdp",Xh[L.m.sc]="ecsid",Xh[L.m.nf]="ga_uid",Xh[L.m.pf]="delopc",Xh[L.m.md]="gdpr_consent",Xh[L.m.Xa]="oid",Xh[L.m.Zk]=
"uptgs",Xh[L.m.qf]="uaa",Xh[L.m.rf]="uab",Xh[L.m.tf]="uafvl",Xh[L.m.uf]="uamb",Xh[L.m.vf]="uam",Xh[L.m.wf]="uap",Xh[L.m.xf]="uapv",Xh[L.m.yf]="uaw",Xh[L.m.ri]="ec_lat",Xh[L.m.si]="ec_meta",Xh[L.m.ui]="ec_m",Xh[L.m.wi]="ec_sel",Xh[L.m.xi]="ec_s",Xh[L.m.Tb]="ec_mode",Xh[L.m.Sa]="userId",Xh[L.m.zf]="us_privacy",Xh[L.m.Ga]="value",Xh[L.m.bl]="mcov",Xh[L.m.Bi]="hn",Xh[L.m.ql]="gtm_ee",Xh[L.m.vc]="npa",Xh[L.m.Se]=null,Xh[L.m.Mc]=null,Xh[L.m.xb]=null,Xh[L.m.wa]=null,Xh[L.m.Ba]=null,Xh[L.m.Wa]=null,Xh[L.m.ni]=
null,Xh[L.m.rd]=null,Xh[L.m.Fe]=null,Xh[L.m.Ge]=null,Xh[L.m.qc]=null,Xh);function Zh(a,b){if(a){var c=a.split("x");c.length===2&&($h(b,"u_w",c[0]),$h(b,"u_h",c[1]))}}
function ai(a){var b=bi;b=b===void 0?ci:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(di(q.value)),r.push(di(q.quantity)),r.push(di(q.item_id)),r.push(di(q.start_date)),r.push(di(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ci(a){return ei(a.item_id,a.id,a.item_name)}function ei(){for(var a=k(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function fi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function $h(a,b,c){c===void 0||c===null||c===""&&!xg[b]||(a[b]=c)}function di(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var gi={},ii={kq:hi};function ji(a,b){var c=gi[b],d=c.Jm;if(!(gi[b].active||gi[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;ii.kq(a,b)}}function hi(a,b){var c=gi[b];if(!(kb(0,9999)<c.percent*2*100))return a;ki(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function ki(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=kb(0,1)===0,e=kb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var M={K:{Uj:"call_conversion",X:"conversion",ao:"floodlight",Bf:"ga_conversion",Ji:"landing_page",Ia:"page_view",oa:"remarketing",Ua:"user_data_lead",La:"user_data_web"}};
var li={},mi=Object.freeze((li[L.m.Fe]=1,li[L.m.Ge]=1,li[L.m.Fa]=1,li[L.m.Ke]=1,li[L.m.Eg]=1,li[L.m.ob]=1,li[L.m.Zc]=1,li[L.m.Th]=1,li[L.m.Fg]=1,li[L.m.Gg]=1,li[L.m.Hg]=1,li[L.m.wa]=1,li[L.m.Ig]=1,li[L.m.Ob]=1,li[L.m.Qa]=1,li[L.m.pb]=1,li[L.m.qb]=1,li[L.m.wb]=1,li[L.m.ib]=1,li[L.m.Va]=1,li[L.m.Wh]=1,li[L.m.Te]=1,li[L.m.Xh]=1,li[L.m.Yh]=1,li[L.m.qa]=1,li[L.m.Hn]=1,li[L.m.Ng]=1,li[L.m.Og]=1,li[L.m.fi]=1,li[L.m.Wd]=1,li[L.m.qc]=1,li[L.m.Jc]=1,li[L.m.Kc]=1,li[L.m.xb]=1,li[L.m.df]=1,li[L.m.ef]=1,li[L.m.ff]=
1,li[L.m.hf]=1,li[L.m.Ba]=1,li[L.m.Wa]=1,li[L.m.Tk]=1,li[L.m.Uk]=1,li[L.m.Vk]=1,li[L.m.Wk]=1,li[L.m.Sb]=1,li[L.m.rb]=1,li[L.m.kd]=1,li[L.m.ld]=1,li[L.m.pf]=1,li[L.m.Xa]=1,li[L.m.uc]=1,li[L.m.nd]=1,li[L.m.Cb]=1,li[L.m.Ya]=1,li[L.m.Sa]=1,li[L.m.Ga]=1,li));function ni(a){return oi?y.querySelectorAll(a):null}
function pi(a,b){if(!oi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!y.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var qi=!1;
if(y.querySelectorAll)try{var ri=y.querySelectorAll(":root");ri&&ri.length==1&&ri[0]==y.documentElement&&(qi=!0)}catch(a){}var oi=qi;function si(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function ti(){this.blockSize=-1};function ui(a,b){this.blockSize=-1;this.blockSize=64;this.O=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.J=0;this.D=[];this.ia=a;this.T=b;this.la=za.Int32Array?new Int32Array(64):Array(64);vi===void 0&&(za.Int32Array?vi=new Int32Array(wi):vi=wi);this.reset()}Aa(ui,ti);for(var xi=[],yi=0;yi<63;yi++)xi[yi]=0;var zi=[].concat(128,xi);
ui.prototype.reset=function(){this.R=this.J=0;var a;if(za.Int32Array)a=new Int32Array(this.T);else{var b=this.T,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.D=a};
var Ai=function(a){for(var b=a.O,c=a.la,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.D[0]|0,n=a.D[1]|0,p=a.D[2]|0,q=a.D[3]|0,r=a.D[4]|0,t=a.D[5]|0,u=a.D[6]|0,v=a.D[7]|0,w=0;w<64;w++){var x=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(vi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+x|0}a.D[0]=a.D[0]+m|0;a.D[1]=a.D[1]+n|0;a.D[2]=a.D[2]+p|0;a.D[3]=a.D[3]+q|0;a.D[4]=a.D[4]+r|0;a.D[5]=a.D[5]+t|0;a.D[6]=a.D[6]+u|0;a.D[7]=a.D[7]+v|0};
ui.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.J;if(typeof a==="string")for(;c<b;)this.O[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ai(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.O[d++]=g;d==this.blockSize&&(Ai(this),d=0)}else throw Error("message must be string or array");
}this.J=d;this.R+=b};ui.prototype.digest=function(){var a=[],b=this.R*8;this.J<56?this.update(zi,56-this.J):this.update(zi,this.blockSize-(this.J-56));for(var c=63;c>=56;c--)this.O[c]=b&255,b/=256;Ai(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.D[e]>>f&255;return a};
var wi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],vi;function Bi(){ui.call(this,8,Ci)}Aa(Bi,ui);var Ci=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Di=/^[0-9A-Fa-f]{64}$/;function Ei(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Fi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=l.crypto)==null?0:b.subtle){if(Di.test(a))return Promise.resolve(a);try{var c=Ei(a);return l.crypto.subtle.digest("SHA-256",c).then(function(d){return Gi(d,l)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Gi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Hi=[],Ii;function Ji(a){Ii?Ii(a):Hi.push(a)}function Ki(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Ji(a),b):c}function Li(a,b){if(!E(190))return b;var c=Mi(a,"");return c!==b?(Ji(a),b):c}function Mi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ni(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Ji(a),b)}function Oi(){Ii=Pi;for(var a=k(Hi),b=a.next();!b.done;b=a.next())Ii(b.value);Hi.length=0};var Qi={fn:'100',gn:'10',hn:'1000',fo:'US-CO',ho:'US-CO',Bo:Li(44,'101509157~102015665~103116026~103200004~103233427~103351869~103351871~104617979~104617981~104661466~104661468~104718208~104736445~104736447')},Ri={Wo:Number(Qi.fn)||0,Xo:Number(Qi.gn)||0,Zo:Number(Qi.hn)||0,up:Qi.fo.split("~"),vp:Qi.ho.split("~"),Dq:Qi.Bo};Object.assign({},Ri);function N(a){Ya("GTM",a)};
var Wi=function(a,b){var c=["tv.1"],d=Si(a);if(d)return c.push(d),{cb:!1,Nj:c.join("~"),rg:{}};var e={},f=0;var g=Ti(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).cb;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{cb:g,Nj:h,rg:m,Yo:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Ui():Vi()}:{cb:g,Nj:h,rg:m}},Yi=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Xi(a);return Ti(b,function(){}).cb},Ti=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=k(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=Zi[g.name];if(h){var m=$i(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{cb:d,nj:c}},$i=function(a){var b=aj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(bj.test(e)||
Di.test(e))}return d},aj=function(a){return cj.indexOf(a)!==-1},Vi=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BG904dy9WmhVIIN2dcA0nntGq1UgZbXnZdLdnazmhgnynSb4scGZ7898AB4JTEXhKpZyt/bI4c6pv3QFJCN8jcc\x3d\x22,\x22version\x22:0},\x22id\x22:\x22ab6723b9-2511-4f81-9cbd-e0d149a278cf\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BLpjiodJVqbXHjCX08Lld8KPCcDftXT1PLNwYFUZ4lewt6oPzk+zFS+SpTiA5B2AVGOEwVdDQjlCYjTdZbjyC8I\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a3673cdc-b970-4045-a499-51d8c4352595\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BPMyGNvjmGlpCP2STM1WnoBMxEofvl4D/0l1ue1ScTFUEMAHqLbVq+4pAyr7nbTE7dXqK/3mnLAseTs8HwmR/LY\x3d\x22,\x22version\x22:0},\x22id\x22:\x228a8cc618-03a2-4904-889f-e39aef13e0b1\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BNORn+FghIYcBSP8FBXiQP9YIqpHq19J+Ww2IutW21aKRqE9/NO/EzSP6w7wzBvfPLWqHoe5MaG7WF5AmcP2tLs\x3d\x22,\x22version\x22:0},\x22id\x22:\x226d842905-6792-4050-9c1d-6b8b58dae2db\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCbwodqwmiKhapTrvDCT/5oMNvQVr+cSmWFdVdydYAcUYqXw85kcCKqybq8MuinPMHgeHdVdwVKdc2w7boKPY0E\x3d\x22,\x22version\x22:0},\x22id\x22:\x225500c479-6e55-424a-b045-7a6d3f36b679\x22}]}'},fj=function(a){if(l.Promise){var b=void 0;return b}},kj=function(a,b,c,d,e){if(l.Promise)try{var f=Xi(a),g=gj(f,e).then(hj);return g}catch(p){}},mj=function(a){try{return hj(lj(Xi(a)))}catch(b){}},ej=function(a,b){var c=void 0;return c},hj=function(a){var b=a.Tc,c=a.time,d=["tv.1"],e=Si(b);if(e)return d.push(e),{Hb:encodeURIComponent(d.join("~")),nj:!1,cb:!1,time:c,mj:!0};var f=b.filter(function(n){return!$i(n)}),g=Ti(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.nj,m=g.cb;return{Hb:encodeURIComponent(d.join("~")),nj:h,cb:m,time:c,mj:!1}},Si=function(a){if(a.length===1&&a[0].name==="error_code")return Zi.error_code+
"."+a[0].value},jj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(Zi[d.name]&&d.value)return!0}return!1},Xi=function(a){function b(r,t,u,v){var w=nj(r);w!==""&&(Di.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(fb(u)||Array.isArray(u)){u=hb(r);for(var v=0;v<u.length;++v){var w=nj(u[v]),x=Di.test(w);t&&!x&&N(89);!t&&x&&N(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
oj[t];r[v]&&(r[t]&&N(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=hb(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){N(64);return r(t)}}var h=[];if(l.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",pj);e(a,"phone_number",qj);e(a,"first_name",g(rj));e(a,"last_name",g(rj));var m=a.home_address||{};e(m,"street",g(sj));e(m,"city",g(sj));e(m,"postal_code",g(tj));e(m,"region",
g(sj));e(m,"country",g(tj));for(var n=hb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",rj,p);f(q,"last_name",rj,p);f(q,"street",sj,p);f(q,"city",sj,p);f(q,"postal_code",tj,p);f(q,"region",sj,p);f(q,"country",tj,p)}return h},uj=function(a){var b=a?Xi(a):[];return hj({Tc:b})},vj=function(a){return a&&a!=null&&Object.keys(a).length>0&&l.Promise?Xi(a).some(function(b){return b.value&&aj(b.name)&&!Di.test(b.value)}):!1},nj=function(a){return a==null?"":fb(a)?sb(String(a)):"e0"},tj=function(a){return a.replace(wj,
"")},rj=function(a){return sj(a.replace(/\s/g,""))},sj=function(a){return sb(a.replace(xj,"").toLowerCase())},qj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return yj.test(a)?a:"e0"},pj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(zj.test(c))return c}return"e0"},lj=function(a){var b=Qc();try{a.forEach(function(e){if(e.value&&aj(e.name)){var f;var g=e.value,h=l;if(g===""||
g==="e0"||Di.test(g))f=g;else try{var m=new Bi;m.update(Ei(g));f=Gi(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Qc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},gj=function(a,b){if(!a.some(function(d){return d.value&&aj(d.name)}))return Promise.resolve({Tc:a});if(!l.Promise)return Promise.resolve({Tc:[]});var c=b?Qc():void 0;return Promise.all(a.map(function(d){return d.value&&aj(d.name)?Fi(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Tc:a};if(c!==void 0){var e=Qc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},xj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,zj=/^\S+@\S+\.\S+$/,yj=/^\+\d{10,15}$/,wj=/[.~]/g,bj=/^[0-9A-Za-z_-]{43}$/,Aj={},Zi=(Aj.email="em",Aj.phone_number="pn",Aj.first_name="fn",Aj.last_name="ln",Aj.street="sa",Aj.city="ct",Aj.region="rg",Aj.country="co",Aj.postal_code="pc",Aj.error_code="ec",Aj),Bj={},oj=(Bj.email="sha256_email_address",Bj.phone_number="sha256_phone_number",
Bj.first_name="sha256_first_name",Bj.last_name="sha256_last_name",Bj.street="sha256_street",Bj);var cj=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Cj={},Dj=(Cj[L.m.ob]=1,Cj[L.m.ld]=2,Cj[L.m.uc]=2,Cj[L.m.za]=3,Cj[L.m.Te]=4,Cj[L.m.Cg]=5,Cj[L.m.Gc]=6,Cj[L.m.ib]=6,Cj[L.m.pb]=6,Cj[L.m.dd]=6,Cj[L.m.Pb]=6,Cj[L.m.wb]=6,Cj[L.m.qb]=7,Cj[L.m.Sb]=9,Cj[L.m.Dg]=10,Cj[L.m.Lb]=11,Cj),Ej={},Fj=(Ej.unknown=13,Ej.standard=14,Ej.unique=15,Ej.per_session=16,Ej.transactions=17,Ej.items_sold=18,Ej);var $a=[];function Gj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=k(Object.keys(Dj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Dj[f],h=b;h=h===void 0?!1:h;Ya("GTAG_EVENT_FEATURE_CHANNEL",g);h&&($a[g]=!0)}}};var Hj=function(){this.D=new Set;this.J=new Set},Jj=function(a){var b=Ij.ia;a=a===void 0?[]:a;var c=[].concat(ua(b.D)).concat([].concat(ua(b.J))).concat(a);c.sort(function(d,e){return d-e});return c},Kj=function(){var a=[].concat(ua(Ij.ia.D));a.sort(function(b,c){return b-c});return a},Lj=function(){var a=Ij.ia,b=Ri.Dq;a.D=new Set;if(b!=="")for(var c=k(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var Mj={},Nj=Li(14,"56c0"),Oj=Ni(15,Number("2")),Pj=Li(19,"dataLayer");Li(20,"");Li(16,"ChEI8N65wgYQporytrzE0a3YARInABifjpUA3vudm/ANRDvNlYD2W5ZT3O60YpShiqvWkTun8q+HdB3aGgKZdQ\x3d\x3d");var Qj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Rj={__paused:1,__tg:1},Sj;for(Sj in Qj)Qj.hasOwnProperty(Sj)&&(Rj[Sj]=1);var Tj=Ki(11,qb("")),Uj=!1,Vj,Wj=!1;Wj=!0;
Vj=Wj;var Xj,Yj=!1;Xj=Yj;Mj.Bg=Li(3,"www.googletagmanager.com");var Zj=""+Mj.Bg+(Vj?"/gtag/js":"/gtm.js"),ak=null,bk=null,ck={},dk={};Mj.Wm=Ki(2,qb("true"));var ek="";Mj.Oi=ek;
var Ij=new function(){this.ia=new Hj;this.D=this.O=!1;this.J=0;this.Ca=this.Za=this.Eb=this.T="";this.la=this.R=!1};function fk(){var a;a=a===void 0?[]:a;return Jj(a).join("~")}function gk(){var a=Ij.T.length;return Ij.T[a-1]==="/"?Ij.T.substring(0,a-1):Ij.T}function hk(){return Ij.D?E(84)?Ij.J===0:Ij.J!==1:!1}function ik(a){for(var b={},c=k(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var jk=new mb,kk={},lk={},ok={name:Pj,set:function(a,b){dd(Bb(a,b),kk);mk()},get:function(a){return nk(a,2)},reset:function(){jk=new mb;kk={};mk()}};function nk(a,b){return b!=2?jk.get(a):pk(a)}function pk(a,b){var c=a.split(".");b=b||[];for(var d=kk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function qk(a,b){lk.hasOwnProperty(a)||(jk.set(a,b),dd(Bb(a,b),kk),mk())}
function rk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=nk(c,1);if(Array.isArray(d)||cd(d))d=dd(d,null);lk[c]=d}}function mk(a){nb(lk,function(b,c){jk.set(b,c);dd(Bb(b),kk);dd(Bb(b,c),kk);a&&delete lk[b]})}function sk(a,b){var c,d=(b===void 0?2:b)!==1?pk(a):jk.get(a);ad(d)==="array"||ad(d)==="object"?c=dd(d,null):c=d;return c};
var uk=function(a){for(var b=[],c=Object.keys(tk),d=0;d<c.length;d++){var e=c[d],f=tk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},vk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},wk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!zb(w,"#")&&!zb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(zb(m,"dataLayer."))f=nk(m.substring(10));
else{var n=m.split(".");f=l[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&oi)try{var q=ni(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Ec(q[r])||sb(q[r].value));f=f.length===1?f[0]:f}}catch(w){N(149)}if(E(60)){for(var t,u=0;u<g.length&&(t=nk(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=vk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},xk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=wk(c,"email",
a.email,b)||d;d=wk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=wk(g,"first_name",e[f].first_name,b)||d;d=wk(g,"last_name",e[f].last_name,b)||d;d=wk(g,"street",e[f].street,b)||d;d=wk(g,"city",e[f].city,b)||d;d=wk(g,"region",e[f].region,b)||d;d=wk(g,"country",e[f].country,b)||d;d=wk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},yk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&cd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=l.enhanced_conversion_data;d&&Ya("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return xk(a[L.m.xk])}},zk=function(a){return cd(a)?!!a.enable_code:!1},tk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Ak=function(){return kc.userAgent.toLowerCase().indexOf("firefox")!==-1},Bk=function(a){var b=a&&a[L.m.xk];return b&&!!b[L.m.Dn]},Ck=function(a){if(a)switch(a._tag_mode){case "CODE":return"c";case "AUTO":return"a";case "MANUAL":return"m";default:return"c"}};var Dk=/:[0-9]+$/,Ek=/^\d+\.fls\.doubleclick\.net$/;function Fk(a,b,c,d){var e=Gk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Gk(a,b,c){for(var d={},e=k(a.split("&")),f=e.next();!f.done;f=e.next()){var g=k(f.value.split("=")),h=g.next().value,m=ta(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Hk(a){try{return decodeURIComponent(a)}catch(b){}}function Ik(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Jk(a.protocol)||Jk(l.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:l.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||l.location.hostname).replace(Dk,"").toLowerCase());return Kk(a,b,c,d,e)}
function Kk(a,b,c,d,e){var f,g=Jk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Lk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Dk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Ya("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Fk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Jk(a){return a?a.replace(":","").toLowerCase():""}function Lk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Mk={},Nk=0;
function Ok(a){var b=Mk[a];if(!b){var c=y.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Ya("TAGGING",1),d="/"+d);var e=c.hostname.replace(Dk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Nk<5&&(Mk[a]=b,Nk++)}return b}function Pk(a,b,c){var d=Ok(a);return Gb(b,d,c)}
function Qk(a){var b=Ok(l.location.href),c=Ik(b,"host",!1);if(c&&c.match(Ek)){var d=Ik(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Rk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Sk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Tk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Ok(""+c+b).href}}function Uk(a,b){if(hk()||Ij.O)return Tk(a,b)}
function Vk(){return!!Mj.Oi&&Mj.Oi.split("@@").join("")!=="SGTM_TOKEN"}function Wk(a){for(var b=k([L.m.ld,L.m.uc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function Xk(a,b,c){c=c===void 0?"":c;if(!hk())return a;var d=b?Rk[a]||"":"";d==="/gs"&&(c="");return""+gk()+d+c}function Yk(a){if(!hk())return a;for(var b=k(Sk),c=b.next();!c.done;c=b.next())if(zb(a,""+gk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function Zk(a){var b=String(a[Ye.Ha]||"").replace(/_/g,"");return zb(b,"cvt")?"cvt":b}var $k=l.location.search.indexOf("?gtm_latency=")>=0||l.location.search.indexOf("&gtm_latency=")>=0;var al={iq:Ni(27,Number("0.005000")),Sm:"",Cq:"0.01",Uo:Ni(42,Number("0.010000"))};function bl(){var a=al.iq;return Number(a)}
var cl=Math.random(),dl=$k||cl<bl(),el,fl=bl()===1||(nc==null?void 0:nc.includes("gtm_debug=d"))||$k;el=E(163)?$k||cl>=1-Number(al.Uo):fl||cl>=1-Number(al.Cq);var gl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},hl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var il,jl;a:{for(var kl=["CLOSURE_FLAGS"],ll=za,ml=0;ml<kl.length;ml++)if(ll=ll[kl[ml]],ll==null){jl=null;break a}jl=ll}var nl=jl&&jl[610401301];il=nl!=null?nl:!1;function ol(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var pl,ql=za.navigator;pl=ql?ql.userAgentData||null:null;function rl(a){if(!il||!pl)return!1;for(var b=0;b<pl.brands.length;b++){var c=pl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function sl(a){return ol().indexOf(a)!=-1};function tl(){return il?!!pl&&pl.brands.length>0:!1}function ul(){return tl()?!1:sl("Opera")}function vl(){return sl("Firefox")||sl("FxiOS")}function wl(){return tl()?rl("Chromium"):(sl("Chrome")||sl("CriOS"))&&!(tl()?0:sl("Edge"))||sl("Silk")};var xl=function(a){xl[" "](a);return a};xl[" "]=function(){};var yl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function zl(){return il?!!pl&&!!pl.platform:!1}function Al(){return sl("iPhone")&&!sl("iPod")&&!sl("iPad")}function Bl(){Al()||sl("iPad")||sl("iPod")};ul();tl()||sl("Trident")||sl("MSIE");sl("Edge");!sl("Gecko")||ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")||sl("Trident")||sl("MSIE")||sl("Edge");ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")&&sl("Mobile");zl()||sl("Macintosh");zl()||sl("Windows");(zl()?pl.platform==="Linux":sl("Linux"))||zl()||sl("CrOS");zl()||sl("Android");Al();sl("iPad");sl("iPod");Bl();ol().toLowerCase().indexOf("kaios");var Cl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{xl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Dl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},El=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Fl=function(a){if(l.top==l)return 0;if(a===void 0?0:a){var b=l.location.ancestorOrigins;
if(b)return b[b.length-1]==l.location.origin?1:2}return Cl(l.top)?1:2},Gl=function(a){a=a===void 0?document:a;return a.createElement("img")},Hl=function(){for(var a=l,b=a;a&&a!=a.parent;)a=a.parent,Cl(a)&&(b=a);return b};function Il(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Jl(){return Il("join-ad-interest-group")&&eb(kc.joinAdInterestGroup)}
function Kl(a,b,c){var d=pg[3]===void 0?1:pg[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=y.querySelector(e);g&&(f=[g])}else f=Array.from(y.querySelectorAll(e))}catch(r){}var h;a:{try{h=y.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(pg[2]===void 0?50:pg[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&ub()-q<(pg[1]===void 0?6E4:pg[1])?(Ya("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ll(f[0]);else{if(n)return Ya("TAGGING",10),!1}else f.length>=d?Ll(f[0]):n&&Ll(m[0]);yc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:ub()});return!0}function Ll(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ml(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Nl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};vl();Al()||sl("iPod");sl("iPad");!sl("Android")||wl()||vl()||ul()||sl("Silk");wl();!sl("Safari")||wl()||(tl()?0:sl("Coast"))||ul()||(tl()?0:sl("Edge"))||(tl()?rl("Microsoft Edge"):sl("Edg/"))||(tl()?rl("Opera"):sl("OPR"))||vl()||sl("Silk")||sl("Android")||Bl();var Ol={},Pl=null,Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Pl){Pl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ol[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Pl[q]===void 0&&(Pl[q]=p)}}}for(var r=Ol[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],B=b[v+2],C=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|B>>6],I=r[B&63];t[w++]=""+C+F+G+I}var K=0,U=u;switch(b.length-v){case 2:K=b[v+1],U=r[(K&15)<<2]||u;case 1:var Q=b[v];t[w]=""+r[Q>>2]+r[(Q&3)<<4|K>>4]+U+u}return t.join("")};var Rl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Sl=/#|$/,Tl=function(a,b){var c=a.search(Sl),d=Rl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return yl(a.slice(d,e!==-1?e:0))},Ul=/[?&]($|#)/,Vl=function(a,b,c){for(var d,e=a.search(Sl),f=0,g,h=[];(g=Rl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Ul,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Wl(a,b,c,d,e,f){var g=Tl(c,"fmt");if(d){var h=Tl(c,"random"),m=Tl(c,"label")||"";if(!h)return!1;var n=Ql(yl(m)+":"+yl(h));if(!Ml(a,n,d))return!1}g&&Number(g)!==4&&(c=Vl(c,"rfmt",g));var p=Vl(c,"fmt",4);wc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Xl={},Yl=(Xl[1]={},Xl[2]={},Xl[3]={},Xl[4]={},Xl);function Zl(a,b,c){var d=$l(b,c);if(d){var e=Yl[b][d];e||(e=Yl[b][d]=[]);e.push(Object.assign({},a))}}function am(a,b){var c=$l(a,b);if(c){var d=Yl[a][c];d&&(Yl[a][c]=d.filter(function(e){return!e.Dm}))}}function bm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function $l(a,b){var c=b;if(b[0]==="/"){var d;c=((d=l.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function cm(a){var b=ya.apply(1,arguments);el&&(Zl(a,2,b[0]),Zl(a,3,b[0]));Jc.apply(null,ua(b))}function dm(a){var b=ya.apply(1,arguments);el&&Zl(a,2,b[0]);return Kc.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);el&&Zl(a,3,b[0]);zc.apply(null,ua(b))}
function fm(a){var b=ya.apply(1,arguments),c=b[0];el&&(Zl(a,2,c),Zl(a,3,c));return Mc.apply(null,ua(b))}function gm(a){var b=ya.apply(1,arguments);el&&Zl(a,1,b[0]);wc.apply(null,ua(b))}function hm(a){var b=ya.apply(1,arguments);b[0]&&el&&Zl(a,4,b[0]);yc.apply(null,ua(b))}function im(a){var b=ya.apply(1,arguments);el&&Zl(a,1,b[2]);return Wl.apply(null,ua(b))}function jm(a){var b=ya.apply(1,arguments);el&&Zl(a,4,b[0]);Kl.apply(null,ua(b))};var km=/gtag[.\/]js/,lm=/gtm[.\/]js/,mm=!1;function nm(a){if(mm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(km.test(c))return"3";if(lm.test(c))return"2"}return"0"};function om(a,b){var c=pm();c.pending||(c.pending=[]);jb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function qm(){var a=l.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=k(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var rm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=qm()};
function pm(){var a=oc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new rm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=qm());return c};var sm={},tm=!1,um=void 0,cg={ctid:Li(5,"AW-10837089199"),canonicalContainerId:Li(6,"121258338"),xm:Li(10,"AW-10837089199"),ym:Li(9,"AW-10837089199")};sm.he=Ki(7,qb(""));function vm(){return sm.he&&wm().some(function(a){return a===cg.ctid})}function xm(){var a=ym();return tm?a.map(zm):a}function Am(){var a=wm();return tm?a.map(zm):a}
function Bm(){var a=Am();if(!tm)for(var b=k([].concat(ua(a))),c=b.next();!c.done;c=b.next()){var d=zm(c.value),e=pm().destination[d];e&&e.state!==0||a.push(d)}return a}function Cm(){return Dm(cg.ctid)}function Em(){return Dm(cg.canonicalContainerId||"_"+cg.ctid)}function ym(){return cg.xm?cg.xm.split("|"):[cg.ctid]}function wm(){return cg.ym?cg.ym.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function Fm(){var a=Gm(Hm()),b=a&&a.parent;if(b)return Gm(b)}
function Gm(a){var b=pm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function Dm(a){return tm?zm(a):a}function zm(a){return"siloed_"+a}function Im(a){a=String(a);return zb(a,"siloed_")?a.substring(7):a}function Jm(){if(Ij.R){var a=pm();if(a.siloed){for(var b=[],c=ym().map(zm),d=wm().map(zm),e={},f=0;f<a.siloed.length;e={xh:void 0},f++)e.xh=a.siloed[f],!tm&&jb(e.xh.isDestination?d:c,function(g){return function(h){return h===g.xh.ctid}}(e))?tm=!0:b.push(e.xh);a.siloed=b}}}
function Km(){var a=pm();if(a.pending){for(var b,c=[],d=!1,e=xm(),f=um?um:Bm(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],jb(g.mg.target.isDestination?f:e,function(m){return function(n){return n===m.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(Em())}catch(m){}}}
function Lm(){var a=cg.ctid,b=xm(),c=Bm();um=c;for(var d=function(n,p){var q={canonicalContainerId:cg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};mc&&(q.scriptElement=mc);nc&&(q.scriptSource=nc);if(Fm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Ij.D,x=Ok(v),z=w?x.pathname:""+x.hostname+x.pathname,B=y.scripts,C="",F=0;F<B.length;++F){var G=B[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}C=String(F)}}if(C){t=C;break b}}t=void 0}var I=t;if(I){mm=!0;r=I;break a}}var K=[].slice.call(y.scripts);r=q.scriptElement?String(K.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=nm(q)}var U=p?e.destination:e.container,Q=U[n];Q?(p&&Q.state===0&&N(93),Object.assign(Q,q)):U[n]=q},e=pm(),f=k(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=k(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Em()]={};Km()}function Mm(){var a=Em();return!!pm().canonical[a]}function Nm(a){return!!pm().container[a]}function Om(a){var b=pm().destination[a];return!!b&&!!b.state}function Hm(){return{ctid:Cm(),isDestination:sm.he}}function Pm(a,b,c){b.siloed&&Qm({ctid:a,isDestination:!1});var d=Hm();pm().container[a]={state:1,context:b,parent:d};om({ctid:a,isDestination:!1},c)}
function Qm(a){var b=pm();(b.siloed=b.siloed||[]).push(a)}function Rm(){var a=pm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Sm(){var a={};nb(pm().destination,function(b,c){c.state===0&&(a[Im(b)]=c)});return a}function Tm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Um(){for(var a=pm(),b=k(xm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function Vm(a){var b=pm();return b.destination[a]?1:b.destination[zm(a)]?2:0};var Wm={Ka:{be:0,fe:1,Ki:2}};Wm.Ka[Wm.Ka.be]="FULL_TRANSMISSION";Wm.Ka[Wm.Ka.fe]="LIMITED_TRANSMISSION";Wm.Ka[Wm.Ka.Ki]="NO_TRANSMISSION";var Xm={Z:{Db:0,Ea:1,Ec:2,Nc:3}};Xm.Z[Xm.Z.Db]="NO_QUEUE";Xm.Z[Xm.Z.Ea]="ADS";Xm.Z[Xm.Z.Ec]="ANALYTICS";Xm.Z[Xm.Z.Nc]="MONITORING";function Ym(){var a=oc("google_tag_data",{});return a.ics=a.ics||new Zm}var Zm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
Zm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Ya("TAGGING",19);b==null?Ya("TAGGING",18):$m(this,a,b==="granted",c,d,e,f,g)};Zm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)$m(this,a[d],void 0,void 0,"","",b,c)};
var $m=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&fb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&l.setTimeout(function(){m[b]===t&&t.quiet&&(Ya("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};ba=Zm.prototype;ba.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=k(d),n=m.next();!n.done;n=m.next())an(this,n.value)}else if(b!==void 0&&h!==b)for(var p=k(d),q=p.next();!q.done;q=p.next())an(this,q.value)};
ba.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
ba.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&fb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
ba.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
ba.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};ba.addListener=function(a,b){this.D.push({consentTypes:a,se:b})};var an=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.zm=!0)}};Zm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.zm){d.zm=!1;try{d.se({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var bn=!1,cn=!1,dn={},en={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(dn.ad_storage=1,dn.analytics_storage=1,dn.ad_user_data=1,dn.ad_personalization=1,dn),usedContainerScopedDefaults:!1};function fn(a){var b=Ym();b.accessedAny=!0;return(fb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,en)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function gn(a){var b=Ym();b.accessedAny=!0;return b.getConsentState(a,en)}function hn(a){var b=Ym();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function jn(){if(!qg(8))return!1;var a=Ym();a.accessedAny=!0;if(a.active)return!0;if(!en.usedContainerScopedDefaults)return!1;for(var b=k(Object.keys(en.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(en.containerScopedDefaults[c.value]!==1)return!0;return!1}function kn(a,b){Ym().addListener(a,b)}
function ln(a,b){Ym().notifyListeners(a,b)}function mn(a,b){function c(){for(var e=0;e<b.length;e++)if(!hn(b[e]))return!0;return!1}if(c()){var d=!1;kn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function nn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];fn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=fb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),kn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):l.setTimeout(function(){m(c())},500)}}))};var on={},pn=(on[Xm.Z.Db]=Wm.Ka.be,on[Xm.Z.Ea]=Wm.Ka.be,on[Xm.Z.Ec]=Wm.Ka.be,on[Xm.Z.Nc]=Wm.Ka.be,on),qn=function(a,b){this.D=a;this.consentTypes=b};qn.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return fn(a)});case 1:return this.consentTypes.some(function(a){return fn(a)});default:bc(this.D,"consentsRequired had an unknown type")}};
var rn={},sn=(rn[Xm.Z.Db]=new qn(0,[]),rn[Xm.Z.Ea]=new qn(0,["ad_storage"]),rn[Xm.Z.Ec]=new qn(0,["analytics_storage"]),rn[Xm.Z.Nc]=new qn(1,["ad_storage","analytics_storage"]),rn);var un=function(a){var b=this;this.type=a;this.D=[];kn(sn[a].consentTypes,function(){tn(b)||b.flush()})};un.prototype.flush=function(){for(var a=k(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var tn=function(a){return pn[a.type]===Wm.Ka.Ki&&!sn[a.type].isConsentGranted()},vn=function(a,b){tn(a)?a.D.push(b):b()},wn=new Map;function xn(a){wn.has(a)||wn.set(a,new un(a));return wn.get(a)};var yn="https://"+Li(21,"www.googletagmanager.com"),zn="/td?id="+cg.ctid,An="v t pid dl tdp exp".split(" "),Bn=["mcc"],Cn={},Dn={},En=!1,Fn=void 0;function Gn(a,b,c){Dn[a]=b;(c===void 0||c)&&Hn(a)}function Hn(a,b){Cn[a]!==void 0&&(b===void 0||!b)||zb(cg.ctid,"GTM-")&&a==="mcc"||(Cn[a]=!0)}
function In(a){a=a===void 0?!1:a;var b=Object.keys(Cn).filter(function(c){return Cn[c]===!0&&Dn[c]!==void 0&&(a||!Bn.includes(c))}).map(function(c){var d=Dn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Xk(yn)+zn+(""+b+"&z=0")}function Jn(){Object.keys(Cn).forEach(function(a){An.indexOf(a)<0&&(Cn[a]=!1)})}
function Kn(a){a=a===void 0?!1:a;if(Ij.la&&el&&cg.ctid){var b=xn(Xm.Z.Nc);if(tn(b))En||(En=!0,vn(b,Kn));else{var c=In(a),d={destinationId:cg.ctid,endpoint:61};a?fm(d,c,void 0,{Hh:!0},void 0,function(){em(d,c+"&img=1")}):em(d,c);Jn();En=!1}}}var Ln={};function Mn(a){var b=String(a);Ln.hasOwnProperty(b)||(Ln[b]=!0,Gn("csp",Object.keys(Ln).join("~")),Hn("csp",!0),Fn===void 0&&E(171)&&(Fn=l.setTimeout(function(){var c=Cn.csp;Cn.csp=!0;var d=In(!1);Cn.csp=c;wc(d+"&script=1");Fn=void 0},500)))}
function Nn(){Object.keys(Cn).filter(function(a){return Cn[a]&&!An.includes(a)}).length>0&&Kn(!0)}var On=kb();function Pn(){On=kb()}function Qn(){Gn("v","3");Gn("t","t");Gn("pid",function(){return String(On)});Gn("exp",fk());Bc(l,"pagehide",Nn);l.setInterval(Pn,864E5)};var Rn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Sn=[L.m.ld,L.m.uc,L.m.Wd,L.m.Nb,L.m.sc,L.m.Sa,L.m.Ra,L.m.ib,L.m.pb,L.m.Pb],Tn=!1,Un=!1,Vn={},Wn={};function Xn(){!Un&&Tn&&(Rn.some(function(a){return en.containerScopedDefaults[a]!==1})||Yn("mbc"));Un=!0}function Yn(a){el&&(Gn(a,"1"),Kn())}function Zn(a,b){if(!Vn[b]&&(Vn[b]=!0,Wn[b]))for(var c=k(Sn),d=c.next();!d.done;d=c.next())if(O(a,d.value)){Yn("erc");break}};function $n(a){Ya("HEALTH",a)};var ao={aa:{Tm:"aw_user_data_cache",Ph:"cookie_deprecation_label",bo:"fl_user_data_cache",eo:"ga4_user_data_cache",Cf:"ip_geo_data_cache",Ei:"ip_geo_fetch_in_progress",yl:"nb_data",vo:"page_experiment_ids",Lf:"pt_data",Al:"pt_listener_set",Hl:"service_worker_endpoint",Jl:"shared_user_id",Kl:"shared_user_id_requested",ph:"shared_user_id_source"}};var bo=function(a){return Re(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(ao.aa);
function co(a,b){b=b===void 0?!1:b;if(bo(a)){var c,d,e=(d=(c=oc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=k(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function eo(a,b){var c=co(a,!0);c&&c.set(b)}function fo(a){var b;return(b=co(a))==null?void 0:b.get()}function go(a){var b={},c=co(a);if(!c){c=co(a,!0);if(!c)return;c.set(b)}return c.get()}function ho(a,b){if(typeof b==="function"){var c;return(c=co(a,!0))==null?void 0:c.subscribe(b)}}function io(a,b){var c=co(a);return c?c.unsubscribe(b):!1};var jo={op:Li(22,"eyIwIjoiVVMiLCIxIjoiVVMtTUEiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9")},ko={},lo=!1;function mo(){function a(){c!==void 0&&io(ao.aa.Cf,c);try{var e=fo(ao.aa.Cf);ko=JSON.parse(e)}catch(f){N(123),$n(2),ko={}}lo=!0;b()}var b=no,c=void 0,d=fo(ao.aa.Cf);d?a(d):(c=ho(ao.aa.Cf,a),oo())}
function oo(){function a(c){eo(ao.aa.Cf,c||"{}");eo(ao.aa.Ei,!1)}if(!fo(ao.aa.Ei)){eo(ao.aa.Ei,!0);var b="";try{l.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function po(){var a=jo.op;try{return JSON.parse(Va(a))}catch(b){return N(123),$n(2),{}}}function qo(){return ko["0"]||""}function ro(){return ko["1"]||""}function so(){var a=!1;return a}function to(){return ko["6"]!==!1}function uo(){var a="";return a}
function vo(){var a=!1;a=!!ko["5"];return a}function wo(){var a="";return a};var xo={},yo=Object.freeze((xo[L.m.Fa]=1,xo[L.m.Dg]=1,xo[L.m.Eg]=1,xo[L.m.Lb]=1,xo[L.m.wa]=1,xo[L.m.pb]=1,xo[L.m.qb]=1,xo[L.m.wb]=1,xo[L.m.dd]=1,xo[L.m.Pb]=1,xo[L.m.ib]=1,xo[L.m.Gc]=1,xo[L.m.Ue]=1,xo[L.m.qa]=1,xo[L.m.uk]=1,xo[L.m.Xe]=1,xo[L.m.Pg]=1,xo[L.m.Qg]=1,xo[L.m.Wd]=1,xo[L.m.Jk]=1,xo[L.m.qc]=1,xo[L.m.Zd]=1,xo[L.m.Lk]=1,xo[L.m.Tg]=1,xo[L.m.ii]=1,xo[L.m.Jc]=1,xo[L.m.Kc]=1,xo[L.m.Ra]=1,xo[L.m.ji]=1,xo[L.m.Sb]=1,xo[L.m.rb]=1,xo[L.m.kd]=1,xo[L.m.ld]=1,xo[L.m.lf]=1,xo[L.m.li]=1,xo[L.m.pf]=1,xo[L.m.uc]=
1,xo[L.m.nd]=1,xo[L.m.ah]=1,xo[L.m.Ub]=1,xo[L.m.rd]=1,xo[L.m.Ni]=1,xo));Object.freeze([L.m.Ba,L.m.Wa,L.m.Bb,L.m.xb,L.m.ki,L.m.Sa,L.m.fi,L.m.En]);
var zo={},Ao=Object.freeze((zo[L.m.jn]=1,zo[L.m.kn]=1,zo[L.m.ln]=1,zo[L.m.mn]=1,zo[L.m.nn]=1,zo[L.m.qn]=1,zo[L.m.rn]=1,zo[L.m.sn]=1,zo[L.m.un]=1,zo[L.m.Qd]=1,zo)),Bo={},Co=Object.freeze((Bo[L.m.kk]=1,Bo[L.m.lk]=1,Bo[L.m.Md]=1,Bo[L.m.Nd]=1,Bo[L.m.mk]=1,Bo[L.m.Wc]=1,Bo[L.m.Od]=1,Bo[L.m.hc]=1,Bo[L.m.Fc]=1,Bo[L.m.jc]=1,Bo[L.m.mb]=1,Bo[L.m.Pd]=1,Bo[L.m.ub]=1,Bo[L.m.nk]=1,Bo)),Do=Object.freeze([L.m.Fa,L.m.Ke,L.m.Lb,L.m.Gc,L.m.Wd,L.m.cf,L.m.rb,L.m.nd]),Eo=Object.freeze([].concat(ua(Do))),Fo=Object.freeze([L.m.qb,
L.m.Qg,L.m.lf,L.m.li,L.m.Lg]),Go=Object.freeze([].concat(ua(Fo))),Ho={},Io=(Ho[L.m.V]="1",Ho[L.m.ja]="2",Ho[L.m.W]="3",Ho[L.m.Na]="4",Ho),Jo={},Ko=Object.freeze((Jo.search="s",Jo.youtube="y",Jo.playstore="p",Jo.shopping="h",Jo.ads="a",Jo.maps="m",Jo));function Lo(a){return typeof a!=="object"||a===null?{}:a}function Mo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function No(a){if(a!==void 0&&a!==null)return Mo(a)}function Oo(a){return typeof a==="number"?a:No(a)};function Po(a){return a&&a.indexOf("pending:")===0?Qo(a.substr(8)):!1}function Qo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=ub();return b<c+3E5&&b>c-9E5};var Ro=!1,So=!1,To=!1,Uo=0,Vo=!1,Wo=[];function Xo(a){if(Uo===0)Vo&&Wo&&(Wo.length>=100&&Wo.shift(),Wo.push(a));else if(Yo()){var b=Li(41,'google.tagmanager.ta.prodqueue'),c=oc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Zo(){$o();Cc(y,"TAProdDebugSignal",Zo)}function $o(){if(!So){So=!0;ap();var a=Wo;Wo=void 0;a==null||a.forEach(function(b){Xo(b)})}}
function ap(){var a=y.documentElement.getAttribute("data-tag-assistant-prod-present");Qo(a)?Uo=1:!Po(a)||Ro||To?Uo=2:(To=!0,Bc(y,"TAProdDebugSignal",Zo,!1),l.setTimeout(function(){$o();Ro=!0},200))}function Yo(){if(!Vo)return!1;switch(Uo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var bp=!1;function cp(a,b){var c=ym(),d=wm();if(Yo()){var e=dp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Xo(e)}}
function ep(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.ab;e=a.isBatched;var f;if(f=Yo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=dp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Xo(h)}}function fp(a){Yo()&&ep(a())}
function dp(a,b){b=b===void 0?{}:b;b.groupId=gp;var c,d=b,e={publicId:hp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=bp?"OGT":"GTM";c.key.targetRef=ip;return c}var hp="",ip={ctid:"",isDestination:!1},gp;
function jp(a){var b=cg.ctid,c=vm();Uo=0;Vo=!0;ap();gp=a;hp=b;bp=Vj;ip={ctid:b,isDestination:c}};var kp=[L.m.V,L.m.ja,L.m.W,L.m.Na],lp,mp;function np(a){var b=a[L.m.bc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)nb(a,function(d){return function(e,f){if(e!==L.m.bc){var g=Mo(f),h=b[d.cg],m=qo(),n=ro();cn=!0;bn&&Ya("TAGGING",20);Ym().declare(e,g,h,m,n)}}}(c))}
function op(a){Xn();!mp&&lp&&Yn("crc");mp=!0;var b=a[L.m.wg];b&&N(41);var c=a[L.m.bc];c?N(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)nb(a,function(e){return function(f,g){if(f!==L.m.bc&&f!==L.m.wg){var h=No(g),m=c[e.dg],n=Number(b),p=qo(),q=ro();n=n===void 0?0:n;bn=!0;cn&&Ya("TAGGING",20);Ym().default(f,h,m,p,q,n,en)}}}(d))}
function pp(a){en.usedContainerScopedDefaults=!0;var b=a[L.m.bc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ro())&&!c.includes(qo()))return}nb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}en.usedContainerScopedDefaults=!0;en.containerScopedDefaults[d]=e==="granted"?3:2})}
function qp(a,b){Xn();lp=!0;nb(a,function(c,d){var e=Mo(d);bn=!0;cn&&Ya("TAGGING",20);Ym().update(c,e,en)});ln(b.eventId,b.priorityId)}function rp(a){a.hasOwnProperty("all")&&(en.selectedAllCorePlatformServices=!0,nb(Ko,function(b){en.corePlatformServices[b]=a.all==="granted";en.usedCorePlatformServices=!0}));nb(a,function(b,c){b!=="all"&&(en.corePlatformServices[b]=c==="granted",en.usedCorePlatformServices=!0)})}function sp(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return fn(b)})}
function tp(a,b){kn(a,b)}function up(a,b){nn(a,b)}function vp(a,b){mn(a,b)}function wp(){var a=[L.m.V,L.m.Na,L.m.W];Ym().waitForUpdate(a,500,en)}function xp(a){for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;Ym().clearTimeout(d,void 0,en)}ln()}function yp(){if(!Xj)for(var a=to()?ik(Ij.Za):ik(Ij.Eb),b=0;b<kp.length;b++){var c=kp[b],d=c,e=a[c]?"granted":"denied";Ym().implicit(d,e)}};var zp=!1,Ap=[];function Bp(){if(!zp){zp=!0;for(var a=Ap.length-1;a>=0;a--)Ap[a]();Ap=[]}};var Cp=l.google_tag_manager=l.google_tag_manager||{};function Dp(a,b){return Cp[a]=Cp[a]||b()}function Ep(){var a=Cm(),b=Fp;Cp[a]=Cp[a]||b}function Hp(){var a=Cp.sequence||1;Cp.sequence=a+1;return a};function Ip(){if(Cp.pscdl!==void 0)fo(ao.aa.Ph)===void 0&&eo(ao.aa.Ph,Cp.pscdl);else{var a=function(c){Cp.pscdl=c;eo(ao.aa.Ph,c)},b=function(){a("error")};try{kc.cookieDeprecationLabel?(a("pending"),kc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Jp=0;function Kp(a){el&&a===void 0&&Jp===0&&(Gn("mcc","1"),Jp=1)};var Lp={Af:{Xm:"cd",Ym:"ce",Zm:"cf",bn:"cpf",dn:"cu"}};var Mp=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Np=/\s/;
function Op(a,b){if(fb(a)){a=sb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Mp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Np.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Pp(a,b){for(var c={},d=0;d<a.length;++d){var e=Op(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Qp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=k(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Rp={},Qp=(Rp[0]=0,Rp[1]=1,Rp[2]=2,Rp[3]=0,Rp[4]=1,Rp[5]=0,Rp[6]=0,Rp[7]=0,Rp);var Sp=Number('')||500,Tp={},Up={},Vp={initialized:11,complete:12,interactive:13},Wp={},Xp=Object.freeze((Wp[L.m.rb]=!0,Wp)),Yp=void 0;function Zp(a,b){if(b.length&&el){var c;(c=Tp)[a]!=null||(c[a]=[]);Up[a]!=null||(Up[a]=[]);var d=b.filter(function(e){return!Up[a].includes(e)});Tp[a].push.apply(Tp[a],ua(d));Up[a].push.apply(Up[a],ua(d));!Yp&&d.length>0&&(Hn("tdc",!0),Yp=l.setTimeout(function(){Kn();Tp={};Yp=void 0},Sp))}}
function $p(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function aq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;ad(t)==="object"?u=t[r]:ad(t)==="array"&&(u=t[r]);return u===void 0?Xp[r]:u},f=$p(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=ad(m)==="object"||ad(m)==="array",q=ad(n)==="object"||ad(n)==="array";if(p&&q)aq(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function bq(){Gn("tdc",function(){Yp&&(l.clearTimeout(Yp),Yp=void 0);var a=[],b;for(b in Tp)Tp.hasOwnProperty(b)&&a.push(b+"*"+Tp[b].join("."));return a.length?a.join("!"):void 0},!1)};var cq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.R=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},dq=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.R)}return c},O=function(a,b,c,d){for(var e=k(dq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},eq=function(a){for(var b={},c=dq(a,4),d=k(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=k(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
cq.prototype.getMergedValues=function(a,b,c){function d(n){cd(n)&&nb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=dq(this,b);g.reverse();for(var h=k(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var fq=function(a){for(var b=[L.m.Pe,L.m.Le,L.m.Me,L.m.Ne,L.m.Oe,L.m.Qe,L.m.Re],c=dq(a,3),d=k(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=k(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},gq=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.ia={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},hq=function(a,
b){a.J=b;return a},iq=function(a,b){a.T=b;return a},jq=function(a,b){a.D=b;return a},kq=function(a,b){a.O=b;return a},lq=function(a,b){a.ia=b;return a},mq=function(a,b){a.R=b;return a},nq=function(a,b){a.eventMetadata=b||{};return a},oq=function(a,b){a.onSuccess=b;return a},pq=function(a,b){a.onFailure=b;return a},qq=function(a,b){a.isGtmEvent=b;return a},rq=function(a){return new cq(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={C:{Rj:"accept_by_default",vg:"add_tag_timing",Lh:"allow_ad_personalization",Tj:"batch_on_navigation",Vj:"client_id_source",Be:"consent_event_id",Ce:"consent_priority_id",Fq:"consent_state",ka:"consent_updated",Vc:"conversion_linker_enabled",ya:"cookie_options",yg:"create_dc_join",zg:"create_fpm_geo_join",Ag:"create_fpm_signals_join",Ld:"create_google_join",Ee:"em_event",Iq:"endpoint_for_debug",jk:"enhanced_client_id_source",Sh:"enhanced_match_result",od:"euid_mode_enabled",jb:"event_start_timestamp_ms",
kl:"event_usage",eh:"extra_tag_experiment_ids",Pq:"add_parameter",zi:"attribution_reporting_experiment",Ai:"counting_method",fh:"send_as_iframe",Qq:"parameter_order",gh:"parsed_target",co:"ga4_collection_subdomain",ol:"gbraid_cookie_marked",fa:"hit_type",sd:"hit_type_override",jo:"is_config_command",Df:"is_consent_update",Ef:"is_conversion",sl:"is_ecommerce",ud:"is_external_event",Fi:"is_fallback_aw_conversion_ping_allowed",Ff:"is_first_visit",tl:"is_first_visit_conversion",hh:"is_fl_fallback_conversion_flow_allowed",
ce:"is_fpm_encryption",ih:"is_fpm_split",de:"is_gcp_conversion",Gi:"is_google_signals_allowed",vd:"is_merchant_center",jh:"is_new_to_site",kh:"is_server_side_destination",ee:"is_session_start",wl:"is_session_start_conversion",Tq:"is_sgtm_ga_ads_conversion_study_control_group",Uq:"is_sgtm_prehit",xl:"is_sgtm_service_worker",Hi:"is_split_conversion",ko:"is_syn",Gf:"join_id",Ii:"join_elapsed",Hf:"join_timer_sec",ie:"tunnel_updated",Yq:"prehit_for_retry",ar:"promises",er:"record_aw_latency",wc:"redact_ads_data",
je:"redact_click_ids",wo:"remarketing_only",Fl:"send_ccm_parallel_ping",oh:"send_fledge_experiment",hr:"send_ccm_parallel_test_ping",Mf:"send_to_destinations",Mi:"send_to_targets",Gl:"send_user_data_hit",kb:"source_canonical_id",Ja:"speculative",Ll:"speculative_in_message",Ml:"suppress_script_load",Nl:"syn_or_mod",Ql:"transient_ecsid",Nf:"transmission_type",Ta:"user_data",kr:"user_data_from_automatic",lr:"user_data_from_automatic_getter",me:"user_data_from_code",sh:"user_data_from_manual",Sl:"user_data_mode",
Of:"user_id_updated"}};var sq={Rm:Number("5"),Hr:Number("")},tq=[],uq=!1;function vq(a){tq.push(a)}var wq="?id="+cg.ctid,xq=void 0,yq={},zq=void 0,Aq=new function(){var a=5;sq.Rm>0&&(a=sq.Rm);this.J=a;this.D=0;this.O=[]},Bq=1E3;
function Cq(a,b){var c=xq;if(c===void 0)if(b)c=Hp();else return"";for(var d=[Xk("https://www.googletagmanager.com"),"/a",wq],e=k(tq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Kd:!!a}),m=k(h),n=m.next();!n.done;n=m.next()){var p=k(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Dq(){if(Ij.la&&(zq&&(l.clearTimeout(zq),zq=void 0),xq!==void 0&&Eq)){var a=xn(Xm.Z.Nc);if(tn(a))uq||(uq=!0,vn(a,Dq));else{var b;if(!(b=yq[xq])){var c=Aq;b=c.D<c.J?!1:ub()-c.O[c.D%c.J]<1E3}if(b||Bq--<=0)N(1),yq[xq]=!0;else{var d=Aq,e=d.D++%d.J;d.O[e]=ub();var f=Cq(!0);em({destinationId:cg.ctid,endpoint:56,eventId:xq},f);uq=Eq=!1}}}}function Fq(){if(dl&&Ij.la){var a=Cq(!0,!0);em({destinationId:cg.ctid,endpoint:56,eventId:xq},a)}}var Eq=!1;
function Gq(a){yq[a]||(a!==xq&&(Dq(),xq=a),Eq=!0,zq||(zq=l.setTimeout(Dq,500)),Cq().length>=2022&&Dq())}var Hq=kb();function Iq(){Hq=kb()}function Jq(){return[["v","3"],["t","t"],["pid",String(Hq)]]};var Kq={};function Lq(a,b,c){dl&&a!==void 0&&(Kq[a]=Kq[a]||[],Kq[a].push(c+b),Gq(a))}function Mq(a){var b=a.eventId,c=a.Kd,d=[],e=Kq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Kq[b];return d};function Nq(a,b,c,d){var e=Op(Dm(a),!0);e&&Oq.register(e,b,c,d)}function Pq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&(Uj&&(d.deferrable=!0),Oq.push("event",[b,a],e,d))}function Qq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&Oq.push("get",[a,b],e,d)}function Rq(a){var b=Op(Dm(a),!0),c;b?c=Sq(Oq,b).D:c={};return c}function Tq(a,b){var c=Op(Dm(a),!0);c&&Uq(Oq,c,b)}
var Vq=function(){this.T={};this.D={};this.J={};this.ia=null;this.R={};this.O=!1;this.status=1},Wq=function(a,b,c,d){this.J=ub();this.D=b;this.args=c;this.messageContext=d;this.type=a},Xq=function(){this.destinations={};this.D={};this.commands=[]},Sq=function(a,b){var c=b.destinationId;tm||(c=Im(c));return a.destinations[c]=a.destinations[c]||new Vq},Yq=function(a,b,c,d){if(d.D){var e=Sq(a,d.D),f=e.ia;if(f){var g=d.D.id;tm||(g=Im(g));var h=dd(c,null),m=dd(e.T[g],null),n=dd(e.R,null),p=dd(e.D,null),
q=dd(a.D,null),r={};if(dl)try{r=dd(kk,null)}catch(x){N(72)}var t=d.D.prefix,u=function(x){Lq(d.messageContext.eventId,t,x)},v=rq(qq(pq(oq(nq(lq(kq(mq(jq(iq(hq(new gq(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Lq(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(el&&x==="config"){var B,C=(B=Op(z))==null?void 0:B.ids;if(!(C&&C.length>1)){var F,G=oc("google_tag_data",{});G.td||(G.td={});F=G.td;var I=dd(v.R);dd(v.D,I);var K=[],U;for(U in F)F.hasOwnProperty(U)&&aq(F[U],I).length&&K.push(U);K.length&&(Zp(z,K),Ya("TAGGING",Vp[y.readyState]||14));F[z]=I}}f(d.D.id,b,d.J,v)}catch(Q){Lq(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():vn(e.la,w)}}};
Xq.prototype.register=function(a,b,c,d){var e=Sq(this,a);e.status!==3&&(e.ia=b,e.status=3,e.la=xn(c),Uq(this,a,d||{}),this.flush())};
Xq.prototype.push=function(a,b,c,d){c!==void 0&&(Sq(this,c).status===1&&(Sq(this,c).status=2,this.push("require",[{}],c,{})),Sq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.C.Mf]||(d.eventMetadata[P.C.Mf]=[c.destinationId]),d.eventMetadata[P.C.Mi]||(d.eventMetadata[P.C.Mi]=[c.id]));this.commands.push(new Wq(a,c,b,d));d.deferrable||this.flush()};
Xq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Pc:void 0,yh:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||Sq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Sq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];nb(h,function(t,u){dd(Bb(t,u),b.D)});Gj(h,!0);break;case "config":var m=Sq(this,g);
e.Pc={};nb(f.args[0],function(t){return function(u,v){dd(Bb(u,v),t.Pc)}}(e));var n=!!e.Pc[L.m.nd];delete e.Pc[L.m.nd];var p=g.destinationId===g.id;Gj(e.Pc,!0);n||(p?m.R={}:m.T[g.id]={});m.O&&n||Yq(this,L.m.ra,e.Pc,f);m.O=!0;p?dd(e.Pc,m.R):(dd(e.Pc,m.T[g.id]),N(70));d=!0;break;case "event":e.yh={};nb(f.args[0],function(t){return function(u,v){dd(Bb(u,v),t.yh)}}(e));Gj(e.yh);Yq(this,f.args[1],e.yh,f);break;case "get":var q={},r=(q[L.m.oc]=f.args[0],q[L.m.Hc]=f.args[1],q);Yq(this,L.m.Ab,r,f)}this.commands.shift();
Zq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Zq=function(a,b){if(b.type!=="require")if(b.D)for(var c=Sq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},Uq=function(a,b,c){var d=dd(c,null);dd(Sq(a,b).D,d);Sq(a,b).D=d},Oq=new Xq;function $q(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function ar(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function br(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Gl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=hc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}ar(e,"load",f);ar(e,"error",f)};$q(e,"load",f);$q(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function cr(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Dl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});dr(c,b)}
function dr(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else br(c,a,b===void 0?!1:b,d===void 0?!1:d)};var er=function(){this.ia=this.ia;this.R=this.R};er.prototype.ia=!1;er.prototype.dispose=function(){this.ia||(this.ia=!0,this.O())};er.prototype[Symbol.dispose]=function(){this.dispose()};er.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};er.prototype.O=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function fr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var gr=function(a,b){b=b===void 0?{}:b;er.call(this);this.D=null;this.la={};this.Eb=0;this.T=null;this.J=a;var c;this.Za=(c=b.timeoutMs)!=null?c:500;var d;this.Ca=(d=b.vr)!=null?d:!1};sa(gr,er);gr.prototype.O=function(){this.la={};this.T&&(ar(this.J,"message",this.T),delete this.T);delete this.la;delete this.J;delete this.D;er.prototype.O.call(this)};var ir=function(a){return typeof a.J.__tcfapi==="function"||hr(a)!=null};
gr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ca},d=hl(function(){return a(c)}),e=0;this.Za!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Za));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=fr(c),c.internalBlockOnErrors=b.Ca,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{jr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};gr.prototype.removeEventListener=function(a){a&&a.listenerId&&jr(this,"removeEventListener",null,a.listenerId)};
var lr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=kr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&kr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?kr(a.purpose.legitimateInterests,
b)&&kr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},kr=function(a,b){return!(!a||!a[b])},jr=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(hr(a)){mr(a);var g=++a.Eb;a.la[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},hr=function(a){if(a.D)return a.D;a.D=El(a.J,"__tcfapiLocator");return a.D},mr=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;$q(a.J,"message",b)}},nr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=fr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(cr({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var or={1:0,3:0,4:0,7:3,9:3,10:3};function pr(){return Dp("tcf",function(){return{}})}var qr=function(){return new gr(l,{timeoutMs:-1})};
function rr(){var a=pr(),b=qr();ir(b)&&!sr()&&!tr()&&N(124);if(!a.active&&ir(b)){sr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Ym().active=!0,a.tcString="tcunavailable");wp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)ur(a),xp([L.m.V,L.m.Na,L.m.W]),Ym().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,tr()&&(a.active=!0),!vr(c)||sr()||tr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in or)or.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(vr(c)){var g={},h;for(h in or)if(or.hasOwnProperty(h))if(h==="1"){var m,n=c,p={np:!0};p=p===void 0?{}:p;m=nr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.np)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?lr(n,"1",0):!0:!1;g["1"]=m}else g[h]=lr(c,h,or[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[L.m.V]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(xp([L.m.V,L.m.Na,L.m.W]),Ym().active=!0):(r[L.m.Na]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[L.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":xp([L.m.W]),qp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:wr()||""}))}}else xp([L.m.V,L.m.Na,L.m.W])})}catch(c){ur(a),xp([L.m.V,L.m.Na,L.m.W]),Ym().active=!0}}}
function ur(a){a.type="e";a.tcString="tcunavailable"}function vr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function sr(){return l.gtag_enable_tcf_support===!0}function tr(){return pr().enableAdvertiserConsentMode===!0}function wr(){var a=pr();if(a.active)return a.tcString}function xr(){var a=pr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function yr(a){if(!or.hasOwnProperty(String(a)))return!0;var b=pr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var zr=[L.m.V,L.m.ja,L.m.W,L.m.Na],Ar={},Br=(Ar[L.m.V]=1,Ar[L.m.ja]=2,Ar);function Cr(a){if(a===void 0)return 0;switch(O(a,L.m.Fa)){case void 0:return 1;case !1:return 3;default:return 2}}function Dr(){return E(182)?(E(183)?Ri.up:Ri.vp).indexOf(ro())!==-1&&kc.globalPrivacyControl===!0:ro()==="US-CO"&&kc.globalPrivacyControl===!0}
function Er(a){if(Dr())return!1;var b=Cr(a);if(b===3)return!1;switch(gn(L.m.Na)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Fr(){return jn()||!fn(L.m.V)||!fn(L.m.ja)}function Gr(){var a={},b;for(b in Br)Br.hasOwnProperty(b)&&(a[Br[b]]=gn(b));return"G1"+Ue(a[1]||0)+Ue(a[2]||0)}var Hr={},Ir=(Hr[L.m.V]=0,Hr[L.m.ja]=1,Hr[L.m.W]=2,Hr[L.m.Na]=3,Hr);
function Jr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Kr(a){for(var b="1",c=0;c<zr.length;c++){var d=b,e,f=zr[c],g=en.delegatedConsentTypes[f];e=g===void 0?0:Ir.hasOwnProperty(g)?12|Ir[g]:8;var h=Ym();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Jr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Jr(m.declare)<<4|Jr(m.default)<<2|Jr(m.update)])}var n=b,p=(Dr()?1:0)<<3,q=(jn()?1:0)<<2,r=Cr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[en.containerScopedDefaults.ad_storage<<4|en.containerScopedDefaults.analytics_storage<<2|en.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(en.usedContainerScopedDefaults?1:0)<<2|en.containerScopedDefaults.ad_personalization]}
function Lr(){if(!fn(L.m.W))return"-";for(var a=Object.keys(Ko),b={},c=k(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=en.corePlatformServices[e]!==!1}for(var f="",g=k(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ko[m])}(en.usedCorePlatformServices?en.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Mr(){return to()||(sr()||tr())&&xr()==="1"?"1":"0"}function Nr(){return(to()?!0:!(!sr()&&!tr())&&xr()==="1")||!fn(L.m.W)}
function Or(){var a="0",b="0",c;var d=pr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=pr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;to()&&(h|=1);xr()==="1"&&(h|=2);sr()&&(h|=4);var m;var n=pr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Ym().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Pr(){return ro()==="US-CO"};function Qr(){var a=!1;return a};var Rr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Sr(a){a=a===void 0?{}:a;var b=cg.ctid.split("-")[0].toUpperCase(),c={ctid:cg.ctid,Ij:Oj,Mj:Nj,km:sm.he?2:1,sq:a.Gm,oe:cg.canonicalContainerId};c.oe!==a.Oa&&(c.Oa=a.Oa);var d=Fm();c.wm=d?d.canonicalContainerId:void 0;Vj?(c.Uc=Rr[b],c.Uc||(c.Uc=0)):c.Uc=Xj?13:10;Ij.D?(c.Sc=0,c.Xl=2):Ij.O?c.Sc=1:Qr()?c.Sc=2:c.Sc=3;var e={};e[6]=tm;Ij.J===2?e[7]=!0:Ij.J===1&&(e[2]=!0);if(nc){var f=Ik(Ok(nc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Zl=e;return Xe(c,a.th)}
function Tr(){if(!E(192))return Sr();if(E(193))return Xe({Ij:Oj,Mj:Nj});var a=cg.ctid.split("-")[0].toUpperCase(),b={ctid:cg.ctid,Ij:Oj,Mj:Nj,km:sm.he?2:1,oe:cg.canonicalContainerId},c=Fm();b.wm=c?c.canonicalContainerId:void 0;Vj?(b.Uc=Rr[a],b.Uc||(b.Uc=0)):b.Uc=Xj?13:10;Ij.D?(b.Sc=0,b.Xl=2):Ij.O?b.Sc=1:Qr()?b.Sc=2:b.Sc=3;var d={};d[6]=tm;Ij.J===2?d[7]=!0:Ij.J===1&&(d[2]=!0);if(nc){var e=Ik(Ok(nc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Zl=d;return Xe(b)};function Ur(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Vr={P:{xo:0,Sj:1,xg:2,Yj:3,Nh:4,Wj:5,Xj:6,Zj:7,Oh:8,il:9,fl:10,yi:11,jl:12,bh:13,nl:14,Jf:15,uo:16,ke:17,Ri:18,Si:19,Ti:20,Ol:21,Ui:22,Qh:23,ik:24}};Vr.P[Vr.P.xo]="RESERVED_ZERO";Vr.P[Vr.P.Sj]="ADS_CONVERSION_HIT";Vr.P[Vr.P.xg]="CONTAINER_EXECUTE_START";Vr.P[Vr.P.Yj]="CONTAINER_SETUP_END";Vr.P[Vr.P.Nh]="CONTAINER_SETUP_START";Vr.P[Vr.P.Wj]="CONTAINER_BLOCKING_END";Vr.P[Vr.P.Xj]="CONTAINER_EXECUTE_END";Vr.P[Vr.P.Zj]="CONTAINER_YIELD_END";Vr.P[Vr.P.Oh]="CONTAINER_YIELD_START";Vr.P[Vr.P.il]="EVENT_EXECUTE_END";
Vr.P[Vr.P.fl]="EVENT_EVALUATION_END";Vr.P[Vr.P.yi]="EVENT_EVALUATION_START";Vr.P[Vr.P.jl]="EVENT_SETUP_END";Vr.P[Vr.P.bh]="EVENT_SETUP_START";Vr.P[Vr.P.nl]="GA4_CONVERSION_HIT";Vr.P[Vr.P.Jf]="PAGE_LOAD";Vr.P[Vr.P.uo]="PAGEVIEW";Vr.P[Vr.P.ke]="SNIPPET_LOAD";Vr.P[Vr.P.Ri]="TAG_CALLBACK_ERROR";Vr.P[Vr.P.Si]="TAG_CALLBACK_FAILURE";Vr.P[Vr.P.Ti]="TAG_CALLBACK_SUCCESS";Vr.P[Vr.P.Ol]="TAG_EXECUTE_END";Vr.P[Vr.P.Ui]="TAG_EXECUTE_START";Vr.P[Vr.P.Qh]="CUSTOM_PERFORMANCE_START";Vr.P[Vr.P.ik]="CUSTOM_PERFORMANCE_END";var Wr=[],Xr={},Yr={};var Zr=["1"];function $r(a){return a.origin!=="null"};function as(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return qg(10)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function bs(a,b,c,d){if(!cs(d))return[];if(Wr.includes("1")){var e;(e=Sc())==null||e.mark("1-"+Vr.P.Qh+"-"+(Yr["1"]||0))}var f=as(a,String(b||ds()),c);if(Wr.includes("1")){var g="1-"+Vr.P.ik+"-"+(Yr["1"]||0),h={start:"1-"+Vr.P.Qh+"-"+(Yr["1"]||0),end:g},m;(m=Sc())==null||m.mark(g);var n,p,q=(p=(n=Sc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(Yr["1"]=(Yr["1"]||0)+1,Xr["1"]=q+(Xr["1"]||0))}return f}
function es(a,b,c,d,e){if(cs(e)){var f=fs(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=gs(f,function(g){return g.Vo},b);if(f.length===1)return f[0];f=gs(f,function(g){return g.Xp},c);return f[0]}}}function hs(a,b,c,d){var e=ds(),f=window;$r(f)&&(f.document.cookie=a);var g=ds();return e!==g||c!==void 0&&bs(b,g,!1,d).indexOf(c)>=0}
function is(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!cs(c.Cc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=js(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Tp);g=e(g,"samesite",c.jq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ks(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!ls(u,c.path)&&hs(v,a,b,c.Cc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return ls(n,c.path)?1:hs(g,a,b,c.Cc)?0:1}function ms(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return is(a,b,c)}
function gs(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function fs(a,b,c){for(var d=[],e=bs(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({No:e[f],Oo:g.join("."),Vo:Number(n[0])||1,Xp:Number(n[1])||1})}}}return d}function js(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ns=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,os=/(^|\.)doubleclick\.net$/i;function ls(a,b){return a!==void 0&&(os.test(window.document.location.hostname)||b==="/"&&ns.test(a))}function ps(a){if(!a)return 1;var b=a;qg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function qs(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function rs(a,b){var c=""+ps(a),d=qs(b);d>1&&(c+="-"+d);return c}
var ds=function(){return $r(window)?window.document.cookie:""},cs=function(a){return a&&qg(8)?(Array.isArray(a)?a:[a]).every(function(b){return hn(b)&&fn(b)}):!0},ks=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;os.test(e)||ns.test(e)||a.push("none");return a};function ss(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Ur(a)&2147483647):String(b)}function ts(a){return[ss(a),Math.round(ub()/1E3)].join(".")}function us(a,b,c,d,e){var f=ps(b),g;return(g=es(a,f,qs(c),d,e))==null?void 0:g.Oo};function vs(a,b,c,d){var e,f=Number(a.Ac!=null?a.Ac:void 0);f!==0&&(e=new Date((b||ub())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Cc:d}};var ws=["ad_storage","ad_user_data"];function xs(a,b){if(!a)return Ya("TAGGING",32),10;if(b===null||b===void 0||b==="")return Ya("TAGGING",33),11;var c=ys(!1);if(c.error!==0)return Ya("TAGGING",34),c.error;if(!c.value)return Ya("TAGGING",35),2;c.value[a]=b;var d=zs(c);d!==0&&Ya("TAGGING",36);return d}
function As(a){if(!a)return Ya("TAGGING",27),{error:10};var b=ys();if(b.error!==0)return Ya("TAGGING",29),b;if(!b.value)return Ya("TAGGING",30),{error:2};if(!(a in b.value))return Ya("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(Ya("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function ys(a){a=a===void 0?!0:a;if(!fn(ws))return Ya("TAGGING",43),{error:3};try{if(!l.localStorage)return Ya("TAGGING",44),{error:1}}catch(f){return Ya("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=l.localStorage.getItem("_gcl_ls")}catch(f){return Ya("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return Ya("TAGGING",47),{error:12}}}catch(f){return Ya("TAGGING",48),{error:8}}if(b.schema!=="gcl")return Ya("TAGGING",49),{error:4};
if(b.version!==1)return Ya("TAGGING",50),{error:5};try{var e=Bs(b);a&&e&&zs({value:b,error:0})}catch(f){return Ya("TAGGING",48),{error:8}}return{value:b,error:0}}
function Bs(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,Ya("TAGGING",54),!0}else{for(var c=!1,d=k(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Bs(a[e.value])||c;return c}return!1}
function zs(a){if(a.error)return a.error;if(!a.value)return Ya("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return Ya("TAGGING",52),6}try{l.localStorage.setItem("_gcl_ls",c)}catch(d){return Ya("TAGGING",53),7}return 0};function Cs(){if(!Ds())return-1;var a=Es();return a!==-1&&Fs(a+1)?a+1:-1}function Es(){if(!Ds())return-1;var a=As("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Ds(){return fn(["ad_storage","ad_user_data"])?!0:!1}
function Fs(a,b){b=b||{};var c=ub();return xs("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(vs(b,c,!0).expires)})===0?!0:!1};var Gs;function Hs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Is,d=Js,e=Ks();if(!e.init){Bc(y,"mousedown",a);Bc(y,"keyup",a);Bc(y,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ls(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ks().decorators.push(f)}
function Ms(a,b,c){for(var d=Ks().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==y.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&xb(e,g.callback())}}return e}
function Ks(){var a=oc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ns=/(.*?)\*(.*?)\*(.*)/,Os=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ps=/^(?:www\.|m\.|amp\.)+/,Qs=/([^?#]+)(\?[^#]*)?(#.*)?/;function Rs(a){var b=Qs.exec(a);if(b)return{Bj:b[1],query:b[2],fragment:b[3]}}function Ss(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ts(a,b){var c=[kc.userAgent,(new Date).getTimezoneOffset(),kc.userLanguage||kc.language,Math.floor(ub()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Gs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Gs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Gs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Us(a){return function(b){var c=Ok(l.location.href),d=c.search.replace("?",""),e=Fk(d,"_gl",!1,!0)||"";b.query=Vs(e)||{};var f=Ik(c,"fragment"),g;var h=-1;if(zb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Vs(g||"")||{};a&&Ws(c,d,f)}}function Xs(a,b){var c=Ss(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ws(a,b,c){function d(g,h){var m=Xs("_gl",g);m.length&&(m=h+m);return m}if(jc&&jc.replaceState){var e=Ss("_gl");if(e.test(b)||e.test(c)){var f=Ik(a,"path");b=d(b,"?");c=d(c,"#");jc.replaceState({},"",""+f+b+c)}}}function Ys(a,b){var c=Us(!!b),d=Ks();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(xb(e,f.query),a&&xb(e,f.fragment));return e}
var Vs=function(a){try{var b=Zs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Va(d[e+1]);c[f]=g}Ya("TAGGING",6);return c}}catch(h){Ya("TAGGING",8)}};function Zs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ns.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ts(h,p)){m=!0;break a}m=!1}if(m)return h;Ya("TAGGING",7)}}}
function $s(a,b,c,d,e){function f(p){p=Xs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Rs(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Bj+h+m}
function at(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Ua(String(x))))}var z=v.join("*");u=["1",Ts(z),z].join("*");d?(qg(3)||qg(1)||!p)&&bt("_gl",u,a,p,q):ct("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ms(b,1,d),f=Ms(b,2,d),g=Ms(b,4,d),h=Ms(b,3,d);c(e,!1,!1);c(f,!0,!1);qg(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
dt(m,h[m],a)}function dt(a,b,c){c.tagName.toLowerCase()==="a"?ct(a,b,c):c.tagName.toLowerCase()==="form"&&bt(a,b,c)}function ct(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!qg(5)||d)){var h=l.location.href,m=Rs(c.href),n=Rs(h);g=!(m&&n&&m.Bj===n.Bj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=$s(a,b,c.href,d,e);Zb.test(p)&&(c.href=p)}}
function bt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=$s(a,b,f,d,e);Zb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=y.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Is(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||at(e,e.hostname)}}catch(g){}}function Js(a){try{var b=a.getAttribute("action");if(b){var c=Ik(Ok(b),"host");at(a,c)}}catch(d){}}function et(a,b,c,d){Hs();var e=c==="fragment"?2:1;d=!!d;Ls(a,b,e,d,!1);e===2&&Ya("TAGGING",23);d&&Ya("TAGGING",24)}
function ft(a,b){Hs();Ls(a,[Kk(l.location,"host",!0)],b,!0,!0)}function gt(){var a=y.location.hostname,b=Os.exec(y.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ps,""),m=e.replace(Ps,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ht(a,b){return a===!1?!1:a||b||gt()};var it=["1"],jt={},kt={};function lt(a,b){b=b===void 0?!0:b;var c=mt(a.prefix);if(jt[c])nt(a);else if(ot(c,a.path,a.domain)){var d=kt[mt(a.prefix)]||{id:void 0,Fh:void 0};b&&pt(a,d.id,d.Fh);nt(a)}else{var e=Qk("auiddc");if(e)Ya("TAGGING",17),jt[c]=e;else if(b){var f=mt(a.prefix),g=ts();qt(f,g,a);ot(c,a.path,a.domain);nt(a,!0)}}}
function nt(a,b){if((b===void 0?0:b)&&Ds()){var c=ys(!1);c.error!==0?Ya("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,zs(c)!==0&&Ya("TAGGING",41)):Ya("TAGGING",40):Ya("TAGGING",39)}Ds()&&Es()===-1&&Fs(0,a)}function pt(a,b,c){var d=mt(a.prefix),e=jt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(ub()/1E3)));qt(d,h,a,g*1E3)}}}}
function qt(a,b,c,d){var e;e=["1",rs(c.domain,c.path),b].join(".");var f=vs(c,d);f.Cc=rt();ms(a,e,f)}function ot(a,b,c){var d=us(a,b,c,it,rt());if(!d)return!1;st(a,d);return!0}function st(a,b){var c=b.split(".");c.length===5?(jt[a]=c.slice(0,2).join("."),kt[a]={id:c.slice(2,4).join("."),Fh:Number(c[4])||0}):c.length===3?kt[a]={id:c.slice(0,2).join("."),Fh:Number(c[2])||0}:jt[a]=b}function mt(a){return(a||"_gcl")+"_au"}
function tt(a){function b(){fn(c)&&a()}var c=rt();mn(function(){b();fn(c)||nn(b,c)},c)}function ut(a){var b=Ys(!0),c=mt(a.prefix);tt(function(){var d=b[c];if(d){st(c,d);var e=Number(jt[c].split(".")[1])*1E3;if(e){Ya("TAGGING",16);var f=vs(a,e);f.Cc=rt();var g=["1",rs(a.domain,a.path),d].join(".");ms(c,g,f)}}})}function vt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=us(a,e.path,e.domain,it,rt());h&&(g[a]=h);return g};tt(function(){et(f,b,c,d)})}
function rt(){return["ad_storage","ad_user_data"]};function wt(a){for(var b=[],c=y.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Pj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function xt(a,b){var c=wt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Pj]||(d[c[e].Pj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Pj].push(g)}}return d};var zt={},At=(zt.k={da:/^[\w-]+$/},zt.b={da:/^[\w-]+$/,Jj:!0},zt.i={da:/^[1-9]\d*$/},zt.h={da:/^\d+$/},zt.t={da:/^[1-9]\d*$/},zt.d={da:/^[A-Za-z0-9_-]+$/},zt.j={da:/^\d+$/},zt.u={da:/^[1-9]\d*$/},zt.l={da:/^[01]$/},zt.o={da:/^[1-9]\d*$/},zt.g={da:/^[01]$/},zt.s={da:/^.+$/},zt);var Bt={},Ft=(Bt[5]={Kh:{2:Ct},uj:"2",uh:["k","i","b","u"]},Bt[4]={Kh:{2:Ct,GCL:Dt},uj:"2",uh:["k","i","b"]},Bt[2]={Kh:{GS2:Ct,GS1:Et},uj:"GS2",uh:"sogtjlhd".split("")},Bt);function Gt(a,b,c){var d=Ft[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function Ct(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ft[b];if(f){for(var g=f.uh,h=k(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=At[p];r&&(r.Jj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ht(a,b,c){var d=Ft[b];if(d)return[d.uj,c||"1",It(a,b)].join(".")}
function It(a,b){var c=Ft[b];if(c){for(var d=[],e=k(c.uh),f=e.next();!f.done;f=e.next()){var g=f.value,h=At[g];if(h){var m=a[g];if(m!==void 0)if(h.Jj&&Array.isArray(m))for(var n=k(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Dt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Et(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Jt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Kt(a,b,c){if(Ft[b]){for(var d=[],e=bs(a,void 0,void 0,Jt.get(b)),f=k(e),g=f.next();!g.done;g=f.next()){var h=Gt(g.value,b,c);h&&d.push(Lt(h))}return d}}function Mt(a,b,c,d,e){d=d||{};var f=rs(d.domain,d.path),g=Ht(b,c,f);if(!g)return 1;var h=vs(d,e,void 0,Jt.get(c));return ms(a,g,h)}function Nt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Lt(a){for(var b=k(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=At[e];d.Tf?d.Tf.Jj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Nt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Nt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Ot=function(){this.value=0};Ot.prototype.set=function(a){return this.value|=1<<a};var Pt=function(a,b){b<=0||(a.value|=1<<b-1)};Ot.prototype.get=function(){return this.value};Ot.prototype.clear=function(a){this.value&=~(1<<a)};Ot.prototype.clearAll=function(){this.value=0};Ot.prototype.equals=function(a){return this.value===a.value};function Qt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Rt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]}
function St(a){if(!a||a.length<50||a.length>200)return!1;var b=Qt(a),c;if(b)a:{if(b&&b.length!==0){var d=0;try{for(;d<b.length;){var e=Rt(b,d);if(e===void 0)break;var f=k(e),g=f.next().value,h=f.next().value,m=g,n=h,p=m&7;if(m>>3===16382){if(p!==0)break;var q=Rt(b,n);if(q===void 0)break;c=k(q).next().value===1;break a}var r;b:{var t=void 0;switch(p){case 0:r=(t=Rt(b,n))==null?void 0:t[1];break b;case 1:r=n+8;break b;case 2:var u=Rt(b,n);if(u===void 0)break;var v=k(u),w=v.next().value;r=v.next().value+
w;break b;case 5:r=n+4;break b}r=void 0}var x=r;if(x===void 0||x>b.length)break;d=x}}catch(z){}}c=!1}else c=!1;return c};function Tt(){var a=String,b=l.location.hostname,c=l.location.pathname,d=b=Hb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Hb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Ur((""+b+e).toLowerCase()))};var Ut={},Vt=(Ut.gclid=!0,Ut.dclid=!0,Ut.gbraid=!0,Ut.wbraid=!0,Ut),Wt=/^\w+$/,Xt=/^[\w-]+$/,Yt={},Zt=(Yt.aw="_aw",Yt.dc="_dc",Yt.gf="_gf",Yt.gp="_gp",Yt.gs="_gs",Yt.ha="_ha",Yt.ag="_ag",Yt.gb="_gb",Yt),$t=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,au=/^www\.googleadservices\.com$/;function bu(){return["ad_storage","ad_user_data"]}function cu(a){return!qg(8)||fn(a)}function du(a,b){function c(){var d=cu(b);d&&a();return d}mn(function(){c()||nn(c,b)},b)}
function eu(a){return fu(a).map(function(b){return b.gclid})}function gu(a){return hu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function hu(a){var b=iu(a.prefix),c=ju("gb",b),d=ju("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=fu(c).map(e("gb")),g=ku(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function lu(a,b,c,d,e,f){var g=jb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Ed=f),g.labels=mu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Ed:f})}function ku(a){for(var b=Kt(a,5)||[],c=[],d=k(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=nu(f);if(n){var p=void 0;qg(9)&&(p=f.u);lu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function fu(a){for(var b=[],c=bs(a,y.cookie,void 0,bu()),d=k(c),e=d.next();!e.done;e=d.next()){var f=ou(e.value);if(f!=null){var g=f;lu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return pu(b)}function qu(a,b){for(var c=[],d=k(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=k(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function ru(a,b,c){c=c===void 0?!1:c;for(var d,e,f=k(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ma&&b.Ma&&h.Ma.equals(b.Ma)&&(e=h)}if(d){var m,n,p=(m=d.Ma)!=null?m:new Ot,q=(n=b.Ma)!=null?n:new Ot;p.value|=q.value;d.Ma=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Ed=b.Ed);d.labels=qu(d.labels||[],b.labels||[]);d.zb=qu(d.zb||[],b.zb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function su(a){if(!a)return new Ot;var b=new Ot;if(a===1)return Pt(b,2),Pt(b,3),b;Pt(b,a);return b}
function tu(){var a=As("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Xt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Ot;typeof e==="number"?g=su(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ma:g,zb:[2]}}catch(h){return null}}
function uu(){var a=As("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Xt))return b;var f=new Ot,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ma:f,zb:[2]});return b},[])}catch(b){return null}}
function vu(a){for(var b=[],c=bs(a,y.cookie,void 0,bu()),d=k(c),e=d.next();!e.done;e=d.next()){var f=ou(e.value);f!=null&&(f.Ed=void 0,f.Ma=new Ot,f.zb=[1],ru(b,f))}var g=tu();g&&(g.Ed=void 0,g.zb=g.zb||[2],ru(b,g));if(qg(12)){var h=uu();if(h)for(var m=k(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Ed=void 0;p.zb=p.zb||[2];ru(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return pu(b)}
function mu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function iu(a){return a&&typeof a==="string"&&a.match(Wt)?a:"_gcl"}function wu(a,b){if(a){var c={value:a,Ma:new Ot};Pt(c.Ma,b);return c}}
function xu(a,b,c,d){var e=Ok(a),f=Ik(e,"query",!1,void 0,"gclsrc"),g=wu(Ik(e,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!g||!f)){var h=e.hash.replace("#","");g||(g=wu(Fk(h,"gclid",!1),3));f||(f=Fk(h,"gclsrc",!1))}var m;if(d&&!St((m=g)==null?void 0:m.value)){var n;a:{for(var p=Gk(Ik(e,"query")),q=k(Object.keys(p)),r=q.next();!r.done;r=q.next()){var t=r.value;if(!Vt[t]){var u=p[t][0]||"";if(St(u)){n=u;break a}}}n=void 0}var v=n,w;v&&v!==((w=g)==null?void 0:w.value)&&(g=wu(v,7))}return!g||f!==void 0&&
f!=="aw"&&f!=="aw.ds"?[]:[g]}function yu(a,b){var c=Ok(a),d=Ik(c,"query",!1,void 0,"gclid"),e=Ik(c,"query",!1,void 0,"gclsrc"),f=Ik(c,"query",!1,void 0,"wbraid");f=Fb(f);var g=Ik(c,"query",!1,void 0,"gbraid"),h=Ik(c,"query",!1,void 0,"gad_source"),m=Ik(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Fk(n,"gclid",!1);e=e||Fk(n,"gclsrc",!1);f=f||Fk(n,"wbraid",!1);g=g||Fk(n,"gbraid",!1);h=h||Fk(n,"gad_source",!1)}return zu(d,e,m,f,g,h)}
function Au(){return yu(l.location.href,!0)}
function zu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Xt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Xt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Xt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Xt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Bu(a){for(var b=Au(),c=!0,d=k(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=yu(l.document.referrer,!1),b.gad_source=void 0);Cu(b,!1,a)}
function Du(a){Bu(a);var b=xu(l.location.href,!0,!1,qg(13)?Eu(Fu()):!1);b.length||(b=xu(l.document.referrer,!1,!0,!1));if(b.length){var c=b[0];a=a||{};var d=ub(),e=vs(a,d,!0),f=bu(),g=function(){cu(f)&&e.expires!==void 0&&xs("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ma.get()},expires:Number(e.expires)})};mn(function(){g();cu(f)||nn(g,f)},f)}}
function Gu(a,b,c){c=c||{};var d=ub(),e=vs(c,d,!0),f=bu(),g=function(){if(cu(f)&&e.expires!==void 0){var h=uu()||[];ru(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ma:su(b)},!0);xs("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ma?m.Ma.get():0},expires:Number(m.expires)}}))}};mn(function(){cu(f)?g():nn(g,f)},f)}
function Cu(a,b,c,d,e){c=c||{};e=e||[];var f=iu(c.prefix),g=d||ub(),h=Math.round(g/1E3),m=bu(),n=!1,p=!1,q=function(){if(cu(m)){var r=vs(c,g,!0);r.Cc=m;for(var t=function(K,U){var Q=ju(K,f);Q&&(ms(Q,U,r),K!=="gb"&&(n=!0))},u=function(K){var U=["GCL",h,K];e.length>0&&U.push(e.join("."));return U.join(".")},v=k(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],B=ju("gb",f);!b&&fu(B).some(function(K){return K.gclid===z&&K.labels&&
K.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&cu("ad_storage")&&(p=!0,!n)){var C=a.gbraid,F=ju("ag",f);if(b||!ku(F).some(function(K){return K.gclid===C&&K.labels&&K.labels.length>0})){var G={},I=(G.k=C,G.i=""+h,G.b=e,G);Mt(F,I,5,c,g)}}Hu(a,f,g,c)};mn(function(){q();cu(m)||nn(q,m)},m)}
function Hu(a,b,c,d){if(a.gad_source!==void 0&&cu("ad_storage")){if(qg(4)){var e=Rc();if(e==="r"||e==="h")return}var f=a.gad_source,g=ju("gs",b);if(g){var h=Math.floor((ub()-(Qc()||0))/1E3),m;if(qg(9)){var n=Tt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Mt(g,m,5,d,c)}}}
function Iu(a,b){var c=Ys(!0);du(function(){for(var d=iu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Zt[f]!==void 0){var g=ju(f,d),h=c[g];if(h){var m=Math.min(Ju(h),ub()),n;b:{for(var p=m,q=bs(g,y.cookie,void 0,bu()),r=0;r<q.length;++r)if(Ju(q[r])>p){n=!0;break b}n=!1}if(!n){var t=vs(b,m,!0);t.Cc=bu();ms(g,h,t)}}}}Cu(zu(c.gclid,c.gclsrc),!1,b)},bu())}
function Ku(a){var b=["ag"],c=Ys(!0),d=iu(a.prefix);du(function(){for(var e=0;e<b.length;++e){var f=ju(b[e],d);if(f){var g=c[f];if(g){var h=Gt(g,5);if(h){var m=nu(h);m||(m=ub());var n;a:{for(var p=m,q=Kt(f,5),r=0;r<q.length;++r)if(nu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Mt(f,h,5,a,m)}}}}},["ad_storage"])}function ju(a,b){var c=Zt[a];if(c!==void 0)return b+c}function Ju(a){return Lu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function nu(a){return a?(Number(a.i)||0)*1E3:0}function ou(a){var b=Lu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Lu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Xt.test(a[2])?[]:a}
function Mu(a,b,c,d,e){if(Array.isArray(b)&&$r(l)){var f=iu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=ju(a[m],f);if(n){var p=bs(n,y.cookie,void 0,bu());p.length&&(h[n]=p.sort()[p.length-1])}}return h};du(function(){et(g,b,c,d)},bu())}}
function Nu(a,b,c,d){if(Array.isArray(a)&&$r(l)){var e=["ag"],f=iu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=ju(e[m],f);if(!n)return{};var p=Kt(n,5);if(p.length){var q=p.sort(function(r,t){return nu(t)-nu(r)})[0];h[n]=Ht(q,5)}}return h};du(function(){et(g,a,b,c)},["ad_storage"])}}function pu(a){return a.filter(function(b){return Xt.test(b.gclid)})}
function Ou(a,b){if($r(l)){for(var c=iu(b.prefix),d={},e=0;e<a.length;e++)Zt[a[e]]&&(d[a[e]]=Zt[a[e]]);du(function(){nb(d,function(f,g){var h=bs(c+g,y.cookie,void 0,bu());h.sort(function(t,u){return Ju(u)-Ju(t)});if(h.length){var m=h[0],n=Ju(m),p=Lu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Lu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Cu(q,!0,b,n,p)}})},bu())}}
function Pu(a){var b=["ag"],c=["gbraid"];du(function(){for(var d=iu(a.prefix),e=0;e<b.length;++e){var f=ju(b[e],d);if(!f)break;var g=Kt(f,5);if(g.length){var h=g.sort(function(q,r){return nu(r)-nu(q)})[0],m=nu(h),n=h.b,p={};p[c[e]]=h.k;Cu(p,!0,a,m,n)}}},["ad_storage"])}function Qu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Ru(a){function b(h,m,n){n&&(h[m]=n)}if(jn()){var c=Au(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Ys(!1)._gs);if(Qu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ft(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ft(function(){return g},1)}}}
function Su(a){if(!qg(1))return null;var b=Ys(!0).gad_source;if(b!=null)return l.location.hash="",b;if(qg(2)){var c=Ok(l.location.href);b=Ik(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=Au();if(Qu(d,a))return"0"}return null}function Tu(a){var b=Su(a);b!=null&&ft(function(){var c={};return c.gad_source=b,c},4)}
function Uu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Vu(a,b,c,d){var e=[];c=c||{};if(!cu(bu()))return e;var f=fu(a),g=Uu(e,f,b);if(g.length&&!d)for(var h=k(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=vs(c,p,!0);r.Cc=bu();ms(a,q,r)}return e}
function Wu(a,b){var c=[];b=b||{};var d=hu(b),e=Uu(c,d,a);if(e.length)for(var f=k(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=iu(b.prefix),n=ju(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Mt(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=vs(b,u,!0);B.Cc=bu();ms(n,z,B)}}return c}
function Xu(a,b){var c=iu(b),d=ju(a,c);if(!d)return 0;var e;e=a==="ag"?ku(d):fu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Yu(a){for(var b=0,c=k(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Zu(a){var b=Math.max(Xu("aw",a),Yu(cu(bu())?xt():{})),c=Math.max(Xu("gb",a),Yu(cu(bu())?xt("_gac_gb",!0):{}));c=Math.max(c,Xu("ag",a));return c>b}
function Eu(a){return $t.test(a)||au.test(a)}function Fu(){return y.referrer?Ik(Ok(y.referrer),"host"):""};
var $u=function(a,b){b=b===void 0?!1:b;var c=Dp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},av=function(a){return Pk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},hv=function(a,b,c,d,e){var f=iu(a.prefix);if($u(f,!0)){var g=Au(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=bv(),r=q.Yf,t=q.im;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,zd:p});n&&h.push({gclid:n,zd:"ds"});h.length===2&&N(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,zd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",zd:"aw.ds"});cv(function(){var u=sp(dv());if(u){lt(a);var v=[],w=u?jt[mt(a.prefix)]:void 0;w&&v.push("auid="+w);if(sp(L.m.W)){e&&v.push("userId="+e);var x=fo(ao.aa.Jl);if(x===void 0)eo(ao.aa.Kl,!0);else{var z=fo(ao.aa.ph);v.push("ga_uid="+z+"."+x)}}var B=Fu(),C=u||!d?h:[];C.length===0&&Eu(B)&&C.push({gclid:"",zd:""});if(C.length!==0||r!==void 0){B&&v.push("ref="+encodeURIComponent(B));var F=ev();v.push("url="+encodeURIComponent(F));
v.push("tft="+ub());var G=Qc();G!==void 0&&v.push("tfd="+Math.round(G));var I=Fl(!0);v.push("frm="+I);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var K={};c=rq(hq(new gq(0),(K[L.m.Fa]=Oq.D[L.m.Fa],K)))}v.push("gtm="+Sr({Oa:b}));Fr()&&v.push("gcs="+Gr());v.push("gcd="+Kr(c));Nr()&&v.push("dma_cps="+Lr());v.push("dma="+Mr());Er(c)?v.push("npa=0"):v.push("npa=1");Pr()&&v.push("_ng=1");ir(qr())&&v.push("tcfd="+Or());
var U=xr();U&&v.push("gdpr="+U);var Q=wr();Q&&v.push("gdpr_consent="+Q);E(23)&&v.push("apve=0");E(123)&&Ys(!1)._up&&v.push("gtm_up=1");fk()&&v.push("tag_exp="+fk());if(C.length>0)for(var na=0;na<C.length;na++){var T=C[na],aa=T.gclid,Y=T.zd;if(!fv(a.prefix,Y+"."+aa,w!==void 0)){var V=gv+"?"+v.join("&");aa!==""?V=Y==="gb"?V+"&wbraid="+aa:V+"&gclid="+aa+"&gclsrc="+Y:Y==="aw.ds"&&(V+="&gclsrc=aw.ds");Jc(V)}}else if(r!==void 0&&!fv(a.prefix,"gad",w!==void 0)){var ka=gv+"?"+v.join("&");Jc(ka)}}}})}},fv=
function(a,b,c){var d=Dp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},bv=function(){var a=Ok(l.location.href),b=void 0,c=void 0,d=Ik(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(iv);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Yf:b,im:c}},ev=function(){var a=Fl(!1)===1?l.top.location.href:l.location.href;return a=a.replace(/[\?#].*$/,"")},jv=function(a){var b=[];nb(a,function(c,d){d=pu(d);for(var e=[],f=0;f<
d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},lv=function(a,b){return kv("dc",a,b)},mv=function(a,b){return kv("aw",a,b)},kv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Qk("gcl"+a);if(d)return d.split(".")}var e=iu(b);if(e==="_gcl"){var f=!sp(dv())&&c,g;g=Au()[a]||[];if(g.length>0)return f?["0"]:g}var h=ju(a,e);return h?eu(h):[]},cv=function(a){var b=dv();vp(function(){a();sp(b)||nn(a,b)},b)},dv=function(){return[L.m.V,L.m.W]},gv=Li(36,'https://adservice.google.com/pagead/regclk'),
iv=/^gad_source[_=](\d+)$/;function nv(){return Dp("dedupe_gclid",function(){return ts()})};var ov=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,pv=/^www.googleadservices.com$/;function qv(a){a||(a=rv());return a.Bq?!1:a.Cp||a.Dp||a.Gp||a.Ep||a.Yf||a.mp||a.Fp||a.rp?!0:!1}function rv(){var a={},b=Ys(!0);a.Bq=!!b._up;var c=Au();a.Cp=c.aw!==void 0;a.Dp=c.dc!==void 0;a.Gp=c.wbraid!==void 0;a.Ep=c.gbraid!==void 0;a.Fp=c.gclsrc==="aw.ds";a.Yf=bv().Yf;var d=y.referrer?Ik(Ok(y.referrer),"host"):"";a.rp=ov.test(d);a.mp=pv.test(d);return a};function sv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function tv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function uv(){return["ad_storage","ad_user_data"]}function vv(a){if(E(38)&&!fo(ao.aa.yl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{sv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(eo(ao.aa.yl,function(d){d.gclid&&Gu(d.gclid,5,a)}),tv(c)||N(178))})}catch(c){N(177)}};mn(function(){cu(uv())?b():nn(b,uv())},uv())}};var wv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function xv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?eo(ao.aa.Lf,{gadSource:a.data.gadSource}):N(173)}
function yv(a,b){if(E(a)){if(fo(ao.aa.Lf))return N(176),ao.aa.Lf;if(fo(ao.aa.Al))return N(170),ao.aa.Lf;var c=Hl();if(!c)N(171);else if(c.opener){var d=function(g){if(wv.includes(g.origin)){a===119?xv(g):a===200&&(xv(g),g.data.gclid&&Gu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);ar(c,"message",d)}else N(172)};if($q(c,"message",d)){eo(ao.aa.Al,!0);for(var e=k(wv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);N(174);return ao.aa.Lf}N(175)}}}
;var zv=function(){this.D=this.gppString=void 0};zv.prototype.reset=function(){this.D=this.gppString=void 0};var Av=new zv;var Bv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Cv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Dv=/^\d+\.fls\.doubleclick\.net$/,Ev=/;gac=([^;?]+)/,Fv=/;gacgb=([^;?]+)/;
function Gv(a,b){if(Dv.test(y.location.host)){var c=y.location.href.match(b);return c&&c.length===2&&c[1].match(Bv)?Hk(c[1])||"":""}for(var d=[],e=k(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Hv(a,b,c){for(var d=cu(bu())?xt("_gac_gb",!0):{},e=[],f=!1,g=k(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Vu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{lp:f?e.join(";"):"",kp:Gv(d,Fv)}}function Iv(a){var b=y.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Cv)?b[1]:void 0}
function Jv(a){var b=qg(9),c={},d,e,f;Dv.test(y.location.host)&&(d=Iv("gclgs"),e=Iv("gclst"),b&&(f=Iv("gcllp")));if(d&&e&&(!b||f))c.zh=d,c.Bh=e,c.Ah=f;else{var g=ub(),h=ku((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Ed}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.zh=m.join("."),c.Bh=n.join("."),b&&p.length>0&&(c.Ah=p.join(".")))}return c}
function Kv(a,b,c,d){d=d===void 0?!1:d;if(Dv.test(y.location.host)){var e=Iv(c);if(e){if(d){var f=new Ot;Pt(f,2);Pt(f,3);return e.split(".").map(function(h){return{gclid:h,Ma:f,zb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?vu(g):fu(g)}if(b==="wbraid")return fu((a||"_gcl")+"_gb");if(b==="braids")return hu({prefix:a})}return[]}function Lv(a){return Dv.test(y.location.host)?!(Iv("gclaw")||Iv("gac")):Zu(a)}
function Mv(a,b,c){var d;d=c?Wu(a,b):Vu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Nv(){var a=l.__uspapi;if(eb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Sv=function(a){if(a.eventName===L.m.ra&&R(a,P.C.fa)===M.K.Ia)if(E(24)){S(a,P.C.je,O(a.F,L.m.za)!=null&&O(a.F,L.m.za)!==!1&&!sp([L.m.V,L.m.W]));var b=Ov(a),c=O(a.F,L.m.Qa)!==!1;c||W(a,L.m.Vh,"1");var d=iu(b.prefix),e=R(a,P.C.kh);if(!R(a,P.C.ka)&&!R(a,P.C.Of)&&!R(a,P.C.ie)){var f=O(a.F,L.m.Cb),g=O(a.F,L.m.Ra)||{};Pv({pe:c,xe:g,Ae:f,Qc:b});if(!e&&!$u(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{W(a,L.m.gd,L.m.Xc);if(R(a,P.C.ka))W(a,L.m.gd,L.m.on),W(a,L.m.ka,"1");else if(R(a,P.C.Of))W(a,L.m.gd,
L.m.yn);else if(R(a,P.C.ie))W(a,L.m.gd,L.m.vn);else{var h=Au();W(a,L.m.Yc,h.gclid);W(a,L.m.ed,h.dclid);W(a,L.m.qk,h.gclsrc);Qv(a,L.m.Yc)||Qv(a,L.m.ed)||(W(a,L.m.Ud,h.wbraid),W(a,L.m.Je,h.gbraid));W(a,L.m.Wa,Fu());W(a,L.m.Ba,ev());if(E(27)&&nc){var m=Ik(Ok(nc),"host");m&&W(a,L.m.Xk,m)}if(!R(a,P.C.ie)){var n=bv(),p=n.im;W(a,L.m.He,n.Yf);W(a,L.m.Ie,p)}W(a,L.m.Ic,Fl(!0));var q=rv();qv(q)&&W(a,L.m.jd,"1");W(a,L.m.sk,nv());Ys(!1)._up==="1"&&W(a,L.m.Nk,"1")}Tn=!0;W(a,L.m.Bb);W(a,L.m.Mb);var r=sp([L.m.V,
L.m.W]);r&&(W(a,L.m.Bb,Rv()),c&&(lt(b),W(a,L.m.Mb,jt[mt(b.prefix)])));W(a,L.m.kc);W(a,L.m.nb);if(!Qv(a,L.m.Yc)&&!Qv(a,L.m.ed)&&Lv(d)){var t=gu(b);t.length>0&&W(a,L.m.kc,t.join("."))}else if(!Qv(a,L.m.Ud)&&r){var u=eu(d+"_aw");u.length>0&&W(a,L.m.nb,u.join("."))}E(31)&&W(a,L.m.Qk,Rc());a.F.isGtmEvent&&(a.F.D[L.m.Fa]=Oq.D[L.m.Fa]);Er(a.F)?W(a,L.m.vc,!1):W(a,L.m.vc,!0);S(a,P.C.vg,!0);var v=Nv();v!==void 0&&W(a,L.m.zf,v||"error");var w=xr();w&&W(a,L.m.hd,w);if(E(137))try{var x=Intl.DateTimeFormat().resolvedOptions().timeZone;
W(a,L.m.mi,x||"-")}catch(F){W(a,L.m.mi,"e")}var z=wr();z&&W(a,L.m.md,z);var B=Av.gppString;B&&W(a,L.m.af,B);var C=Av.D;C&&W(a,L.m.Ze,C);S(a,P.C.Ja,!1)}}else a.isAborted=!0},Ov=function(a){var b={prefix:O(a.F,L.m.Ob)||O(a.F,L.m.ib),domain:O(a.F,L.m.pb),Ac:O(a.F,L.m.qb),flags:O(a.F,L.m.wb)};a.F.isGtmEvent&&(b.path=O(a.F,L.m.Pb));return b},Tv=function(a,b){var c,d,e,f,g,h,m,n;c=a.pe;d=a.xe;e=a.Ae;f=a.Oa;g=a.F;h=a.ye;m=a.yr;n=a.Pm;Pv({pe:c,xe:d,Ae:e,Qc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,hv(b,
f,g,h,n))},Uv=function(a,b){if(!R(a,P.C.ie)){var c=yv(119);if(c){var d=fo(c),e=function(g){S(a,P.C.ie,!0);var h=Qv(a,L.m.He),m=Qv(a,L.m.Ie);W(a,L.m.He,String(g.gadSource));W(a,L.m.Ie,6);S(a,P.C.ka);S(a,P.C.Of);W(a,L.m.ka);b();W(a,L.m.He,h);W(a,L.m.Ie,m);S(a,P.C.ie,!1)};if(d)e(d);else{var f=void 0;f=ho(c,function(g,h){e(h);io(c,f)})}}}},Pv=function(a){var b,c,d,e;b=a.pe;c=a.xe;d=a.Ae;e=a.Qc;b&&(ht(c[L.m.ae],!!c[L.m.na])&&(Iu(Vv,e),Ku(e),ut(e)),Fl()!==2?(Du(e),vv(e),yv(200,e)):Bu(e),Ou(Vv,e),Pu(e));
c[L.m.na]&&(Mu(Vv,c[L.m.na],c[L.m.Lc],!!c[L.m.rc],e.prefix),Nu(c[L.m.na],c[L.m.Lc],!!c[L.m.rc],e.prefix),vt(mt(e.prefix),c[L.m.na],c[L.m.Lc],!!c[L.m.rc],e),vt("FPAU",c[L.m.na],c[L.m.Lc],!!c[L.m.rc],e));d&&(E(101)?Ru(Wv):Ru(Xv));Tu(Xv)},Yv=function(a,b,c,d){var e,f,g;e=a.Qm;f=a.callback;g=a.om;if(typeof f==="function")if(e===L.m.nb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===L.m.Mb?(N(65),lt(b,!1),f(jt[mt(b.prefix)])):f(g)},Zv=function(a,b){Array.isArray(b)||
(b=[b]);var c=R(a,P.C.fa);return b.indexOf(c)>=0},Vv=["aw","dc","gb"],Xv=["aw","dc","gb","ag"],Wv=["aw","dc","gb","ag","gad_source"];function $v(a){var b=O(a.F,L.m.Kc),c=O(a.F,L.m.Jc);b&&!c?(a.eventName!==L.m.ra&&a.eventName!==L.m.Qd&&N(131),a.isAborted=!0):!b&&c&&(N(132),a.isAborted=!0)}function aw(a){var b=sp(L.m.V)?Cp.pscdl:"denied";b!=null&&W(a,L.m.Jg,b)}function bw(a){var b=Fl(!0);W(a,L.m.Ic,b)}function cw(a){Pr()&&W(a,L.m.Yd,1)}
function Rv(){var a=y.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Hk(a.substring(0,b))===void 0;)b--;return Hk(a.substring(0,b))||""}function dw(a){ew(a,Lp.Af.Ym,O(a.F,L.m.qb))}function ew(a,b,c){Qv(a,L.m.rd)||W(a,L.m.rd,{});Qv(a,L.m.rd)[b]=c}function fw(a){S(a,P.C.Nf,Xm.Z.Ea)}function gw(a){var b=ab("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,L.m.bf,b),Za())}function hw(a){var b=a.F.getMergedValues(L.m.qc);b&&a.mergeHitDataForKey(L.m.qc,b)}
function iw(a,b){b=b===void 0?!1:b;if(E(108)){var c=R(a,P.C.Mf);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,P.C.Rj,!1),b||!jw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,P.C.Rj,!0)}}function kw(a){el&&(Tn=!0,a.eventName===L.m.ra?Zn(a.F,a.target.id):(R(a,P.C.Ee)||(Wn[a.target.id]=!0),Kp(R(a,P.C.kb))))};
var lw=function(a){if(Qv(a,L.m.kc)||Qv(a,L.m.Xd)){var b=Qv(a,L.m.mc),c=dd(R(a,P.C.ya),null),d=iu(c.prefix);c.prefix=d==="_gcl"?"":d;if(Qv(a,L.m.kc)){var e=Mv(b,c,!R(a,P.C.ol));S(a,P.C.ol,!0);e&&W(a,L.m.bl,e)}if(Qv(a,L.m.Xd)){var f=Hv(b,c).lp;f&&W(a,L.m.Ik,f)}}},pw=function(a){var b=new mw;E(101)&&Zv(a,[M.K.X])&&W(a,L.m.Zk,Ys(!1)._gs);if(E(16)){var c=O(a.F,L.m.Ba);c||(c=Fl(!1)===1?l.top.location.href:l.location.href);var d,e=Ok(c),f=Ik(e,"query",!1,void 0,"gclid");if(!f){var g=e.hash.replace("#","");
f=f||Fk(g,"gclid",!1)}(d=f?f.length:void 0)&&W(a,L.m.pk,d)}if(sp(L.m.V)&&R(a,P.C.Vc)){var h=R(a,P.C.ya),m=iu(h.prefix);m==="_gcl"&&(m="");var n=Jv(m);W(a,L.m.Rd,n.zh);W(a,L.m.Td,n.Bh);E(135)&&W(a,L.m.Sd,n.Ah);Lv(m)?nw(a,b,h,m):ow(a,b,m)}if(E(21)){var p=sp(L.m.V)&&sp(L.m.W),q;var r;b:{var t,u=[];try{l.navigation&&l.navigation.entries&&(u=l.navigation.entries())}catch(Q){}t=u;var v={};try{for(var w=t.length-1;w>=0;w--){var x=t[w]&&t[w].url;if(x){var z=(new URL(x)).searchParams,B=z.get("gclid")||void 0,
C=z.get("gclsrc")||void 0;if(B){v.gclid=B;C&&(v.zd=C);r=v;break b}}}}catch(Q){}r=v}var F=r,G=F.gclid,I=F.zd,K;if(!G||I!==void 0&&I!=="aw"&&I!=="aw.ds")K=void 0;else if(G!==void 0){var U=new Ot;Pt(U,2);Pt(U,3);K={version:"GCL",timestamp:0,gclid:G,Ma:U,zb:[3]}}else K=void 0;q=K;q&&(p||(q.gclid="0"),b.O(q),b.T(!1))}b.la(a)},ow=function(a,b,c){var d=R(a,P.C.fa)===M.K.X&&Fl()!==2;Kv(c,"gclid","gclaw",d).forEach(function(f){b.O(f)});b.T(!d);if(!c){var e=Gv(cu(bu())?xt():{},Ev);e&&W(a,L.m.Sg,e)}},nw=function(a,
b,c,d){Kv(d,"braids","gclgb").forEach(function(g){b.ia(g)});if(!d){var e=Qv(a,L.m.mc);c=dd(c,null);c.prefix=d;var f=Hv(e,c,!0).kp;f&&W(a,L.m.Xd,f)}},mw=function(){this.D=[];this.R=[];this.J=void 0};mw.prototype.O=function(a){ru(this.D,a)};mw.prototype.ia=function(a){ru(this.R,a)};mw.prototype.T=function(a){this.J!==!1&&(this.J=a)};mw.prototype.la=function(a){if(this.D.length>0){var b=[],c=[],d=[];this.D.forEach(function(f){b.push(f.gclid);var g,h;c.push((h=(g=f.Ma)==null?void 0:g.get())!=null?h:0);
for(var m=d.push,n=0,p=k(f.zb||[0]),q=p.next();!q.done;q=p.next()){var r=q.value;r>0&&(n|=1<<r-1)}m.call(d,n.toString())});b.length>0&&W(a,L.m.nb,b.join("."));this.J||(c.length>0&&W(a,L.m.Fe,c.join(".")),d.length>0&&W(a,L.m.Ge,d.join(".")))}else{var e=this.R.map(function(f){return f.gclid}).join(".");e&&W(a,L.m.kc,e)}};
var qw=function(a,b){var c=a&&!sp([L.m.V,L.m.W]);return b&&c?"0":b},tw=function(a){var b=a.Qc===void 0?{}:a.Qc,c=iu(b.prefix);$u(c)&&vp(function(){function d(x,z,B){var C=sp([L.m.V,L.m.W]),F=m&&C,G=b.prefix||"_gcl",I=rw(),K=(F?G:"")+"."+(sp(L.m.V)?1:0)+"."+(sp(L.m.W)?1:0);if(!I[K]){I[K]=!0;var U={},Q=function(ka,ja){if(ja||typeof ja==="number")U[ka]=ja.toString()},na="https://www.google.com";Fr()&&(Q("gcs",Gr()),x&&Q("gcu",1));Q("gcd",Kr(h));fk()&&Q("tag_exp",fk());if(jn()){Q("rnd",nv());if((!p||
q&&q!=="aw.ds")&&C){var T=eu(G+"_aw");Q("gclaw",T.join("."))}Q("url",String(l.location).split(/[?#]/)[0]);Q("dclid",qw(f,r));C||(na="https://pagead2.googlesyndication.com")}Nr()&&Q("dma_cps",Lr());Q("dma",Mr());Q("npa",Er(h)?0:1);Pr()&&Q("_ng",1);ir(qr())&&Q("tcfd",Or());Q("gdpr_consent",wr()||"");Q("gdpr",xr()||"");Ys(!1)._up==="1"&&Q("gtm_up",1);Q("gclid",qw(f,p));Q("gclsrc",q);if(!(U.hasOwnProperty("gclid")||U.hasOwnProperty("dclid")||U.hasOwnProperty("gclaw"))&&(Q("gbraid",qw(f,t)),!U.hasOwnProperty("gbraid")&&
jn()&&C)){var aa=eu(G+"_gb");aa.length>0&&Q("gclgb",aa.join("."))}Q("gtm",Sr({Oa:h.eventMetadata[P.C.kb],th:!g}));m&&sp(L.m.V)&&(lt(b||{}),F&&Q("auid",jt[mt(b.prefix)]||""));sw||a.fm&&Q("did",a.fm);a.kj&&Q("gdid",a.kj);a.gj&&Q("edid",a.gj);a.oj!==void 0&&Q("frm",a.oj);E(23)&&Q("apve","0");var Y=Object.keys(U).map(function(ka){return ka+"="+encodeURIComponent(U[ka])}),V=na+"/pagead/landing?"+Y.join("&");Jc(V);v&&g!==void 0&&ep({targetId:g,request:{url:V,parameterEncoding:3,endpoint:C?12:13},ab:{eventId:h.eventId,
priorityId:h.priorityId},wh:z===void 0?void 0:{eventId:z,priorityId:B}})}}var e=!!a.aj,f=!!a.ye,g=a.targetId,h=a.F,m=a.Dh===void 0?!0:a.Dh,n=Au(),p=n.gclid||"",q=n.gclsrc,r=n.dclid||"",t=n.wbraid||"",u=!e&&((!p||q&&q!=="aw.ds"?!1:!0)||t),v=jn();if(u||v)if(v){var w=[L.m.V,L.m.W,L.m.Na];d();(function(){sp(w)||up(function(x){d(!0,x.consentEventId,x.consentPriorityId)},w)})()}else d()},[L.m.V,L.m.W,L.m.Na])},rw=function(){return Dp("reported_gclid",function(){return{}})},sw=!1;function uw(a,b,c,d){var e=xc(),f;if(e===1)a:{var g=Zj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=y.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==l.location.protocol?a:b)+c};
var zw=function(a,b){if(a)if(Qr()){}else if(a=fb(a)?Op(Im(a)):Op(Im(a.id))){var c=void 0,d=!1,e=O(b,L.m.Tn);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Op(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=O(b,L.m.Vk),m;if(h){m=Array.isArray(h)?h:[h];var n=O(b,L.m.Tk),p=O(b,L.m.Uk),q=O(b,L.m.Wk),r=No(O(b,L.m.Sn)),t=n||p,u=1;a.prefix!==
"UA"||c||(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)vw(c,m[v],r,b,{Bc:t,options:q});else if(a.prefix==="AW"&&a.ids[Qp[1]])E(155)?vw([a],m[v],r||"US",b,{Bc:t,options:q}):ww(a.ids[Qp[0]],a.ids[Qp[1]],m[v],b,{Bc:t,options:q});else if(a.prefix==="UA")if(E(155))vw([a],m[v],r||"US",b,{Bc:t});else{var w=a.destinationId,x=m[v],z={Bc:t};N(23);if(x){z=z||{};var B=xw(yw,z,w),C={};z.Bc!==void 0?C.receiver=z.Bc:C.replace=x;C.ga_wpid=w;C.destination=x;B(2,tb(),C)}}}}}},vw=function(a,b,c,d,e){N(21);if(b&&c){e=
e||{};for(var f={countryNameCode:c,destinationNumber:b,retrievalTime:tb()},g=0;g<a.length;g++){var h=a[g];Aw[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Qp[0]],cl:h.ids[Qp[1]]},Bw(f.adData,d),Aw[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},Aw[h.id]=!0))}(f.gaData||f.adData)&&xw(Cw,e,void 0,d)(e.Bc,f,e.options)}},ww=function(a,b,c,d,e){N(22);if(c){e=e||{};var f=xw(Dw,e,a,d),g={ak:a,cl:b};e.Bc===void 0&&(g.autoreplace=c);Bw(g,d);f(2,e.Bc,
g,c,0,tb(),e.options)}},Bw=function(a,b){a.dma=Mr();Nr()&&(a.dmaCps=Lr());Er(b)?a.npa="0":a.npa="1"},xw=function(a,b,c,d){if(l[a.functionName])return b.Aj&&A(b.Aj),l[a.functionName];var e=Ew();l[a.functionName]=e;if(a.additionalQueues)for(var f=0;f<a.additionalQueues.length;f++)l[a.additionalQueues[f]]=l[a.additionalQueues[f]]||Ew();a.idKey&&l[a.idKey]===void 0&&(l[a.idKey]=c);gm({destinationId:cg.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},uw("https://",
"http://",a.scriptUrl),b.Aj,b.Vp);return e},Ew=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},Dw={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},yw={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},Fw={Um:"9",zo:"5"},Cw={functionName:"_googCallTrackingImpl",additionalQueues:[yw.functionName,Dw.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+
(Fw.Um||Fw.zo)+".js"},Aw={};function Gw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Qv(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Qv(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.F,b)},yb:function(){return a},getHitKeys:function(){return Object.keys(a.D)},getMergedValues:function(b){return a.F.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return cd(c)?a.mergeHitDataForKey(b,c):!1}}};var Iw=function(a){var b=Hw[tm?a.target.destinationId:Im(a.target.destinationId)];if(!a.isAborted&&b)for(var c=Gw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Jw=function(a,b){var c=Hw[a];c||(c=Hw[a]=[]);c.push(b)},Hw={};var Kw=function(a){if(sp(L.m.V)){a=a||{};lt(a,!1);var b,c=iu(a.prefix);if((b=kt[mt(c)])&&!(ub()-b.Fh*1E3>18E5)){var d=b.id,e=d.split(".");if(e.length===2&&!(ub()-(Number(e[1])||0)*1E3>864E5))return d}}};function Lw(a,b){return arguments.length===1?Mw("set",a):Mw("set",a,b)}function Nw(a,b){return arguments.length===1?Mw("config",a):Mw("config",a,b)}function Ow(a,b,c){c=c||{};c[L.m.kd]=a;return Mw("event",b,c)}function Mw(){return arguments};var Pw=function(){var a=kc&&kc.userAgent||"";if(a.indexOf("Safari")<0||/Chrome|Coast|Opera|Edg|Silk|Android/.test(a))return!1;var b=(/Version\/([\d\.]+)/.exec(a)||[])[1]||"";if(b==="")return!1;for(var c=["14","1","1"],d=b.split("."),e=0;e<d.length;e++){if(c[e]===void 0)return!0;if(d[e]!==c[e])return Number(d[e])>Number(c[e])}return d.length>=c.length};var Qw=function(){this.messages=[];this.D=[]};Qw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};Qw.prototype.listen=function(a){this.D.push(a)};
Qw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Qw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Rw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.C.kb]=cg.canonicalContainerId;Sw().enqueue(a,b,c)}
function Tw(){var a=Uw;Sw().listen(a)}function Sw(){return Dp("mb",function(){return new Qw})};var Vw,Ww=!1;function Xw(){Ww=!0;Vw=Vw||{}}function Yw(a){Ww||Xw();return Vw[a]};function Zw(){var a=l.screen;return{width:a?a.width:0,height:a?a.height:0}}
function $w(a){if(y.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!l.getComputedStyle)return!0;var c=l.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=l.getComputedStyle(d,null))}return!1}
var jx=function(a){return a.tagName+":"+a.isVisible+":"+a.ma.length+":"+ix.test(a.ma)},xx=function(a){a=a||{ve:!0,we:!0,Jh:void 0};a.Wb=a.Wb||{email:!0,phone:!1,address:!1};var b=kx(a),c=lx[b];if(c&&ub()-c.timestamp<200)return c.result;var d=mx(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Wb&&a.Wb.email){var n=nx(d.elements);f=ox(n,a&&a.Uf);g=px(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(qx(f[p],!!a.ve,!!a.we));m=m.slice(0,10)}else if(a.Wb){}g&&(h=qx(g,!!a.ve,!!a.we));var F={elements:m,
Ej:h,status:e};lx[b]={timestamp:ub(),result:F};return F},yx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Ax=function(a){var b=zx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},zx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},wx=function(a,b,c){var d=a.element,e={ma:a.ma,type:a.xa,tagName:d.tagName};b&&(e.querySelector=Bx(d));c&&(e.isVisible=!$w(d));return e},qx=function(a,b,c){return wx({element:a.element,ma:a.ma,xa:vx.fc},b,c)},kx=function(a){var b=!(a==null||!a.ve)+"."+!(a==null||!a.we);a&&a.Uf&&a.Uf.length&&(b+="."+a.Uf.join("."));a&&a.Wb&&(b+="."+a.Wb.email+"."+a.Wb.phone+"."+a.Wb.address);return b},px=function(a){if(a.length!==0){var b;b=Cx(a,function(c){return!Dx.test(c.ma)});b=Cx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Cx(b,function(c){return!$w(c.element)});return b[0]}},ox=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&pi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Cx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Bx=function(a){var b;if(a===y.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Bx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},nx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Ex);if(f){var g=f[0],h;if(l.location){var m=Kk(l.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ma:g})}}}return b},mx=function(){var a=[],b=y.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Fx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Gx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Hx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Ex=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,ix=/@(gmail|googlemail)\./i,Dx=/support|noreply/i,Fx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Gx=
["BR"],Ix=ng('',2),vx={fc:"1",xd:"2",pd:"3",wd:"4",De:"5",Kf:"6",mh:"7",Qi:"8",Mh:"9",Li:"10"},lx={},Hx=["INPUT","SELECT"],Jx=zx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var Zf;var my=Number('')||5,ny=Number('')||50,oy=kb();
var qy=function(a,b){a&&(py("sid",a.targetId,b),py("cc",a.clientCount,b),py("tl",a.totalLifeMs,b),py("hc",a.heartbeatCount,b),py("cl",a.clientLifeMs,b))},py=function(a,b,c){b!=null&&c.push(a+"="+b)},ry=function(){var a=y.referrer;if(a){var b;return Ik(Ok(a),"host")===((b=l.location)==null?void 0:b.host)?1:2}return 0},sy="https://"+Li(21,"www.googletagmanager.com")+"/a?",uy=function(){this.T=ty;this.O=0};uy.prototype.J=function(a,b,c,d){var e=ry(),f,
g=[];f=l===l.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&py("si",a.gg,g);py("m",0,g);py("iss",f,g);py("if",c,g);qy(b,g);d&&py("fm",encodeURIComponent(d.substring(0,ny)),g);this.R(g);};uy.prototype.D=function(a,b,c,d,e){var f=[];py("m",1,f);py("s",a,f);py("po",ry(),f);b&&(py("st",b.state,f),py("si",b.gg,f),py("sm",b.qg,f));qy(c,f);py("c",d,f);e&&py("fm",encodeURIComponent(e.substring(0,
ny)),f);this.R(f);};uy.prototype.R=function(a){a=a===void 0?[]:a;!dl||this.O>=my||(py("pid",oy,a),py("bc",++this.O,a),a.unshift("ctid="+cg.ctid+"&t=s"),this.T(""+sy+a.join("&")))};var vy=Number('')||500,wy=Number('')||5E3,xy=Number('20')||10,yy=Number('')||5E3;function zy(a){return a.performance&&a.performance.now()||Date.now()}
var Ay=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{sm:function(){},tm:function(){},rm:function(){},onFailure:function(){}}:g;this.Do=e;this.D=f;this.O=g;this.ia=this.la=this.heartbeatCount=this.Co=0;this.nh=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=zy(this.D);this.qg=zy(this.D);this.T=10};d.prototype.init=function(){this.R(1);this.Ca()};d.prototype.getState=function(){return{state:this.state,
gg:Math.round(zy(this.D)-this.gg),qg:Math.round(zy(this.D)-this.qg)}};d.prototype.R=function(e){this.state!==e&&(this.state=e,this.qg=zy(this.D))};d.prototype.Rl=function(){return String(this.Co++)};d.prototype.Ca=function(){var e=this;this.heartbeatCount++;this.Za({type:0,clientId:this.id,requestId:this.Rl(),maxDelay:this.qh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ia++,f.isDead||e.ia>xy){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.R(4);e.Ao();var m,n;(n=(m=e.O).rm)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.R(3),e.Tl();else{if(e.heartbeatCount>f.stats.heartbeatCount+xy){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.R(2);if(r!==2)if(e.nh){var t,u;(u=(t=e.O).tm)==null||u.call(t)}else{e.nh=!0;var v,w;(w=(v=e.O).sm)==null||w.call(v)}e.ia=0;e.Eo();e.Tl()}}})};d.prototype.qh=function(){return this.state===2?
wy:vy};d.prototype.Tl=function(){var e=this;this.D.setTimeout(function(){e.Ca()},Math.max(0,this.qh()-(zy(this.D)-this.la)))};d.prototype.Ho=function(e,f,g){var h=this;this.Za({type:1,clientId:this.id,requestId:this.Rl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.Za=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.If(r,7)},(n=e.maxDelay)!=null?n:yy),q={request:e,Fm:f,Am:h,Sp:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.la=zy(this.D);e.Am=!1;this.Do(e.request)};d.prototype.Eo=function(){for(var e=k(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.Am&&this.sendRequest(g)}};d.prototype.Ao=function(){for(var e=
k(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.If(this.J[f.value],this.T)};d.prototype.If=function(e,f){this.Eb(e);var g=e.request;g.failure={failureType:f};e.Fm(g)};d.prototype.Eb=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.Sp)};d.prototype.Ap=function(e){this.la=zy(this.D);var f=this.J[e.requestId];if(f)this.Eb(f),f.Fm(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,l,b);return c};var By;
var Cy=function(){By||(By=new uy);return By},ty=function(a){vn(xn(Xm.Z.Nc),function(){Ac(a)})},Dy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ey=function(a){var b=a,c=Ij.Ca;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Fy=function(a){var b=fo(ao.aa.Hl);return b&&b[a]},Gy=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.R=!1;this.ia=null;this.initTime=c;this.D=15;this.O=this.Qo(a);l.setTimeout(function(){f.initialize()},1E3);A(function(){f.Kp(a,b,e)})};ba=Gy.prototype;ba.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),gg:this.initTime,qg:Math.round(ub())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.Ho(a,b,c)};ba.getState=function(){return this.O.getState().state};ba.Kp=function(a,b,c){var d=l.location.origin,e=this,
f=yc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Dy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});yc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.Ap(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};ba.Qo=function(a){var b=this,c=Ay(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{sm:function(){b.R=!0;b.J.J(c.getState(),c.stats)},tm:function(){},rm:function(d){b.R?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};ba.initialize=function(){this.T||this.O.init();this.T=!0};function Hy(){var a=bg(Zf.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Iy(a,b){var c=Math.round(ub());b=b===void 0?!1:b;var d=l.location.origin;if(!d||!Hy()||E(168))return;hk()&&(a=""+d+gk()+"/_/service_worker");var e=Ey(a);if(e===null||Fy(e.origin))return;if(!lc()){Cy().J(void 0,void 0,6);return}var f=new Gy(e,!!a,c||Math.round(ub()),Cy(),b);go(ao.aa.Hl)[e.origin]=f;}
var Jy=function(a,b,c,d){var e;if((e=Fy(a))==null||!e.delegate){var f=lc()?16:6;Cy().D(f,void 0,void 0,b.commandType);d({failureType:f});return}Fy(a).delegate(b,c,d);};
function Ky(a,b,c,d,e){var f=Ey();if(f===null){d(lc()?16:6);return}var g,h=(g=Fy(f.origin))==null?void 0:g.initTime,m=Math.round(ub()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Jy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ly(a,b,c,d){var e=Ey(a);if(e===null){d("_is_sw=f"+(lc()?16:6)+"te");return}var f=b?1:0,g=Math.round(ub()),h,m=(h=Fy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Jy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:l.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Fy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function My(a){if(E(10)||hk()||Ij.O||Wk(a.F)||E(168))return;Iy(void 0,E(131));};var Ny="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Oy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Py(){var a=l.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Qy(){var a,b;return(b=(a=l.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Ry(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Sy(){var a=l;if(!Ry(a))return null;var b=Oy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Ny).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Uy=function(a,b){if(a)for(var c=Ty(a),d=k(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}},Ty=function(a){var b={};b[L.m.qf]=a.architecture;b[L.m.rf]=a.bitness;a.fullVersionList&&(b[L.m.tf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[L.m.uf]=a.mobile?"1":"0";b[L.m.vf]=a.model;b[L.m.wf]=a.platform;b[L.m.xf]=a.platformVersion;b[L.m.yf]=a.wow64?"1":"0";return b},Vy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=Py();if(d)c(d);else{var e=Qy();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=l.setTimeout(function(){c.hg||(c.hg=!0,N(106),c(null,Error("Timeout")))},b);e.then(function(g){c.hg||(c.hg=!0,N(104),l.clearTimeout(f),c(g))}).catch(function(g){c.hg||(c.hg=!0,N(105),l.clearTimeout(f),c(null,g))})}else c(null)}},Xy=function(){if(Ry(l)&&(Wy=ub(),!Qy())){var a=Sy();a&&(a.then(function(){N(95)}),a.catch(function(){N(96)}))}},Wy;function Yy(a){var b=a.location.href;if(a===a.top)return{url:b,Pp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Pp:c}};
var Zy=function(){return[L.m.V,L.m.W]},$y=function(a){E(24)&&a.eventName===L.m.ra&&Zv(a,M.K.Ia)&&!R(a,P.C.ka)&&!a.F.isGtmEvent?zw(a.target,a.F):Zv(a,M.K.Uj)&&(zw(a.target,a.F),a.isAborted=!0)},bz=function(a){var b;if(a.eventName!=="gtag.config"&&R(a,P.C.Gl))switch(R(a,P.C.fa)){case M.K.La:b=97;az(a);break;case M.K.Ua:b=98;az(a);break;case M.K.X:b=99}!R(a,P.C.Ja)&&b&&N(b);R(a,P.C.Ja)===!0&&(a.isAborted=!0)},cz=function(a){if(!R(a,P.C.ka)&&E(30)&&Zv(a,[M.K.X])){var b=rv();qv(b)&&(W(a,L.m.jd,"1"),S(a,
P.C.vg,!0))}},dz=function(a){Zv(a,[M.K.X])&&a.F.eventMetadata[P.C.ud]&&W(a,L.m.ql,!0)},ez=function(a){var b=sp(Zy());switch(R(a,P.C.fa)){case M.K.Ua:case M.K.La:a.isAborted=!b||!!R(a,P.C.ka);break;case M.K.oa:a.isAborted=!b;break;case M.K.X:R(a,P.C.ka)&&W(a,L.m.ka,!0)}},fz=function(a,b){if((Ij.D||E(168))&&sp(Zy())&&(!E(13)||!jw(a,"ccd_enable_cm",!1))){var c=function(m){var n=R(a,P.C.eh);n?n.push(m):S(a,P.C.eh,[m])};E(62)&&c(102696396);if(E(63)||E(168)){c(102696397);var d=R(a,P.C.Ta);S(a,P.C.ih,!0);
S(a,P.C.ce,!0);if(Yi(d)){c(102780931);S(a,P.C.Hi,!0);var e=b||ts(),f={},g={eventMetadata:(f[P.C.sd]=M.K.La,f[P.C.Ta]=d,f[P.C.Ql]=e,f[P.C.ce]=!0,f[P.C.ih]=!0,f[P.C.Hi]=!0,f[P.C.eh]=[102696397,102780931],f),noGtmEvent:!0},h=Ow(a.target.destinationId,a.eventName,a.F.D);Rw(h,a.F.eventId,g);S(a,P.C.Ta);return e}}}},gz=function(a){if(Zv(a,[M.K.X])){var b=R(a,P.C.ya),c=Kw(b),d=fz(a,c),e=c||d;if(e&&!Qv(a,L.m.Xa)){var f=ts(Qv(a,L.m.mc));W(a,L.m.Xa,f);Ya("GTAG_EVENT_FEATURE_CHANNEL",12)}e&&(W(a,L.m.sc,e),S(a,
P.C.Fl,!0))}},hz=function(a){My(a)},iz=function(a){if(Zv(a,[M.K.X,M.K.oa,M.K.Ua,M.K.La])&&R(a,P.C.Vc)&&sp(L.m.V)){var b=R(a,P.C.fa)===M.K.oa,c=!E(4);if(!b||c){var d=R(a,P.C.fa)===M.K.X&&a.eventName!==L.m.Ab,e=R(a,P.C.ya);lt(e,d);sp(L.m.W)&&W(a,L.m.Mb,jt[mt(e.prefix)])}}},jz=function(a){Zv(a,[M.K.X,M.K.Ua,M.K.La])&&pw(a)},kz=function(a){Zv(a,[M.K.X])&&S(a,P.C.je,!!R(a,P.C.wc)&&!sp(Zy()))},lz=function(a){Zv(a,[M.K.X])&&Ys(!1)._up==="1"&&W(a,L.m.Ug,!0)},mz=function(a){if(Zv(a,[M.K.X,M.K.oa])){var b=
Nv();b!==void 0&&W(a,L.m.zf,b||"error");var c=xr();c&&W(a,L.m.hd,c);var d=wr();d&&W(a,L.m.md,d)}},nz=function(a){if(Zv(a,[M.K.X,M.K.oa])&&l.__gsaExp&&l.__gsaExp.id){var b=l.__gsaExp.id;if(eb(b))try{var c=Number(b());isNaN(c)||W(a,L.m.Mk,c)}catch(d){}}},oz=function(a){Iw(a);},pz=function(a){E(47)&&Zv(a,M.K.X)&&(a.copyToHitData(L.m.Xh),a.copyToHitData(L.m.Yh),a.copyToHitData(L.m.Wh))},qz=function(a){Zv(a,M.K.X)&&(a.copyToHitData(L.m.hf),
a.copyToHitData(L.m.Te),a.copyToHitData(L.m.pf),a.copyToHitData(L.m.Og),a.copyToHitData(L.m.Vd),a.copyToHitData(L.m.We))},rz=function(a){if(Zv(a,[M.K.X,M.K.oa,M.K.Ua,M.K.La])){var b=a.F;if(Zv(a,[M.K.X,M.K.oa])){var c=O(b,L.m.Sb);c!==!0&&c!==!1||W(a,L.m.Sb,c)}Er(b)?W(a,L.m.vc,!1):(W(a,L.m.vc,!0),Zv(a,M.K.oa)&&(a.isAborted=!0))}},sz=function(a){if(Zv(a,[M.K.X,M.K.oa])){var b=R(a,P.C.fa)===M.K.X;b&&a.eventName!==L.m.mb||(a.copyToHitData(L.m.wa),b&&(a.copyToHitData(L.m.Ig),a.copyToHitData(L.m.Gg),a.copyToHitData(L.m.Hg),
a.copyToHitData(L.m.Fg),W(a,L.m.rk,a.eventName),E(113)&&(a.copyToHitData(L.m.ff),a.copyToHitData(L.m.df),a.copyToHitData(L.m.ef))))}},tz=function(a){var b=a.F;if(!E(6)){var c=b.getMergedValues(L.m.qa);W(a,L.m.Vg,Db(cd(c)?c:{}))}var d={};E(167)&&(d=Lo(Oq.D[L.m.qa]));var e=b.getMergedValues(L.m.qa,1,d),f=b.getMergedValues(L.m.qa,2);W(a,L.m.Rb,Db(cd(e)?e:{},"."));W(a,L.m.Qb,Db(cd(f)?f:{},"."))},uz=function(a){if(a!=null){var b=String(a).substring(0,512),c=b.indexOf("#");return c===-1?b:b.substring(0,
c)}return""},vz=function(a){Zv(a,M.K.X)&&sp(L.m.V)&&lw(a)},wz=function(a){if(a.eventName===L.m.Ab&&!a.F.isGtmEvent){if(!R(a,P.C.ka)&&Zv(a,M.K.X)){var b=O(a.F,L.m.Hc);if(typeof b!=="function")return;var c=String(O(a.F,L.m.oc)),d=Qv(a,c),e=O(a.F,c);c===L.m.nb||c===L.m.Mb?Yv({Qm:c,callback:b,om:e},R(a,P.C.ya),R(a,P.C.wc),mv):b(d||e)}a.isAborted=!0}},xz=function(a){if(!jw(a,"hasPreAutoPiiCcdRule",!1)&&Zv(a,M.K.X)&&sp(L.m.V)){var b=O(a.F,L.m.Ng)||{},c=String(Qv(a,L.m.mc)),d=b[c],e=Qv(a,L.m.Se),f;if(!(f=
Bk(d)))if(vo()){var g=Yw("AW-"+e);f=!!g&&!!g.preAutoPii}else f=!1;if(f){var h=ub(),m=xx({ve:!0,we:!0,Jh:!0});if(m.elements.length!==0){for(var n=[],p=0;p<m.elements.length;++p){var q=m.elements[p];n.push(q.querySelector+"*"+jx(q)+"*"+q.type)}W(a,L.m.ui,n.join("~"));var r=m.Ej;r&&(W(a,L.m.wi,r.querySelector),W(a,L.m.si,jx(r)));W(a,L.m.ri,String(ub()-h));W(a,L.m.xi,m.status)}}}},yz=function(a){if(a.eventName===L.m.ra&&!R(a,P.C.ka)&&(S(a,P.C.jo,!0),Zv(a,M.K.X)&&S(a,P.C.Ja,!0),Zv(a,M.K.oa)&&(O(a.F,L.m.Zc)===
!1||O(a.F,L.m.rb)===!1)&&S(a,P.C.Ja,!0),Zv(a,M.K.Ji))){var b=O(a.F,L.m.Ra)||{},c=O(a.F,L.m.Cb),d=R(a,P.C.Vc),e=R(a,P.C.kb),f=R(a,P.C.wc),g={pe:d,xe:b,Ae:c,Oa:e,F:a.F,ye:f,Pm:O(a.F,L.m.Sa)},h=R(a,P.C.ya);Tv(g,h);zw(a.target,a.F);var m={aj:!1,ye:f,targetId:a.target.id,F:a.F,Qc:d?h:void 0,Dh:d,fm:Qv(a,L.m.Vg),kj:Qv(a,L.m.Rb),gj:Qv(a,L.m.Qb),oj:Qv(a,L.m.Ic)};tw(m);a.isAborted=!0}},zz=function(a){Zv(a,[M.K.X,M.K.oa])&&(a.F.isGtmEvent?R(a,P.C.fa)!==M.K.X&&a.eventName&&W(a,L.m.gd,a.eventName):W(a,L.m.gd,
a.eventName),nb(a.F.D,function(b,c){mi[b.split(".")[0]]||W(a,b,c)}))},Az=function(a){if(!R(a,P.C.ih)){var b=!R(a,P.C.Gl)&&Zv(a,[M.K.X,M.K.La]),c=!jw(a,"ccd_add_1p_data",!1)&&Zv(a,M.K.Ua);if((b||c)&&sp(L.m.V)){var d=R(a,P.C.fa)===M.K.X,e=a.F,f=void 0,g=O(e,L.m.Ya);if(d){var h=O(e,L.m.Eg)===!0,m=O(e,L.m.Ng)||{},n=String(Qv(a,L.m.mc)),p=m[n];if(a.F.isGtmEvent&&p===void 0&&!tm)return;if(h||p){var q;var r;p?r=yk(p,g):(r=l.enhanced_conversion_data)&&Ya("GTAG_EVENT_FEATURE_CHANNEL",8);var t=(p||{}).enhanced_conversions_mode,
u;if(r){if(t==="manual")switch(r._tag_mode){case "CODE":u="c";break;case "AUTO":u="a";break;case "MANUAL":u="m";break;default:u="c"}else u=t==="automatic"?Bk(p)?"a":"m":"c";q={ma:r,Om:u}}else q={ma:r,Om:void 0};var v=q,w=v.Om;f=v.ma;W(a,L.m.Tb,w)}}else if(tm&&a.F.isGtmEvent){az(a);S(a,P.C.Ta,g);W(a,L.m.Tb,Ck(g));return}S(a,P.C.Ta,f)}}},Bz=function(a){if(jw(a,"ccd_add_1p_data",!1)&&sp(Zy())){var b=a.F.J[L.m.ah];if(zk(b)){var c=O(a.F,L.m.Ya);if(c===null)S(a,P.C.me,null);else if(b.enable_code&&cd(c)&&
S(a,P.C.me,c),cd(b.selectors)){var d={};S(a,P.C.sh,xk(b.selectors,d));E(60)&&a.mergeHitDataForKey(L.m.qc,{ec_data_layer:uk(d)})}}}},Cz=function(a){S(a,P.C.Vc,O(a.F,L.m.Qa)!==!1);S(a,P.C.ya,Ov(a));S(a,P.C.wc,O(a.F,L.m.za)!=null&&O(a.F,L.m.za)!==!1);S(a,P.C.Lh,Er(a.F))},Dz=function(a){if(Zv(a,[M.K.X,M.K.oa])&&!E(189)&&E(34)){var b=function(d){return E(35)?(Ya("fdr",d),!0):!1};if(sp(L.m.V)||b(0))if(sp(L.m.W)||b(1))if(O(a.F,L.m.ob)!==!1||b(2))if(Er(a.F)||b(3))if(O(a.F,L.m.Zc)!==!1||b(4)){var c;E(36)?
c=a.eventName===L.m.ra?O(a.F,L.m.rb):void 0:c=O(a.F,L.m.rb);if(c!==!1||b(5))if(Jl()||b(6))E(35)&&cb()?(W(a,L.m.yk,ab("fdr")),delete Wa.fdr):(W(a,L.m.zk,"1"),S(a,P.C.oh,!0))}}},Ez=function(a){Zv(a,[M.K.X])&&sp(L.m.W)&&(l._gtmpcm===!0||Pw()?W(a,L.m.bd,"2"):E(39)&&Il("attribution-reporting")&&W(a,L.m.bd,"1"))},Fz=function(a){if(!Ry(l))N(87);else if(Wy!==void 0){N(85);var b=Py();b?Uy(b,a):N(86)}},Gz=function(a){if(Zv(a,[M.K.X,M.K.oa,M.K.Ia,M.K.Ua,M.K.La])&&sp(L.m.W)){a.copyToHitData(L.m.Sa);var b=fo(ao.aa.Jl);
if(b===void 0)eo(ao.aa.Kl,!0);else{var c=fo(ao.aa.ph);W(a,L.m.nf,c+"."+b)}}},Hz=function(a){Zv(a,[M.K.X,M.K.oa])&&(a.copyToHitData(L.m.Xa),a.copyToHitData(L.m.Ga),a.copyToHitData(L.m.Va))},Iz=function(a){if(!R(a,P.C.ka)&&Zv(a,[M.K.X,M.K.oa])){var b=Fl(!1);W(a,L.m.Ic,b);var c=O(a.F,L.m.Ba);c||(c=b===1?l.top.location.href:l.location.href);W(a,L.m.Ba,uz(c));a.copyToHitData(L.m.Wa,y.referrer);W(a,L.m.Bb,Rv());a.copyToHitData(L.m.xb);var d=Zw();W(a,L.m.Mc,d.width+"x"+d.height);var e=Hl(),f=Yy(e);f.url&&
c!==f.url&&W(a,L.m.ni,uz(f.url))}},Jz=function(a){Zv(a,[M.K.X,M.K.oa])},Kz=function(a){if(Zv(a,[M.K.X,M.K.oa,M.K.Ua,M.K.La])){var b=Qv(a,L.m.mc),c=O(a.F,L.m.Th)===!0;c&&S(a,P.C.wo,!0);switch(R(a,P.C.fa)){case M.K.X:!c&&b&&az(a);(Ak()||sc())&&S(a,P.C.de,!0);(Ak()||sc()?0:E(157))&&S(a,P.C.Fi,!0);break;case M.K.Ua:case M.K.La:!c&&b&&(a.isAborted=!0);break;case M.K.oa:!c&&b||az(a)}Zv(a,[M.K.X,M.K.oa])&&(R(a,P.C.de)?W(a,L.m.Bi,"www.google.com"):W(a,L.m.Bi,"www.googleadservices.com"))}},Lz=function(a){var b=
a.target.ids[Qp[0]];if(b){W(a,L.m.Se,b);var c=a.target.ids[Qp[1]];c&&W(a,L.m.mc,c)}else a.isAborted=!0},az=function(a){R(a,P.C.Ll)||S(a,P.C.Ja,!1)};function Pz(a,b){var c=!!hk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?gk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(187)?Mz()?Nz():""+gk()+"/ag/g/c":Mz().toLowerCase()==="region1"?""+gk()+"/r1ag/g/c":""+gk()+"/ag/g/c":Nz();case 16:if(c){if(E(187))return Mz()?Oz():
""+gk()+"/ga/g/c";var d=Mz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+gk()+d}return Oz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?gk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?gk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Io+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?gk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return E(180)?c&&b.Cd?gk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion":c?b.Cd?gk()+"/as/d/ccm/conversion":gk()+"/as/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?gk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return E(180)?c&&b.Cd?gk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion":c?b.Cd?gk()+"/g/d/ccm/conversion":gk()+"/g/ccm/conversion":"https://www.google.com/ccm/conversion";case 21:return E(180)?c&&b.Cd?gk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data":c?b.Cd?gk()+"/d/ccm/form-data":gk()+"/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 55:case 27:case 30:case 36:case 54:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:bc(a,"Unknown endpoint")}};function Qz(a){a=a===void 0?[]:a;return Jj(a).join("~")}function Rz(){if(!E(118))return"";var a,b;return(((a=Gm(Hm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Sz(a,b){b&&nb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Uz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=k(Object.keys(a.D)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Qv(a,g),m=Tz[g];m&&h!==void 0&&h!==""&&(!R(a,P.C.je)||g!==L.m.Yc&&g!==L.m.ed&&g!==L.m.Ud&&g!==L.m.Je||(h="0"),d(m,h))}d("gtm",Sr({Oa:R(a,P.C.kb)}));Fr()&&d("gcs",Gr());d("gcd",Kr(a.F));Nr()&&d("dma_cps",Lr());d("dma",Mr());ir(qr())&&d("tcfd",Or());Qz()&&d("tag_exp",Qz());Rz()&&d("ptag_exp",Rz());if(R(a,P.C.vg)){d("tft",
ub());var n=Qc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Nc()?E(26)?"f":"sb":"nf");pn[Xm.Z.Ea]!==Wm.Ka.fe||sn[Xm.Z.Ea].isConsentGranted()||(c.limited_ads="1");b(c)},Vz=function(a,b,c){var d=b.F;ep({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},ab:{eventId:d.eventId,priorityId:d.priorityId},wh:{eventId:R(b,P.C.Be),priorityId:R(b,P.C.Ce)}})},Wz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.F.eventId,
priorityId:b.F.priorityId};Vz(a,b,c);fm(d,a,void 0,{Hh:!0,method:"GET"},function(){},function(){em(d,a+"&img=1")})},Xz=function(a){var b=sc()||qc()?"www.google.com":"www.googleadservices.com",c=[];nb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Yz=function(a){Uz(a,function(b){if(R(a,P.C.fa)===M.K.Ia){var c=[];E(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
nb(b,function(r,t){c.push(r+"="+t)});var d=sp([L.m.V,L.m.W])?45:46,e=Pz(d)+"?"+c.join("&");Vz(e,a,d);var f=a.F,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Nc()){fm(g,e,void 0,{Hh:!0},function(){},function(){em(g,e+"&img=1")});var h=sp([L.m.V,L.m.W]),m=Qv(a,L.m.jd)==="1",n=Qv(a,L.m.Vh)==="1";if(h&&m&&!n){var p=Xz(b),q=sc()||qc()?58:57;Wz(p,a,q)}}else dm(g,e)||em(g,e+"&img=1");if(eb(a.F.onSuccess))a.F.onSuccess()}})},Zz={},Tz=(Zz[L.m.ka]="gcu",
Zz[L.m.kc]="gclgb",Zz[L.m.nb]="gclaw",Zz[L.m.He]="gad_source",Zz[L.m.Ie]="gad_source_src",Zz[L.m.Yc]="gclid",Zz[L.m.qk]="gclsrc",Zz[L.m.Je]="gbraid",Zz[L.m.Ud]="wbraid",Zz[L.m.Mb]="auid",Zz[L.m.sk]="rnd",Zz[L.m.Vh]="ncl",Zz[L.m.Zh]="gcldc",Zz[L.m.ed]="dclid",Zz[L.m.Qb]="edid",Zz[L.m.gd]="en",Zz[L.m.hd]="gdpr",Zz[L.m.Rb]="gdid",Zz[L.m.Yd]="_ng",Zz[L.m.Ze]="gpp_sid",Zz[L.m.af]="gpp",Zz[L.m.bf]="_tu",Zz[L.m.Nk]="gtm_up",Zz[L.m.Ic]="frm",Zz[L.m.jd]="lps",Zz[L.m.Vg]="did",Zz[L.m.Qk]="navt",Zz[L.m.Ba]=
"dl",Zz[L.m.Wa]="dr",Zz[L.m.Bb]="dt",Zz[L.m.Xk]="scrsrc",Zz[L.m.nf]="ga_uid",Zz[L.m.md]="gdpr_consent",Zz[L.m.mi]="u_tz",Zz[L.m.Sa]="uid",Zz[L.m.zf]="us_privacy",Zz[L.m.vc]="npa",Zz);var $z={};$z.P=Vr.P;var aA={Vq:"L",yo:"S",mr:"Y",Eq:"B",Oq:"E",Sq:"I",jr:"TC",Rq:"HTC"},bA={yo:"S",Nq:"V",Hq:"E",ir:"tag"},cA={},dA=(cA[$z.P.Si]="6",cA[$z.P.Ti]="5",cA[$z.P.Ri]="7",cA);function eA(){function a(c,d){var e=ab(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var fA=!1;function wA(a){}
function xA(a){}function yA(){}
function zA(a){}function AA(a){}
function BA(a){}
function CA(){}function DA(a,b){}
function EA(a,b,c){}
function FA(){};var GA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function HA(a,b,c,d,e,f,g){var h=Object.assign({},GA);c&&(h.body=c,h.method="POST");Object.assign(h,e);l.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});IA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?dm(a,b,c):cm(a,b))})};var JA=function(a){this.R=a;this.D=""},KA=function(a,b){a.J=b;return a},LA=function(a,b){a.O=b;return a},IA=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=k(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}MA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},NA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};MA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},MA=function(a,b){b&&(OA(b.send_pixel,b.options,a.R),OA(b.create_iframe,b.options,a.J),OA(b.fetch,b.options,a.O))};function PA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function OA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=cd(b)?b:{},f=k(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};
var QA=function(a,b){return R(a,P.C.Fi)&&(b===3||b===6)},RA=function(a){return new JA(function(b,c){var d;if(c.fallback_url){var e=c.fallback_url,f=c.fallback_url_method;d=function(){switch(f){case "send_pixel":em(a,e);break;default:fm(a,e)}}}em(a,b,void 0,d)})},SA=function(a){if(a!==void 0)return Math.round(a/10)*10},TA=function(a){for(var b={},c=0;c<a.length;c++){var d=a[c],e=void 0;if(d.hasOwnProperty("google_business_vertical")){e=d.google_business_vertical;var f={};b[e]=b[e]||(f.google_business_vertical=
e,f)}else e="",b.hasOwnProperty(e)||(b[e]={});var g=b[e],h;for(h in d)h!=="google_business_vertical"&&(h in g||(g[h]=[]),g[h].push(d[h]))}return Object.keys(b).map(function(m){return b[m]})},UA=function(a){var b=Qv(a,L.m.wa);if(!b||!b.length)return[];for(var c=[],d=0;d<b.length;++d){var e=b[d];if(e){var f={};c.push((f.id=bi(e),f.origin=e.origin,f.destination=e.destination,f.start_date=e.start_date,f.end_date=e.end_date,f.location_id=e.location_id,f.google_business_vertical=e.google_business_vertical,
f))}}return c},bi=function(a){a.item_id!=null&&(a.id!=null?(N(138),a.id!==a.item_id&&N(148)):N(153));return E(20)?ci(a):a.id},WA=function(a){if(!a||typeof a!=="object"||typeof a.join==="function")return"";var b=[];nb(a,function(c,d){var e,f;if(Array.isArray(d)){for(var g=[],h=0;h<d.length;++h){var m=VA(d[h]);m!==void 0&&g.push(m)}f=g.length!==0?g.join(","):void 0}else f=VA(d);e=f;var n=VA(c);n&&e!==void 0&&b.push(n+"="+e)});return b.join(";")},VA=function(a){var b=typeof a;if(a!=null&&b!=="object"&&
b!=="function")return String(a).replace(/,/g,"\\,").replace(/;/g,"\\;").replace(/=/g,"\\=")},XA=function(a,b){var c=[],d=function(g,h){var m=xg[g]===!0;h==null||!m&&h===""||(h===!0&&(h=1),h===!1&&(h=0),c.push(g+"="+encodeURIComponent(h)))},e=R(a,P.C.fa);if(e===M.K.X||e===M.K.oa||e===M.K.Bf){var f=b.random||R(a,P.C.jb);d("random",f);delete b.random}nb(b,d);return c.join("&")},YA=function(a,b,c){if(!Qr()&&R(a,P.C.oh)){R(a,P.C.fa)===M.K.X&&(b.ct_cookie_present=0);var d=XA(a,b);return{xc:"https://td.doubleclick.net/td/rul/"+
c+"?"+d,format:4,Pa:!1,endpoint:44}}},$A=function(a,b){var c="https://www.google.com",d=54;sp(ZA)||(c="https://pagead2.googlesyndication.com",d=55);var e=Xk(c,!0,""),f=XA(a,b);return{xc:""+e+"/measurement/conversion/?"+f,format:5,Pa:!0,endpoint:d}},aB=function(a,b,c){var d=!!R(a,P.C.ce),e=Pz(21,{Cd:d}),f=XA(a,b);return{xc:Yk(e+"/"+c+"?"+f),format:1,Pa:!0,endpoint:21}},bB=function(a,b,c){var d=XA(a,b);return{xc:Pz(11)+"/"+c+"?"+d,format:1,Pa:!0,endpoint:11}},dB=function(a,b,c){if(R(a,P.C.de)&&sp(ZA))return cB(a,
b,c,"&gcp=1&ct_cookie_present=1",2)},fB=function(a,b,c){if(R(a,P.C.Fl)){var d=22;sp(ZA)?R(a,P.C.de)&&(d=23):d=60;var e=!!R(a,P.C.ce);R(a,P.C.ih)&&(b=Object.assign({},b),delete b.item);var f=XA(a,b),g=eB(a),h=Pz(d,{Cd:e})+"/"+c+"/?"+(""+f+g);e&&(h=Yk(h));return{xc:h,format:2,Pa:!0,endpoint:d}}},gB=function(a,b,c,d){for(var e=[],f=b.data||"",g=0;g<d.length;g++){var h=WA(d[g]);b.data=""+f+(f&&h?";":"")+h;e.push(cB(a,b,c));var m=YA(a,b,c);m&&e.push(m);S(a,P.C.jb,R(a,P.C.jb)+1)}return e},iB=function(a,
b,c){if(hk()&&E(148)&&sp(ZA)){var d=hB(a).endpoint,e=R(a,P.C.jb)+1;b=Object.assign({},b,{random:e,adtest:"on",exp_1p:"1"});var f=XA(a,b),g=eB(a),h;a:{switch(d){case 5:h=gk()+"/as/d/pagead/conversion";break a;case 6:h=gk()+"/gs/pagead/conversion";break a;case 8:h=gk()+"/g/d/pagead/1p-conversion";break a;default:bc(d,"Unknown endpoint")}h=void 0}return{xc:h+"/"+c+"/?"+f+g,format:3,Pa:!0,endpoint:d}}},cB=function(a,b,c,d,e){d=d===void 0?"":d;var f=Pz(9),g=XA(a,b);return{xc:f+"/"+c+"/?"+g+d,format:e!=
null?e:Qr()?2:3,Pa:!0,endpoint:9}},jB=function(a,b,c){var d=hB(a).endpoint,e=sp(ZA),f="&gcp=1&sscte=1&ct_cookie_present=1";hk()&&E(148)&&sp(ZA)&&(f="&exp_ph=1&gcp=1&sscte=1&ct_cookie_present=1",b=Object.assign({},b,{exp_1p:"1"}));var g=XA(a,b),h=eB(a),m=e?37:162,n={xc:Pz(d)+"/"+c+"/?"+g+h,format:E(m)?Qr()||!Nc()?2:e?6:5:Qr()?2:3,Pa:!0,endpoint:d};sp(L.m.W)&&(n.attributes={attributionsrc:""});if(e&&R(a,P.C.Fi)){var p=E(175)?Pz(8):""+Xk("https://www.google.com",!0,"")+"/pagead/1p-conversion";n.hp=p+
"/"+c+"/"+("?"+g+f);n.Vf=8}return n},hB=function(a){var b="/pagead/conversion",c="https://www.googleadservices.com",d=5;sp(ZA)?R(a,P.C.de)&&(c="https://www.google.com",b="/pagead/1p-conversion",d=8):(c="https://pagead2.googlesyndication.com",d=6);return{zr:c,ur:b,endpoint:d}},eB=function(a){return R(a,P.C.de)?"&gcp=1&sscte=1&ct_cookie_present=1":""},kB=function(a,b){var c=R(a,P.C.fa),d=Qv(a,L.m.Se),e=[],f=function(h){h&&e.push(h)};switch(c){case M.K.X:e.push(jB(a,b,d));f(iB(a,b,d));f(fB(a,b,d));f(dB(a,
b,d));f(YA(a,b,d));break;case M.K.oa:var g=TA(UA(a));g.length?e.push.apply(e,ua(gB(a,b,d,g))):(e.push(cB(a,b,d)),f(YA(a,b,d)));break;case M.K.Ua:e.push(bB(a,b,d));break;case M.K.La:e.push(aB(a,b,d));break;case M.K.Bf:e.push($A(a,b))}return{Hp:e}},mB=function(a,b,c,d,e,f,g,h){var m=QA(c,b),n=sp(ZA),p=R(c,P.C.fa);m||lB(a,c,e);xA(c.F.eventId);var q=function(){f&&(f(),m&&lB(a,c,e))},r={destinationId:c.target.destinationId,endpoint:e,priorityId:c.F.priorityId,eventId:c.F.eventId};switch(b){case 1:cm(r,
a);f&&f();break;case 2:em(r,a,q,g,h);break;case 3:var t=!1;try{t=im(r,l,y,a,q,g,h)}catch(B){t=!1}t||mB(a,2,c,d,e,q,g,h);break;case 4:var u="AW-"+Qv(c,L.m.Se),v=Qv(c,L.m.mc);v&&(u=u+"/"+v);jm(r,a,u);break;case 5:var w=a;n||p!==M.K.X||(w=Vl(a,"fmt",8));fm(r,w,void 0,void 0,f,g);break;case 6:var x=Vl(a,"fmt",7);el&&Zl(r,2,x);var z={};"setAttributionReporting"in XMLHttpRequest.prototype&&(z={attributionReporting:nB});HA(r,x,void 0,RA(r),z,q,g)}},lB=function(a,b,c){var d=b.F;ep({targetId:b.target.destinationId,
request:{url:a,parameterEncoding:3,endpoint:c},ab:{eventId:d.eventId,priorityId:d.priorityId},wh:{eventId:R(b,P.C.Be),priorityId:R(b,P.C.Ce)}})},oB=function(a,b){var c=!0;switch(a){case M.K.X:case M.K.La:c=!1;break;case M.K.Ua:c=!E(7)}return c?b.replace(/./g,"*"):b},pB=function(a){if(!Qv(a,L.m.Fe)||!Qv(a,L.m.Ge))return"";var b=Qv(a,L.m.Fe).split("."),c=Qv(a,L.m.Ge).split(".");if(!b.length||!c.length||b.length!==c.length)return"";for(var d=[],e=0;e<b.length;++e)d.push(b[e]+"_"+c[e]);return d.join(".")},
sB=function(a,b,c){var d=Xi(R(a,P.C.Ta)),e=Wi(d,c),f=e.Nj,g=e.rg,h=e.cb,m=e.Yo,n=e.encryptionKeyString,p=[];qB(c)||p.push("&em="+f);c===2&&p.push("&eme="+m);return{rg:g,xq:p,Dr:d,cb:h,encryptionKeyString:n,rq:function(q,r){return function(t){var u,v=r.xc;if(t){var w;w=R(a,P.C.kb);var x=Sr({Oa:w,Gm:t});v=v.replace(b.gtm,x)}u=v;if(c===1)rB(r,a,b,u,c,q)(mj(R(a,P.C.Ta)));else{var z;var B=R(a,P.C.Ta);z=c===0?kj(B,!1):c===2?kj(B,!0,!0):void 0;var C=rB(r,a,b,u,c,q);z?z.then(C):C(void 0)}}}}},rB=function(a,
b,c,d,e,f){return function(g){if(!qB(e)){var h=(g==null?0:g.Hb)?g.Hb:hj({Tc:[]}).Hb;d+="&em="+encodeURIComponent(h)}mB(d,a.format,b,c,a.endpoint,a.Pa?f:void 0,void 0,a.attributes)}},qB=function(a){return E(125)?!0:a!==2&&a!==3?!1:Ij.D&&E(19)||E(168)?!0:!1},uB=function(a,b,c){return function(d){var e=d.Hb;qB(d.Fb?2:0)||(b.em=e);if(d.cb&&d.time!==void 0){var f,
g=SA(d.time);f=["t."+(g!=null?g:""),"l."+SA(e.length)].join("~");b._ht=f}d.cb&&tB(a,b,c);}},tB=function(a,b,c){if(a===M.K.La){var d=R(c,P.C.ya),e;if(!(e=R(c,P.C.Ql))){var f;f=d||{};var g;if(sp(L.m.V)){(g=Kw(f))||(g=ts());var h=mt(f.prefix);pt(f,g);delete jt[h];delete kt[h];ot(h,f.path,f.domain);e=Kw(f)}else e=void 0}b.ecsid=e}},vB=function(a,b,c,d,e){if(a)try{uB(c,d,b)(a)}catch(f){}e(d)},
wB=function(a,b,c,d,e){if(a)try{a.then(uB(c,d,b)).then(function(){e(d)});return}catch(f){}e(d)},xB=function(a){var b=Ur(a);if(b&&b!==1)return b&1023},yB=function(a,b){return{Mm:E(164)||(a===void 0?!1:a>=512-b&&a<512),am:a===void 0?!1:a>=768-b&&a<768,bm:a===void 0?!1:a>=1024-b&&a<1024}},BB=function(a){if(R(a,P.C.fa)===M.K.Ia)Yz(a);else{var b=E(22)?wb(a.F.onFailure):void 0;zB(a,function(c,d){E(125)&&delete c.em;for(var e=kB(a,c).Hp,f=((d==null?void 0:d.Gr)||new AB(a)).J(e.filter(function(B){return B.Pa}).length),
g={},h=0;h<e.length;g={jj:void 0,Vf:void 0,Pa:void 0,Vi:void 0,fj:void 0},h++){var m=e[h],n=m.xc,p=m.format;g.Pa=m.Pa;g.Vi=m.attributes;g.fj=m.endpoint;g.jj=m.hp;g.Vf=m.Vf;var q=void 0,r=(q=d)==null?void 0:q.serviceWorker;if(r){var t=r.rq(f,e[h]),u=r,v=u.rg,w=u.encryptionKeyString,x=""+n+u.xq.join("");Ky(x,v,function(B){return function(C){lB(C.data,a,B.fj);B.Pa&&typeof f==="function"&&f()}}(g),t,w)}else{var z=b;g.jj&&g.Vf&&(z=function(B){return function(){mB(B.jj,5,a,c,B.Vf,B.Pa?f:void 0,B.Pa?b:void 0,
B.Vi)}}(g));mB(n,p,a,c,g.fj,g.Pa?f:void 0,g.Pa?z:void 0,g.Vi)}}})}},nB={eventSourceEligible:!1,triggerEligible:!0},AB=function(a){this.D=1;this.onSuccess=a.F.onSuccess};AB.prototype.J=function(a){var b=this;return Eb(function(){b.O()},a||1)};AB.prototype.O=function(){this.D--;if(eb(this.onSuccess)&&this.D===0)this.onSuccess()};var ZA=[L.m.V,L.m.W],zB=function(a,b){var c=R(a,P.C.fa),d={},e={},f=R(a,P.C.jb);c===M.K.X||c===M.K.oa?(d.cv="11",d.fst=f,d.fmt=3,d.bg="ffffff",d.guid="ON",d.async="1",E(198)&&
(d.en=a.eventName)):c===M.K.Bf&&(d.cv="11",d.tid=a.target.destinationId,d.fst=f,d.fmt=6,d.en=a.eventName);if(c===M.K.X){var g=Cs();g>0&&(d.gcl_ctr=g)}var h=Su(["aw","dc"]);h!=null&&(d.gad_source=h);d.gtm=Sr({Oa:R(a,P.C.kb)});c!==M.K.oa&&Fr()&&(d.gcs=Gr());d.gcd=Kr(a.F);Nr()&&(d.dma_cps=Lr());d.dma=Mr();ir(qr())&&(d.tcfd=Or());var m=function(){var Gc=(R(a,P.C.eh)||[]).slice(0);return function(cf){cf!==void 0&&Gc.push(cf);if(Qz()||Gc.length)d.tag_exp=Qz(Gc)}}();m();Rz()&&(d.ptag_exp=Rz());pn[Xm.Z.Ea]!==
Wm.Ka.fe||sn[Xm.Z.Ea].isConsentGranted()||(d.limited_ads="1");Qv(a,L.m.Mc)&&Zh(Qv(a,L.m.Mc),d);if(Qv(a,L.m.xb)){var n=Qv(a,L.m.xb);n&&(n.length===2?$h(d,"hl",n):n.length===5&&($h(d,"hl",n.substring(0,2)),$h(d,"gl",n.substring(3,5))))}var p=R(a,P.C.je),q=function(Gc,cf){var Gp=Qv(a,cf);Gp&&(d[Gc]=p?av(Gp):Gp)};q("url",L.m.Ba);q("ref",L.m.Wa);q("top",L.m.ni);var r=pB(a);r&&(d.gclaw_src=r);for(var t=k(Object.keys(a.D)),u=t.next();!u.done;u=t.next()){var v=u.value,w=Qv(a,v);if(Yh.hasOwnProperty(v)){var x=
Yh[v];x&&(d[x]=w)}else e[v]=w}Sz(d,Qv(a,L.m.rd));var z=Qv(a,L.m.hf);z!==void 0&&z!==""&&(d.vdnc=String(z));var B=Qv(a,L.m.We);B!==void 0&&(d.shf=B);var C=Qv(a,L.m.Vd);C!==void 0&&(d.delc=C);if(E(30)&&R(a,P.C.vg)){d.tft=ub();var F=Qc();F!==void 0&&(d.tfd=Math.round(F))}c!==M.K.Bf&&(d.data=WA(e));var G=Qv(a,L.m.wa);!G||c!==M.K.X&&c!==M.K.Bf||(d.iedeld=fi(G),d.item=ai(G));var I=Qv(a,L.m.qc);if(I&&typeof I==="object")for(var K=k(Object.keys(I)),U=K.next();!U.done;U=K.next()){var Q=U.value;d["gap."+Q]=
I[Q]}R(a,P.C.Hi)&&(d.aecs="1");if(c!==M.K.X&&c!==M.K.Ua&&c!==M.K.La)b(d);else if(sp(L.m.W)&&sp(L.m.V)){var na;a:switch(c){case M.K.Ua:na=E(66);break a;case M.K.La:na=!Ij.D&&E(68)||E(168)?!0:Ij.D;break a;default:na=!1}na&&S(a,P.C.ce,!0);var T=!!R(a,P.C.ce);if(R(a,P.C.Ta)){var aa=xB(Qv(a,L.m.Mb)||"");if(c!==M.K.X){d.gtm=Sr({Oa:R(a,P.C.kb),Gm:3});var Y=yB(aa,Ri.Xo),V=Y.Mm,ka=Y.am,ja=Y.bm;T||(V?m(104557470):ka?m(104557471):ja&&m(104557472));var la=sB(a,d,T?2:V?1:0);la.cb&&tB(c,d,a);b(d,{serviceWorker:la})}else{var Ra=
R(a,P.C.Ta),Xa=yB(aa,Ri.Wo),Fa=Xa.Mm,bb=Xa.am,ib=Xa.bm;T||(Fa?m(103308613):bb?m(103308614):ib&&m(103308615));if(T||!Fa){var Hc=kj(Ra,T,void 0,void 0,bb||ib);wB(Hc,a,c,d,b)}else vB(mj(Ra),a,c,d,b)}}else b(d)}else d.ec_mode=void 0,b(d)};function CB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function DB(a,b,c){c=c===void 0?!1:c;EB().addRestriction(0,a,b,c)}function FB(a,b,c){c=c===void 0?!1:c;EB().addRestriction(1,a,b,c)}function GB(){var a=Em();return EB().getRestrictions(1,a)}var HB=function(){this.container={};this.D={}},IB=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
HB.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=IB(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
HB.prototype.getRestrictions=function(a,b){var c=IB(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
HB.prototype.getExternalRestrictions=function(a,b){var c=IB(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};HB.prototype.removeExternalRestrictions=function(a){var b=IB(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function EB(){return Dp("r",function(){return new HB})};var JB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),KB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},LB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},MB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function NB(){var a=nk("gtm.allowlist")||nk("gtm.whitelist");a&&N(9);Vj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);JB.test(l.location&&l.location.hostname)&&(Vj?N(116):(N(117),OB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&yb(rb(a),KB),c=nk("gtm.blocklist")||nk("gtm.blacklist");c||(c=nk("tagTypeBlacklist"))&&N(3);c?N(8):c=[];JB.test(l.location&&l.location.hostname)&&(c=rb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
rb(c).indexOf("google")>=0&&N(2);var d=c&&yb(rb(c),LB),e={};return function(f){var g=f&&f[Ye.Ha];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=dk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Vj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){N(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=lb(d,h||[]);t&&N(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Vj&&h.indexOf("cmpPartners")>=0?!PB():b&&b.indexOf("sandboxedScripts")!==-1?0:lb(d,MB))&&(u=!0);return e[g]=u}}function PB(){var a=bg(Zf.D,Cm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var OB=!1;OB=!0;
function QB(){tm&&DB(Em(),function(a){var b=Kf(a.entityId),c;if(Nf(b)){var d=b[Ye.Ha];if(!d)throw Error("Error: No function name given for function call.");var e=Bf[d];c=!!e&&!!e.runInSiloedMode}else c=!!CB(b[Ye.Ha],4);return c})};function RB(a,b,c,d,e){if(!SB()){var f=d.siloed?zm(a):a;if(!Nm(f)){d.loadExperiments=Kj();Pm(f,d,e);var g=TB(a),h=function(){pm().container[f]&&(pm().container[f].state=3);UB()},m={destinationId:f,endpoint:0};if(hk())gm(m,gk()+"/"+g,void 0,h);else{var n=zb(a,"GTM-"),p=Vk(),q=c?"/gtag/js":"/gtm.js",r=Uk(b,q+g);if(!r){var t=Mj.Bg+q;p&&nc&&n&&(t=nc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=uw("https://","http://",t+g)}gm(m,r,void 0,h)}}}}
function UB(){Rm()||nb(Sm(),function(a,b){VB(a,b.transportUrl,b.context);N(92)})}
function VB(a,b,c,d){if(!SB()){var e=c.siloed?zm(a):a;if(!Om(e))if(c.loadExperiments||(c.loadExperiments=Kj()),Rm()){var f;(f=pm().destination)[e]!=null||(f[e]={state:0,transportUrl:b,context:c,parent:Hm()});pm().destination[e].state=0;om({ctid:e,isDestination:!0},d);N(91)}else{c.siloed&&Qm({ctid:e,isDestination:!0});var g;(g=pm().destination)[e]!=null||(g[e]={context:c,state:1,parent:Hm()});pm().destination[e].state=1;om({ctid:e,isDestination:!0},d);var h={destinationId:e,endpoint:0};if(hk())gm(h,
gk()+("/gtd"+TB(a,!0)));else{var m="/gtag/destination"+TB(a,!0),n=Uk(b,m);n||(n=uw("https://","http://",Mj.Bg+m));gm(h,n)}}}}function TB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Pj!=="dataLayer"&&(c+="&l="+Pj);if(!zb(a,"GTM-")||b)c=E(130)?c+(hk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Tr();Vk()&&(c+="&sign="+Mj.Oi);var d=Ij.J;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Kj().join("~")&&(c+="&tag_exp="+Kj().join("~"));return c}
function SB(){if(Qr()){return!0}return!1};var WB=function(){this.J=0;this.D={}};WB.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,ac:c};return d};WB.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var YB=function(a,b){var c=[];nb(XB.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.ac===void 0||b.indexOf(e.ac)>=0)&&c.push(e.listener)});return c};function ZB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Cm()}};var aC=function(a,b){this.D=!1;this.R=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;$B(this,a,b)},bC=function(a,b,c,d){if(Rj.hasOwnProperty(b)||b==="__zone")return-1;var e={};cd(d)&&(e=dd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},cC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},dC=function(a){if(!a.D){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.D=!0;a.R.length=0}},$B=function(a,b,c){b!==void 0&&a.Pf(b);c&&l.setTimeout(function(){dC(a)},
Number(c))};aC.prototype.Pf=function(a){var b=this,c=wb(function(){A(function(){a(Cm(),b.eventData)})});this.D?c():this.R.push(c)};var eC=function(a){a.O++;return wb(function(){a.J++;a.T&&a.J>=a.O&&dC(a)})},fC=function(a){a.T=!0;a.J>=a.O&&dC(a)};var gC={};function hC(){return l[iC()]}
function iC(){return l.GoogleAnalyticsObject||"ga"}function lC(){var a=Cm();}
function mC(a,b){return function(){var c=hC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var sC=["es","1"],tC={},uC={};function vC(a,b){if(dl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";tC[a]=[["e",c],["eid",a]];Gq(a)}}function wC(a){var b=a.eventId,c=a.Kd;if(!tC[b])return[];var d=[];uC[b]||d.push(sC);d.push.apply(d,ua(tC[b]));c&&(uC[b]=!0);return d};var xC={},yC={},zC={};function AC(a,b,c,d){dl&&E(120)&&((d===void 0?0:d)?(zC[b]=zC[b]||0,++zC[b]):c!==void 0?(yC[a]=yC[a]||{},yC[a][b]=Math.round(c)):(xC[a]=xC[a]||{},xC[a][b]=(xC[a][b]||0)+1))}function BC(a){var b=a.eventId,c=a.Kd,d=xC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete xC[b];return e.length?[["md",e.join(".")]]:[]}
function CC(a){var b=a.eventId,c=a.Kd,d=yC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete yC[b];return e.length?[["mtd",e.join(".")]]:[]}function DC(){for(var a=[],b=k(Object.keys(zC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+zC[d])}return a.length?[["mec",a.join(".")]]:[]};var EC={},FC={};function GC(a,b,c){if(dl&&b){var d=Zk(b);EC[a]=EC[a]||[];EC[a].push(c+d);var e=(Nf(b)?"1":"2")+d;FC[a]=FC[a]||[];FC[a].push(e);Gq(a)}}function HC(a){var b=a.eventId,c=a.Kd,d=[],e=EC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=FC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete EC[b],delete FC[b]);return d};function IC(a,b,c,d){var e=zf[a],f=JC(a,b,c,d);if(!f)return null;var g=Of(e[Ye.Il],c,[]);if(g&&g.length){var h=g[0];f=IC(h.index,{onSuccess:f,onFailure:h.hm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function JC(a,b,c,d){function e(){function w(){$n(3);var I=ub()-G;GC(c.id,f,"7");cC(c.Oc,C,"exception",I);E(109)&&EA(c,f,$z.P.Ri);F||(F=!0,h())}if(f[Ye.po])h();else{var x=Mf(f,c,[]),z=x[Ye.Vm];if(z!=null)for(var B=0;B<z.length;B++)if(!sp(z[B])){h();return}var C=bC(c.Oc,String(f[Ye.Ha]),Number(f[Ye.rh]),x[Ye.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var I=ub()-G;GC(c.id,zf[a],"5");cC(c.Oc,C,"success",I);E(109)&&EA(c,f,$z.P.Ti);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var I=ub()-
G;GC(c.id,zf[a],"6");cC(c.Oc,C,"failure",I);E(109)&&EA(c,f,$z.P.Si);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);GC(c.id,f,"1");E(109)&&DA(c,f);var G=ub();try{Pf(x,{event:c,index:a,type:1})}catch(I){w(I)}E(109)&&EA(c,f,$z.P.Ol)}}var f=zf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Of(f[Ye.Pl],c,[]);if(n&&n.length){var p=n[0],q=IC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.hm===
2?m:q}if(f[Ye.zl]||f[Ye.ro]){var r=f[Ye.zl]?Af:c.uq,t=g,u=h;if(!r[a]){var v=KC(a,r,wb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function KC(a,b,c){var d=[],e=[];b[a]=LC(d,e,c);return{onSuccess:function(){b[a]=MC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=NC;for(var f=0;f<e.length;f++)e[f]()}}}function LC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function MC(a){a()}function NC(a,b){b()};var QC=function(a,b){for(var c=[],d=0;d<zf.length;d++)if(a[d]){var e=zf[d];var f=eC(b.Oc);try{var g=IC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Ye.Ha];if(!h)throw Error("Error: No function name given for function call.");var m=Bf[h];c.push({Lm:d,priorityOverride:(m?m.priorityOverride||0:0)||CB(e[Ye.Ha],1)||0,execute:g})}else OC(d,b),f()}catch(p){f()}}c.sort(PC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function RC(a,b){if(!XB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=YB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=eC(b);try{d[e](a,f)}catch(g){f()}}return!0}function PC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Lm,h=b.Lm;f=g>h?1:g<h?-1:0}return f}
function OC(a,b){if(dl){var c=function(d){var e=b.isBlocked(zf[d])?"3":"4",f=Of(zf[d][Ye.Il],b,[]);f&&f.length&&c(f[0].index);GC(b.id,zf[d],e);var g=Of(zf[d][Ye.Pl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var SC=!1,XB;function TC(){XB||(XB=new WB);return XB}
function UC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(SC)return!1;SC=!0}var e=!1,f=GB(),g=dd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}vC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:VC(g,e),uq:[],logMacroError:function(){N(6);$n(0)},cachedModelValues:WC(),Oc:new aC(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&dl&&(n.reportMacroDiscrepancy=AC);E(109)&&AA(n.id);var p=Uf(n);E(109)&&BA(n.id);e&&(p=XC(p));E(109)&&zA(b);var q=QC(p,n),r=RC(a,n.Oc);fC(n.Oc);d!=="gtm.js"&&d!=="gtm.sync"||lC();return YC(p,q)||r}function WC(){var a={};a.event=sk("event",1);a.ecommerce=sk("ecommerce",1);a.gtm=sk("gtm");a.eventModel=sk("eventModel");return a}
function VC(a,b){var c=NB();return function(d){if(c(d))return!0;var e=d&&d[Ye.Ha];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Em();f=EB().getRestrictions(0,g);var h=a;b&&(h=dd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=dk[e]||[],n=k(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function XC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(zf[c][Ye.Ha]);if(Qj[d]||zf[c][Ye.so]!==void 0||CB(d,2))b[c]=!0}return b}function YC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&zf[c]&&!Rj[String(zf[c][Ye.Ha])])return!0;return!1};function ZC(){TC().addListener("gtm.init",function(a,b){Ij.la=!0;Kn();b()})};var $C=!1,aD=0,bD=[];function cD(a){if(!$C){var b=y.createEventObject,c=y.readyState==="complete",d=y.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){$C=!0;for(var e=0;e<bD.length;e++)A(bD[e])}bD.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)A(f[g]);return 0}}}function dD(){if(!$C&&aD<140){aD++;try{var a,b;(b=(a=y.documentElement).doScroll)==null||b.call(a,"left");cD()}catch(c){l.setTimeout(dD,50)}}}
function eD(){$C=!1;aD=0;if(y.readyState==="interactive"&&!y.createEventObject||y.readyState==="complete")cD();else{Bc(y,"DOMContentLoaded",cD);Bc(y,"readystatechange",cD);if(y.createEventObject&&y.documentElement.doScroll){var a=!0;try{a=!l.frameElement}catch(b){}a&&dD()}Bc(l,"load",cD)}}function fD(a){$C?a():bD.push(a)};var gD={},hD={};function iD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Dj:void 0,lj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Dj=Op(g,b),e.Dj){var h=um?um:Bm();jb(h,function(r){return function(t){return r.Dj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=gD[g]||[];e.lj={};m.forEach(function(r){return function(t){r.lj[t]=!0}}(e));for(var n=xm(),p=0;p<n.length;p++)if(e.lj[n[p]]){c=c.concat(Am());break}var q=hD[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,Up:d}}
function jD(a){nb(gD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function kD(a){nb(hD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var lD=!1,mD=!1;function nD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=dd(b,null),b[L.m.Xe]&&(d.eventCallback=b[L.m.Xe]),b[L.m.Qg]&&(d.eventTimeout=b[L.m.Qg]));return d}function oD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Hp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function pD(a,b){var c=a&&a[L.m.kd];c===void 0&&(c=nk(L.m.kd,2),c===void 0&&(c="default"));if(fb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?fb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=iD(d,b.isGtmEvent),f=e.wj,g=e.Up;if(g.length)for(var h=qD(a),m=0;m<g.length;m++){var n=Op(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=zb(p,"siloed_"))){var r=n.destinationId,t=pm().destination[r];q=!!t&&t.state===0}q||VB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{wj:Pp(f,b.isGtmEvent),Jo:Pp(u,b.isGtmEvent)}}}var rD=void 0,sD=void 0;function tD(a,b,c){var d=dd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&N(136);var e=dd(b,null);dd(c,e);Rw(Nw(xm()[0],e),a.eventId,d)}function qD(a){for(var b=k([L.m.ld,L.m.uc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Oq.D[d];if(e)return e}}
var uD={config:function(a,b){var c=oD(a,b);if(!(a.length<2)&&fb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!cd(a[2])||a.length>3)return;d=a[2]}var e=Op(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!sm.he){var m=Gm(Hm());if(Tm(m)){var n=m.parent,p=n.isDestination;h={Wp:Gm(n),Rp:p};break a}}h=void 0}var q=h;q&&(f=q.Wp,g=q.Rp);vC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Am().indexOf(r)===-1:xm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[L.m.Kc]){var u=qD(d);if(t)VB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;rD?tD(b,v,rD):sD||(sD=dd(v,null))}else RB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(N(128),g&&N(130),b.inheritParentConfig)){var w;var x=d;sD?(tD(b,sD,x),w=!1):(!x[L.m.nd]&&Tj&&rD||(rD=dd(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}el&&(Jp===1&&(Cn.mcc=!1),Jp=2);if(Tj&&!t&&!d[L.m.nd]){var z=mD;mD=!0;if(z)return}lD||N(43);if(!b.noTargetGroup)if(t){kD(e.id);
var B=e.id,C=d[L.m.Tg]||"default";C=String(C).split(",");for(var F=0;F<C.length;F++){var G=hD[C[F]]||[];hD[C[F]]=G;G.indexOf(B)<0&&G.push(B)}}else{jD(e.id);var I=e.id,K=d[L.m.Tg]||"default";K=K.toString().split(",");for(var U=0;U<K.length;U++){var Q=gD[K[U]]||[];gD[K[U]]=Q;Q.indexOf(I)<0&&Q.push(I)}}delete d[L.m.Tg];var na=b.eventMetadata||{};na.hasOwnProperty(P.C.ud)||(na[P.C.ud]=!b.fromContainerExecution);b.eventMetadata=na;delete d[L.m.Xe];for(var T=t?[e.id]:Am(),aa=0;aa<T.length;aa++){var Y=d,
V=T[aa],ka=dd(b,null),ja=Op(V,ka.isGtmEvent);ja&&Oq.push("config",[Y],ja,ka)}}}}},consent:function(a,b){if(a.length===3){N(39);var c=oD(a,b),d=a[1],e={},f=Lo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===L.m.wg?Array.isArray(h)?NaN:Number(h):g===L.m.bc?(Array.isArray(h)?h:[h]).map(Mo):No(h)}b.fromContainerExecution||(e[L.m.W]&&N(139),e[L.m.Na]&&N(140));d==="default"?op(e):d==="update"?qp(e,c):d==="declare"&&b.fromContainerExecution&&np(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&fb(c)){var d=void 0;if(a.length>2){if(!cd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=nD(c,d),f=oD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=pD(d,b);if(m){var n=m.wj,p=m.Jo,q,r,t;if(!tm&&E(108)){q=p.map(function(I){return I.id});r=p.map(function(I){return I.destinationId});t=n.map(function(I){return I.id});for(var u=k(um?um:Bm()),v=u.next();!v.done;v=u.next()){var w=v.value;
!zb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(zm(w))<0&&t.push(w)}}else q=n.map(function(I){return I.id}),r=n.map(function(I){return I.destinationId}),t=q;vC(g,c);for(var x=k(t),z=x.next();!z.done;z=x.next()){var B=z.value,C=dd(b,null),F=dd(d,null);delete F[L.m.Xe];var G=C.eventMetadata||{};G.hasOwnProperty(P.C.ud)||(G[P.C.ud]=!C.fromContainerExecution);G[P.C.Mi]=q.slice();G[P.C.Mf]=r.slice();C.eventMetadata=G;Pq(c,F,B,C)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[L.m.kd]=q.join(","):delete e.eventModel[L.m.kd];
lD||N(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[P.C.Nl]&&(b.noGtmEvent=!0);e.eventModel[L.m.Jc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){N(53);if(a.length===4&&fb(a[1])&&fb(a[2])&&eb(a[3])){var c=Op(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){lD||N(43);var f=qD();if(jb(Am(),function(h){return c.destinationId===h})){oD(a,b);var g={};dd((g[L.m.oc]=d,g[L.m.Hc]=e,g),null);Qq(d,function(h){A(function(){e(h)})},c.id,b)}else VB(c.destinationId,f,{source:4,
fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){lD=!0;var c=oD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&fb(a[1])&&eb(a[2])){if($f(a[1],a[2]),N(74),a[1]==="all"){N(75);var b=!1;try{b=a[2](Cm(),"unknown",{})}catch(c){}b||N(76)}}else N(73)},set:function(a,b){var c=void 0;a.length===2&&cd(a[1])?c=dd(a[1],null):a.length===
3&&fb(a[1])&&(c={},cd(a[2])||Array.isArray(a[2])?c[a[1]]=dd(a[2],null):c[a[1]]=a[2]);if(c){var d=oD(a,b),e=d.eventId,f=d.priorityId;dd(c,null);var g=dd(c,null);Oq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},vD={policy:!0};var xD=function(a){if(wD(a))return a;this.value=a};xD.prototype.getUntrustedMessageValue=function(){return this.value};var wD=function(a){return!a||ad(a)!=="object"||cd(a)?!1:"getUntrustedMessageValue"in a};xD.prototype.getUntrustedMessageValue=xD.prototype.getUntrustedMessageValue;var yD=!1,zD=[];function AD(){if(!yD){yD=!0;for(var a=0;a<zD.length;a++)A(zD[a])}}function BD(a){yD?A(a):zD.push(a)};var CD=0,DD={},ED=[],FD=[],GD=!1,HD=!1;function ID(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function JD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return KD(a)}function LD(a,b){if(!gb(b)||b<0)b=0;var c=Cp[Pj],d=0,e=!1,f=void 0;f=l.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(l.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function MD(a,b){var c=a._clear||b.overwriteModelFields;nb(a,function(e,f){e!=="_clear"&&(c&&qk(e),qk(e,f))});ak||(ak=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Hp(),a["gtm.uniqueEventId"]=d,qk("gtm.uniqueEventId",d));return UC(a)}function ND(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ob(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function OD(){var a;if(FD.length)a=FD.shift();else if(ED.length)a=ED.shift();else return;var b;var c=a;if(GD||!ND(c.message))b=c;else{GD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Hp(),f=Hp(),c.message["gtm.uniqueEventId"]=Hp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};ED.unshift(n,c);b=h}return b}
function PD(){for(var a=!1,b;!HD&&(b=OD());){HD=!0;delete kk.eventModel;mk();var c=b,d=c.message,e=c.messageContext;if(d==null)HD=!1;else{e.fromContainerExecution&&rk();try{if(eb(d))try{d.call(ok)}catch(u){}else if(Array.isArray(d)){if(fb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=nk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(ob(d))a:{if(d.length&&fb(d[0])){var p=uD[d[0]];if(p&&(!e.fromContainerExecution||!vD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=MD(n,e)||a)}}finally{e.fromContainerExecution&&mk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=DD[String(q)]||[],t=0;t<r.length;t++)FD.push(QD(r[t]));r.length&&FD.sort(ID);delete DD[String(q)];q>CD&&(CD=q)}HD=!1}}}return!a}
function RD(){if(E(109)){var a=!Ij.R;}var c=PD();if(E(109)){}try{var e=Cm(),f=l[Pj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Uw(a){if(CD<a.notBeforeEventId){var b=String(a.notBeforeEventId);DD[b]=DD[b]||[];DD[b].push(a)}else FD.push(QD(a)),FD.sort(ID),A(function(){HD||PD()})}function QD(a){return{message:a.message,messageContext:a.messageContext}}
function SD(){function a(f){var g={};if(wD(f)){var h=f;f=wD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=oc(Pj,[]),c=Cp[Pj]=Cp[Pj]||{};c.pruned===!0&&N(83);DD=Sw().get();Tw();fD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});BD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Cp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new xD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});ED.push.apply(ED,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(N(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return PD()&&p};var e=b.slice(0).map(function(f){return a(f)});ED.push.apply(ED,e);if(!Ij.R){if(E(109)){}A(RD)}}var KD=function(a){return l[Pj].push(a)};function TD(a){KD(a)};function UD(){var a,b=Ok(l.location.href);(a=b.hostname+b.pathname)&&Gn("dl",encodeURIComponent(a));var c;var d=cg.ctid;if(d){var e=sm.he?1:0,f,g=Gm(Hm());f=g&&g.context;c=d+";"+cg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Gn("tdp",h);var m=Fl(!0);m!==void 0&&Gn("frm",String(m))};function VD(){el&&l.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){N(179);var b=bm(a.effectiveDirective);if(b){var c;var d=$l(b,a.blockedURI);c=d?Yl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=k(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Dm){p.Dm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Yo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Yo()){var u=dp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Xo(u)}}}Mn(p.endpoint)}}am(b,a.blockedURI)}}}}})};function WD(){var a;var b=Fm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Gn("pcid",e)};var XD=/^(https?:)?\/\//;
function YD(){var a;var b=Gm(Hm());if(b){for(;b.parent;){var c=Gm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Sc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=k(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(XD,"")===g.replace(XD,""))){e=n;break a}}N(146)}else N(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
Gn("rtg",String(d.canonicalContainerId)),Gn("slo",String(t)),Gn("hlo",d.htmlLoadOrder||"-1"),Gn("lst",String(d.loadScriptType||"0")))}else N(144)};function ZD(){var a=[],b=Number('')||0,c=function(){var f=!1;return f}();a.push({Km:195,Jm:195,experimentId:104527906,controlId:104527907,percent:b,active:c,dj:1});var d=Number('')||0,e=function(){var f=!1;
return f}();a.push({Km:196,Jm:196,experimentId:104528500,controlId:104528501,percent:d,active:e,dj:0});return a};var $D={};function aE(a){for(var b=k(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Ij.ia.J.add(Number(c.value))}function bE(){if(E(194))for(var a=k(ZD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Km;gi[d]=c;if(c.dj===1){var e=d,f=go(ao.aa.vo);ji(f,e);aE(f)}else if(c.dj===0){var g=$D;ji(g,d);aE(g)}}};
function wE(){};var xE=function(){};xE.prototype.toString=function(){return"undefined"};var yE=new xE;function FE(a,b){function c(g){var h=Ok(g),m=Ik(h,"protocol"),n=Ik(h,"host",!0),p=Ik(h,"port"),q=Ik(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function GE(a){return HE(a)?1:0}
function HE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=dd(a,{});dd({arg1:c[d],any_of:void 0},e);if(GE(e))return!0}return!1}switch(a["function"]){case "_cn":return Lg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Gg.length;g++){var h=Gg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Hg(b,c);case "_eq":return Mg(b,c);case "_ge":return Ng(b,c);case "_gt":return Pg(b,c);case "_lc":return Ig(b,c);case "_le":return Og(b,
c);case "_lt":return Qg(b,c);case "_re":return Kg(b,c,a.ignore_case);case "_sw":return Rg(b,c);case "_um":return FE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var IE=function(a,b,c,d){er.call(this);this.nh=b;this.If=c;this.Eb=d;this.Za=new Map;this.qh=0;this.la=new Map;this.Ca=new Map;this.T=void 0;this.J=a};sa(IE,er);IE.prototype.O=function(){delete this.D;this.Za.clear();this.la.clear();this.Ca.clear();this.T&&(ar(this.J,"message",this.T),delete this.T);delete this.J;delete this.Eb;er.prototype.O.call(this)};
var JE=function(a){if(a.D)return a.D;a.If&&a.If(a.J)?a.D=a.J:a.D=El(a.J,a.nh);var b;return(b=a.D)!=null?b:null},LE=function(a,b,c){if(JE(a))if(a.D===a.J){var d=a.Za.get(b);d&&d(a.D,c)}else{var e=a.la.get(b);if(e&&e.vj){KE(a);var f=++a.qh;a.Ca.set(f,{Ih:e.Ih,To:e.lm(c),persistent:b==="addEventListener"});a.D.postMessage(e.vj(c,f),"*")}}},KE=function(a){a.T||(a.T=function(b){try{var c;c=a.Eb?a.Eb(b):void 0;if(c){var d=c.Zp,e=a.Ca.get(d);if(e){e.persistent||a.Ca.delete(d);var f;(f=e.Ih)==null||f.call(e,
e.To,c.payload)}}}catch(g){}},$q(a.J,"message",a.T))};var ME=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},NE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},OE={lm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Ih:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},PE={lm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Ih:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function QE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Zp:b.__gppReturn.callId}}
var RE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;er.call(this);this.caller=new IE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},QE);this.caller.Za.set("addEventListener",ME);this.caller.la.set("addEventListener",OE);this.caller.Za.set("removeEventListener",NE);this.caller.la.set("removeEventListener",PE);this.timeoutMs=c!=null?c:500};sa(RE,er);RE.prototype.O=function(){this.caller.dispose();er.prototype.O.call(this)};
RE.prototype.addEventListener=function(a){var b=this,c=hl(function(){a(SE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);LE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(TE,!0);return}a(UE,!0)}}})};
RE.prototype.removeEventListener=function(a){LE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var UE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},SE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},TE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function VE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Av.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Av.D=d}}function WE(){try{var a=new RE(l,{timeoutMs:-1});JE(a.caller)&&a.addEventListener(VE)}catch(b){}};function XE(){var a=[["cv",Mi(1)],["rv",Nj],["tc",zf.filter(function(b){return b}).length]];Oj&&a.push(["x",Oj]);fk()&&a.push(["tag_exp",fk()]);return a};var YE={};function Pi(a){YE[a]=(YE[a]||0)+1}function ZE(){for(var a=[],b=k(Object.keys(YE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+YE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var $E={},aF={};function bF(a){var b=a.eventId,c=a.Kd,d=[],e=$E[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=aF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete $E[b],delete aF[b]);return d};function cF(){return!1}function dF(){var a={};return function(b,c,d){}};function eF(){var a=fF;return function(b,c,d){var e=d&&d.event;gF(c);var f=wh(b)?void 0:1,g=new Oa;nb(c,function(r,t){var u=td(t,void 0,f);u===void 0&&t!==void 0&&N(44);g.set(r,u)});a.D.D.J=Sf();var h={Wl:gg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Pf:e!==void 0?function(r){e.Oc.Pf(r)}:void 0,Gb:function(){return b},log:function(){},fp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},hq:!!CB(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(cF()){var m=dF(),n,p;h.tb={Oj:[],Qf:{},Yb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Gh:Oh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Pe(a,h,[b,g]);a.D.D.J=void 0;q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return sd(q,void 0,f)}}function gF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;eb(b)&&(a.gtmOnSuccess=function(){A(b)});eb(c)&&(a.gtmOnFailure=function(){A(c)})};function hF(a){}hF.N="internal.addAdsClickIds";function iF(a,b){var c=this;}iF.publicName="addConsentListener";var jF=!1;function kF(a){for(var b=0;b<a.length;++b)if(jF)try{a[b]()}catch(c){N(77)}else a[b]()}function lF(a,b,c){var d=this,e;if(!hh(a)||!dh(b)||!ih(c))throw H(this.getName(),["string","function","string|undefined"],arguments);kF([function(){J(d,"listen_data_layer",a)}]);e=TC().addListener(a,sd(b),c===null?void 0:c);return e}lF.N="internal.addDataLayerEventListener";function mF(a,b,c){}mF.publicName="addDocumentEventListener";function nF(a,b,c,d){}nF.publicName="addElementEventListener";function oF(a){return a.M.D};function pF(a){}pF.publicName="addEventCallback";
var qF=function(a){return typeof a==="string"?a:String(Hp())},tF=function(a,b){rF(a,"init",!1)||(sF(a,"init",!0),b())},rF=function(a,b,c){var d=uF(a);return vb(d,b,c)},vF=function(a,b,c,d){var e=uF(a),f=vb(e,b,d);e[b]=c(f)},sF=function(a,b,c){uF(a)[b]=c},uF=function(a){var b=Dp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},wF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Pc(a,"className"),"gtm.elementId":a.for||Dc(a,"id")||"","gtm.elementTarget":a.formTarget||
Pc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Pc(a,"href")||a.src||a.code||a.codebase||"";return d};
function FF(a){}FF.N="internal.addFormAbandonmentListener";function GF(a,b,c,d){}
GF.N="internal.addFormData";var HF={},IF=[],JF={},KF=0,LF=0;
function SF(a,b){}SF.N="internal.addFormInteractionListener";
function ZF(a,b){}ZF.N="internal.addFormSubmitListener";
function dG(a){}dG.N="internal.addGaSendListener";function eG(a){if(!a)return{};var b=a.fp;return ZB(b.type,b.index,b.name)}function fG(a){return a?{originatingEntity:eG(a)}:{}};function nG(a){var b=Cp.zones;return b?b.getIsAllowedFn(xm(),a):function(){return!0}}function oG(){var a=Cp.zones;a&&a.unregisterChild(xm())}
function pG(){FB(Em(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Cp.zones;return c?c.isActive(xm(),b):!0});DB(Em(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return nG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var qG=function(a,b){this.tagId=a;this.oe=b};
function rG(a,b){var c=this,d=void 0;
return d}rG.N="internal.loadGoogleTag";function sG(a){return new kd("",function(b){var c=this.evaluate(b);if(c instanceof kd)return new kd("",function(){var d=ya.apply(0,arguments),e=this,f=dd(oF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Ia(this.M);h.D=f;return c.Jb.apply(c,[h].concat(ua(g)))})})};function tG(a,b,c){var d=this;}tG.N="internal.addGoogleTagRestriction";var uG={},vG=[];
function CG(a,b){}
CG.N="internal.addHistoryChangeListener";function DG(a,b,c){}DG.publicName="addWindowEventListener";function EG(a,b){return!0}EG.publicName="aliasInWindow";function FG(a,b,c){}FG.N="internal.appendRemoteConfigParameter";function GG(a){var b;return b}
GG.publicName="callInWindow";function HG(a){}HG.publicName="callLater";function IG(a){}IG.N="callOnDomReady";function JG(a){}JG.N="callOnWindowLoad";function KG(a,b){var c;return c}KG.N="internal.computeGtmParameter";function LG(a,b){var c=this;}LG.N="internal.consentScheduleFirstTry";function MG(a,b){var c=this;}MG.N="internal.consentScheduleRetry";function NG(a){var b;return b}NG.N="internal.copyFromCrossContainerData";function OG(a,b){var c;if(!hh(a)||!mh(b)&&b!==null&&!ch(b))throw H(this.getName(),["string","number|undefined"],arguments);J(this,"read_data_layer",a);c=(b||2)!==2?nk(a,1):pk(a,[l,y]);var d=td(c,this.M,wh(oF(this).Gb())?2:1);d===void 0&&c!==void 0&&N(45);return d}OG.publicName="copyFromDataLayer";
function PG(a){var b=void 0;return b}PG.N="internal.copyFromDataLayerCache";function QG(a){var b;return b}QG.publicName="copyFromWindow";function RG(a){var b=void 0;return td(b,this.M,1)}RG.N="internal.copyKeyFromWindow";var SG=function(a){return a===Xm.Z.Ea&&pn[a]===Wm.Ka.fe&&!sp(L.m.V)};var TG=function(){return"0"},UG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Pk(a,b,"0")};var VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH=(tH[L.m.Sa]=(VG[2]=[SG],VG),tH[L.m.nf]=(WG[2]=[SG],WG),tH[L.m.Ye]=(XG[2]=[SG],XG),tH[L.m.ri]=(YG[2]=[SG],YG),tH[L.m.si]=(ZG[2]=[SG],ZG),tH[L.m.ui]=($G[2]=[SG],$G),tH[L.m.wi]=(aH[2]=[SG],aH),tH[L.m.xi]=(bH[2]=[SG],bH),tH[L.m.Tb]=(cH[2]=[SG],cH),tH[L.m.qf]=(dH[2]=[SG],dH),tH[L.m.rf]=(eH[2]=[SG],eH),tH[L.m.tf]=(fH[2]=[SG],fH),tH[L.m.uf]=(gH[2]=
[SG],gH),tH[L.m.vf]=(hH[2]=[SG],hH),tH[L.m.wf]=(iH[2]=[SG],iH),tH[L.m.xf]=(jH[2]=[SG],jH),tH[L.m.yf]=(kH[2]=[SG],kH),tH[L.m.nb]=(lH[1]=[SG],lH),tH[L.m.Yc]=(mH[1]=[SG],mH),tH[L.m.ed]=(nH[1]=[SG],nH),tH[L.m.Ud]=(oH[1]=[SG],oH),tH[L.m.Je]=(pH[1]=[function(a){return E(102)&&SG(a)}],pH),tH[L.m.fd]=(qH[1]=[SG],qH),tH[L.m.Ba]=(rH[1]=[SG],rH),tH[L.m.Wa]=(sH[1]=[SG],sH),tH),vH={},wH=(vH[L.m.nb]=TG,vH[L.m.Yc]=TG,vH[L.m.ed]=TG,vH[L.m.Ud]=TG,vH[L.m.Je]=TG,vH[L.m.fd]=function(a){if(!cd(a))return{};var b=dd(a,
null);delete b.match_id;return b},vH[L.m.Ba]=UG,vH[L.m.Wa]=UG,vH),xH={},yH={},zH=(yH[P.C.Ta]=(xH[2]=[SG],xH),yH),AH={};var BH=function(a,b,c,d){this.D=a;this.O=b;this.R=c;this.T=d};BH.prototype.getValue=function(a){a=a===void 0?Xm.Z.Db:a;if(!this.O.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.T(this.D):this.D};BH.prototype.J=function(){return ad(this.D)==="array"||cd(this.D)?dd(this.D,null):this.D};
var CH=function(){},DH=function(a,b){this.conditions=a;this.D=b},EH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new BH(c,e,g,a.D[b]||CH)},FH,GH;var HH=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=k(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Qv=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,P.C.Nf))},W=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:(FH!=null||(FH=new DH(uH,wH)),e=EH(FH,b,c));d[b]=e};
HH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.D[a])==null?void 0:(e=d.J)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!cd(c))return!1;W(this,a,Object.assign(c,b));return!0};var IH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.D)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};
HH.prototype.copyToHitData=function(a,b,c){var d=O(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&fb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===P.C.Nf){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,P.C.Nf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(GH!=null||(GH=new DH(zH,AH)),e=EH(GH,b,c));d[b]=e},JH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},jw=function(a,b,c){var d=a.target.destinationId;tm||(d=Im(d));var e=Yw(d);return e&&e[b]!==void 0?e[b]:c};function KH(a,b){var c;if(!ah(a)||!bh(b))throw H(this.getName(),["Object","Object|undefined"],arguments);var d=sd(b)||{},e=sd(a,this.M,1).yb(),f=e.F;d.omitEventContext&&(f=rq(new gq(e.F.eventId,e.F.priorityId)));var g=new HH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=IH(e),m=k(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;W(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=JH(e),r=k(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;S(g,u,q[u])}g.isAborted=e.isAborted;c=td(Gw(g),this.M,1);return c}KH.N="internal.copyPreHit";function LH(a,b){var c=null;return td(c,this.M,2)}LH.publicName="createArgumentsQueue";function MH(a){return td(function(c){var d=hC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
hC(),n=m&&m.getByName&&m.getByName(f);return(new l.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.M,1)}MH.N="internal.createGaCommandQueue";function NH(a){return td(function(){if(!eb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.M,
wh(oF(this).Gb())?2:1)}NH.publicName="createQueue";function OH(a,b){var c=null;if(!hh(a)||!ih(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new pd(new RegExp(a,d))}catch(e){}return c}OH.N="internal.createRegex";function PH(a){}PH.N="internal.declareConsentState";function QH(a){var b="";return b}QH.N="internal.decodeUrlHtmlEntities";function RH(a,b,c){var d;return d}RH.N="internal.decorateUrlWithGaCookies";function SH(){}SH.N="internal.deferCustomEvents";function TH(a){var b;J(this,"detect_user_provided_data","auto");var c=sd(a)||{},d=xx({ve:!!c.includeSelector,we:!!c.includeVisibility,Uf:c.excludeElementSelectors,Wb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Oa;var e=new gd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(UH(f[g]));d.Ej!==void 0&&b.set("preferredEmailElement",UH(d.Ej));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(kc&&
kc.userAgent||"")){}return b}
var VH=function(a){switch(a){case vx.fc:return"email";case vx.xd:return"phone_number";case vx.pd:return"first_name";case vx.wd:return"last_name";case vx.Qi:return"street";case vx.Mh:return"city";case vx.Li:return"region";case vx.Kf:return"postal_code";case vx.De:return"country"}},UH=function(a){var b=new Oa;b.set("userData",a.ma);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case vx.fc:b.set("type","email")}return b};TH.N="internal.detectUserProvidedData";
function YH(a,b){return f}YH.N="internal.enableAutoEventOnClick";
function fI(a,b){return p}fI.N="internal.enableAutoEventOnElementVisibility";function gI(){}gI.N="internal.enableAutoEventOnError";var hI={},iI=[],jI={},kI=0,lI=0;
function rI(a,b){var c=this;return d}rI.N="internal.enableAutoEventOnFormInteraction";
function wI(a,b){var c=this;return f}wI.N="internal.enableAutoEventOnFormSubmit";
function BI(){var a=this;}BI.N="internal.enableAutoEventOnGaSend";var CI={},DI=[];
var FI=function(a,b){var c=""+b;if(CI[c])CI[c].push(a);else{var d=[a];CI[c]=d;var e=EI("gtm.historyChange-v2"),f=-1;DI.push(function(g){f>=0&&l.clearTimeout(f);b?f=l.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},EI=function(a){var b=l.location.href,c={source:null,state:l.history.state||null,url:Lk(Ok(b)),hb:Ik(Ok(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.hb!==d.hb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.hb,
"gtm.newUrlFragment":d.hb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;KD(h)}}},GI=function(a,b){var c=l.history,d=c[a];if(eb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=l.location.href;b({source:a,state:e,url:Lk(Ok(h)),hb:Ik(Ok(h),"fragment")})}}catch(e){}},II=function(a){l.addEventListener("popstate",function(b){var c=HI(b);a({source:"popstate",state:b.state,url:Lk(Ok(c)),hb:Ik(Ok(c),
"fragment")})})},JI=function(a){l.addEventListener("hashchange",function(b){var c=HI(b);a({source:"hashchange",state:null,url:Lk(Ok(c)),hb:Ik(Ok(c),"fragment")})})},HI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||l.location.href};
function KI(a,b){var c=this;if(!bh(a))throw H(this.getName(),["Object|undefined","any"],arguments);kF([function(){J(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!rF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<DI.length;n++)DI[n](m)},f=qF(b),FI(f,e),sF(d,"reg",FI)):g=EI("gtm.historyChange");JI(g);II(g);GI("pushState",
g);GI("replaceState",g);sF(d,"init",!0)}else if(d==="ehl"){var h=rF(d,"reg");h&&(f=qF(b),h(f,e))}d==="hl"&&(f=void 0);return f}KI.N="internal.enableAutoEventOnHistoryChange";var LI=["http://","https://","javascript:","file://"];
function PI(a,b){var c=this;return h}PI.N="internal.enableAutoEventOnLinkClick";var QI,RI;
function bJ(a,b){var c=this;return d}bJ.N="internal.enableAutoEventOnScroll";function cJ(a){return function(){if(a.limit&&a.yj>=a.limit)a.Eh&&l.clearInterval(a.Eh);else{a.yj++;var b=ub();KD({event:a.eventName,"gtm.timerId":a.Eh,"gtm.timerEventNumber":a.yj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Im,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Im,"gtm.triggers":a.Aq})}}}
function dJ(a,b){
return f}dJ.N="internal.enableAutoEventOnTimer";var dc=wa(["data-gtm-yt-inspected-"]),fJ=["www.youtube.com","www.youtube-nocookie.com"],gJ,hJ=!1;
function rJ(a,b){var c=this;return e}rJ.N="internal.enableAutoEventOnYouTubeActivity";hJ=!1;function sJ(a,b){if(!hh(a)||!bh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?sd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Dh(f,c);return e}sJ.N="internal.evaluateBooleanExpression";var tJ;function uJ(a){var b=!1;return b}uJ.N="internal.evaluateMatchingRules";
var vJ=function(a){switch(a){case M.K.Ia:return[iw,fw,dw,cw,kw,$y,Sv,Gz,tz,hw,hz,oz,gw];case M.K.Uj:return[iw,fw,cw,kw,$y];case M.K.X:return[iw,$v,fw,cw,kw,Cz,Lz,zz,Kz,Jz,Iz,Hz,Gz,tz,sz,qz,pz,nz,dz,cz,rz,hz,yz,mz,lz,jz,Bz,xz,dw,aw,hw,wz,iz,Fz,oz,Az,bz,gz,vz,kz,Dz,Ez,ez,gw];case M.K.Ji:return[iw,$v,fw,cw,kw,Cz,Lz,tz,bw,hz,yz,Bz,aw,dw,hw,wz,Fz,oz,Az,bz,ez,gw];case M.K.oa:return[iw,$v,fw,cw,kw,Cz,Lz,zz,Kz,Jz,Iz,Hz,Gz,tz,sz,nz,rz,hz,yz,mz,Bz,aw,dw,hw,wz,iz,Fz,oz,Az,bz,Dz,ez,gw];case M.K.Ua:return[iw,
$v,fw,cw,kw,Cz,Lz,Kz,Gz,tz,rz,hz,bw,yz,jz,Bz,aw,dw,hw,wz,iz,Fz,oz,Az,bz,ez,gw];case M.K.La:return[iw,$v,fw,cw,kw,Cz,Lz,Kz,Gz,tz,rz,hz,bw,yz,jz,Bz,aw,dw,hw,wz,iz,Fz,oz,Az,bz,ez,gw];default:return[iw,$v,fw,cw,kw,Cz,Lz,zz,Kz,Jz,Iz,Hz,Gz,tz,sz,qz,pz,nz,dz,cz,rz,hz,yz,mz,lz,jz,Bz,xz,aw,dw,hw,wz,iz,Fz,oz,Az,bz,gz,vz,kz,Dz,Ez,ez,gw]}},wJ=function(a){for(var b=vJ(R(a,P.C.fa)),c=0;c<b.length&&(b[c](a),!a.isAborted);c++);},xJ=function(a,b,c,d){var e=new HH(b,c,d);S(e,P.C.fa,a);S(e,P.C.Ja,!0);S(e,P.C.jb,ub());
S(e,P.C.Ll,d.eventMetadata[P.C.Ja]);return e},yJ=function(a,b,c,d){function e(t,u){for(var v=k(h),w=v.next();!w.done;w=v.next()){var x=w.value;x.isAborted=!1;S(x,P.C.Ja,!0);S(x,P.C.ka,!0);S(x,P.C.jb,ub());S(x,P.C.Be,t);S(x,P.C.Ce,u)}}function f(t){for(var u={},v=0;v<h.length;u={lb:void 0},v++)if(u.lb=h[v],!t||t(R(u.lb,P.C.fa)))if(!R(u.lb,P.C.ka)||R(u.lb,P.C.fa)===M.K.Ia||sp(q))wJ(h[v]),R(u.lb,P.C.Ja)||u.lb.isAborted||(BB(u.lb),R(u.lb,P.C.fa)===M.K.Ia&&(Uv(u.lb,function(){f(function(w){return w===
M.K.Ia})}),Qv(u.lb,L.m.nf)===void 0&&r===void 0&&(r=ho(ao.aa.ph,function(w){return function(){sp(L.m.W)&&(S(w.lb,P.C.Of,!0),S(w.lb,P.C.ka,!1),W(w.lb,L.m.ka),f(function(x){return x===M.K.Ia}),S(w.lb,P.C.Of,!1),io(ao.aa.ph,r),r=void 0)}}(u)))))}var g=d.isGtmEvent&&a===""?{id:"",prefix:"",destinationId:"",ids:[]}:Op(a,d.isGtmEvent);if(g){var h=[];if(d.eventMetadata[P.C.sd]){var m=d.eventMetadata[P.C.sd];Array.isArray(m)||(m=[m]);for(var n=0;n<m.length;n++){var p=xJ(m[n],g,b,d);S(p,P.C.Ja,!1);h.push(p)}}else b===
L.m.ra&&(E(24)?h.push(xJ(M.K.Ia,g,b,d)):h.push(xJ(M.K.Ji,g,b,d))),h.push(xJ(M.K.X,g,b,d)),h.push(xJ(M.K.Ua,g,b,d)),h.push(xJ(M.K.La,g,b,d)),h.push(xJ(M.K.oa,g,b,d));var q=[L.m.V,L.m.W],r=void 0;vp(function(){f();var t=E(29)&&!sp([L.m.Na]);if(!sp(q)||t){var u=q;t&&(u=[].concat(ua(u),[L.m.Na]));up(function(v){var w,x,z;w=v.consentEventId;x=v.consentPriorityId;z=v.consentTypes;e(w,x);z&&z.length===1&&z[0]===L.m.Na?f(function(B){return B===M.K.oa}):f()},u)}},q)}};function dK(){return yr(7)&&yr(9)&&yr(10)};function ZK(a,b,c,d){}ZK.N="internal.executeEventProcessor";function $K(a){var b;return td(b,this.M,1)}$K.N="internal.executeJavascriptString";function aL(a){var b;return b};function bL(a){var b="";return b}bL.N="internal.generateClientId";function cL(a){var b={};return td(b)}cL.N="internal.getAdsCookieWritingOptions";function dL(a,b){var c=!1;return c}dL.N="internal.getAllowAdPersonalization";function eL(){var a;return a}eL.N="internal.getAndResetEventUsage";function fL(a,b){b=b===void 0?!0:b;var c;return c}fL.N="internal.getAuid";var gL=null;
function hL(){var a=new Oa;return a}
hL.publicName="getContainerVersion";function iL(a,b){b=b===void 0?!0:b;var c;return c}iL.publicName="getCookieValues";function jL(){var a="";return a}jL.N="internal.getCorePlatformServicesParam";function kL(){return qo()}kL.N="internal.getCountryCode";function lL(){var a=[];a=Am();return td(a)}lL.N="internal.getDestinationIds";function mL(a){var b=new Oa;return b}mL.N="internal.getDeveloperIds";function nL(a){var b;return b}nL.N="internal.getEcsidCookieValue";function oL(a,b){var c=null;return c}oL.N="internal.getElementAttribute";function pL(a){var b=null;return b}pL.N="internal.getElementById";function qL(a){var b="";return b}qL.N="internal.getElementInnerText";function rL(a,b){var c=null;return td(c)}rL.N="internal.getElementProperty";function sL(a){var b;return b}sL.N="internal.getElementValue";function tL(a){var b=0;return b}tL.N="internal.getElementVisibilityRatio";function uL(a){var b=null;return b}uL.N="internal.getElementsByCssSelector";
function vL(a){var b;if(!hh(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=oF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=k(n),B=z.next();!B.done;B=
z.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===h?x+".":x+C}x&&w.push(x);for(var F=k(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=td(c,this.M,1);return b}vL.N="internal.getEventData";var wL={};wL.enableCcdSendTo=E(41);wL.enableConversionAutoDataAnalysis=E(188);wL.enableDecodeUri=E(92);wL.enableDeferAllEnhancedMeasurement=E(58);wL.enableGa4OutboundClicksFix=E(96);wL.enableGaAdsConversions=E(122);wL.enableGaAdsConversionsClientId=E(121);wL.enableOverrideAdsCps=E(170);wL.enableUrlDecodeEventUsage=E(139);wL.enableZoneConfigInChildContainers=E(142);wL.useEnableAutoEventOnFormApis=E(156);function xL(){return td(wL)}xL.N="internal.getFlags";function yL(){var a;return a}yL.N="internal.getGsaExperimentId";function zL(){return new pd(yE)}zL.N="internal.getHtmlId";function AL(a){var b;return b}AL.N="internal.getIframingState";function BL(a,b){var c={};return td(c)}BL.N="internal.getLinkerValueFromLocation";function CL(){var a=new Oa;if(arguments.length!==0)throw H(this.getName(),[],arguments);var b=Nv();b!==void 0&&a.set(L.m.zf,b||"error");var c=xr();c&&a.set(L.m.hd,c);var d=wr();d&&a.set(L.m.md,d);var e=Av.gppString;e&&a.set(L.m.af,e);var f=Av.D;f&&a.set(L.m.Ze,f);return a}CL.N="internal.getPrivacyStrings";function DL(a,b){var c;if(!hh(a)||!hh(b))throw H(this.getName(),["string","string"],arguments);var d=Yw(a)||{};c=td(d[b],this.M);return c}DL.N="internal.getProductSettingsParameter";function EL(a,b){var c;return c}EL.publicName="getQueryParameters";function FL(a,b){var c;return c}FL.publicName="getReferrerQueryParameters";function GL(a){var b="";return b}GL.publicName="getReferrerUrl";function HL(){return ro()}HL.N="internal.getRegionCode";function IL(a,b){var c;return c}IL.N="internal.getRemoteConfigParameter";function JL(){var a=new Oa;a.set("width",0);a.set("height",0);return a}JL.N="internal.getScreenDimensions";function KL(){var a="";return a}KL.N="internal.getTopSameDomainUrl";function LL(){var a="";return a}LL.N="internal.getTopWindowUrl";function ML(a){var b="";return b}ML.publicName="getUrl";function NL(){J(this,"get_user_agent");return kc.userAgent}NL.N="internal.getUserAgent";function OL(){var a;return a?td(Ty(a)):a}OL.N="internal.getUserAgentClientHints";function WL(){return l.gaGlobal=l.gaGlobal||{}}function XL(){var a=WL();a.hid=a.hid||kb();return a.hid}function YL(a,b){var c=WL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function vM(a){(ky(a)||hk())&&W(a,L.m.al,ro()||qo());!ky(a)&&hk()&&W(a,L.m.rl,"::")}function wM(a){if(hk()&&!ky(a)){var b=E(176);E(187)&&E(201)&&(b=b&&!uo());b&&W(a,L.m.Ok,!0);if(E(78)){dw(a);ew(a,Lp.Af.bn,Oo(O(a.F,L.m.ib)));var c=Lp.Af.dn;var d=O(a.F,L.m.Gc);ew(a,c,d===!0?1:d===!1?0:void 0);ew(a,Lp.Af.Zm,Oo(O(a.F,L.m.wb)));ew(a,Lp.Af.Xm,rs(No(O(a.F,L.m.pb)),No(O(a.F,L.m.Pb))))}}};var SM={AW:ao.aa.Tm,G:ao.aa.eo,DC:ao.aa.bo};function TM(a){var b=Xi(a);return""+Ur(b.map(function(c){return c.value}).join("!"))}function UM(a){var b=Op(a);return b&&SM[b.prefix]}function VM(a,b){var c=a[b];c&&(c.clearTimerId&&l.clearTimeout(c.clearTimerId),c.clearTimerId=l.setTimeout(function(){delete a[b]},36E5))};var zN=window,AN=document,BN=function(a){var b=zN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||AN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&zN["ga-disable-"+a]===!0)return!0;try{var c=zN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(AN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return AN.getElementById("__gaOptOutExtension")?!0:!1};
function NN(a){nb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[L.m.Ub]||{};nb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function uO(a,b){}function vO(a,b){var c=function(){};return c}
function wO(a,b,c){};var xO=vO;function zO(a,b,c){var d=this;}zO.N="internal.gtagConfig";
function BO(a,b){}
BO.publicName="gtagSet";function CO(){var a={};return a};function DO(a){}DO.N="internal.initializeServiceWorker";function EO(a,b){}EO.publicName="injectHiddenIframe";var FO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function GO(a,b,c,d,e){}GO.N="internal.injectHtml";var KO={};
function MO(a,b,c,d){}var NO={dl:1,id:1},OO={};
function PO(a,b,c,d){}E(160)?PO.publicName="injectScript":MO.publicName="injectScript";PO.N="internal.injectScript";function QO(){return vo()}QO.N="internal.isAutoPiiEligible";function RO(a){var b=!0;return b}RO.publicName="isConsentGranted";function SO(a){var b=!1;return b}SO.N="internal.isDebugMode";function TO(){return to()}TO.N="internal.isDmaRegion";function UO(a){var b=!1;return b}UO.N="internal.isEntityInfrastructure";function VO(a){var b=!1;if(!mh(a))throw H(this.getName(),["number"],[a]);b=E(a);return b}VO.N="internal.isFeatureEnabled";function WO(){var a=!1;return a}WO.N="internal.isFpfe";function XO(){var a=!1;return a}XO.N="internal.isGcpConversion";function YO(){var a=!1;return a}YO.N="internal.isLandingPage";function ZO(){var a;return a}ZO.N="internal.isSafariPcmEligibleBrowser";function $O(){var a=Jh(function(b){oF(this).log("error",b)});a.publicName="JSON";return a};function aP(a){var b=void 0;return td(b)}aP.N="internal.legacyParseUrl";function bP(){return!1}
var cP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function dP(){}dP.publicName="logToConsole";function eP(a,b){}eP.N="internal.mergeRemoteConfig";function fP(a,b,c){c=c===void 0?!0:c;var d=[];return td(d)}fP.N="internal.parseCookieValuesFromString";function gP(a){var b=void 0;return b}gP.publicName="parseUrl";function hP(a){if(!ah(a))throw H(this.getName(),["Object"],arguments);var b=sd(a,this.M,1).yb(),c={};dd(b.F.D,c);IH(b,c);var d={};JH(b,d);d[P.C.Nl]=!0;var e={eventMetadata:d},f=b.F.eventId,g=Ow(b.target.destinationId,b.eventName,c);Rw(g,f,e);}hP.N="internal.processAsNewEvent";function iP(a,b,c){var d;return d}iP.N="internal.pushToDataLayer";function jP(a){var b=ya.apply(1,arguments),c=!1;if(!hh(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=k(b),f=e.next();!f.done;f=e.next())d.push(sd(f.value,this.M,1));try{J.apply(null,d),c=!0}catch(g){return!1}return c}jP.publicName="queryPermission";function kP(a){var b=this;}kP.N="internal.queueAdsTransmission";function lP(a,b){var c=void 0;return c}lP.publicName="readAnalyticsStorage";function mP(){var a="";return a}mP.publicName="readCharacterSet";function nP(){return Pj}nP.N="internal.readDataLayerName";function oP(){var a="";return a}oP.publicName="readTitle";function pP(a,b){var c=this;if(!hh(a)||!dh(b))throw H(this.getName(),["string","function"],arguments);Jw(a,function(d){b.invoke(c.M,td(d,c.M,1))});}pP.N="internal.registerCcdCallback";function qP(a,b){return!0}qP.N="internal.registerDestination";var rP=["config","event","get","set"];function sP(a,b,c){}sP.N="internal.registerGtagCommandListener";function tP(a,b){var c=!1;return c}tP.N="internal.removeDataLayerEventListener";function uP(a,b){}
uP.N="internal.removeFormData";function vP(){}vP.publicName="resetDataLayer";function wP(a,b,c){var d=void 0;return d}wP.N="internal.scrubUrlParams";function xP(a){}xP.N="internal.sendAdsHit";function yP(a,b,c,d){if(arguments.length<2||!bh(d)||!bh(c))throw H(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?sd(c):{},f=sd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?sd(d):{},m=oF(this);h.originatingEntity=eG(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};dd(e,q);var r={};dd(h,r);var t=Ow(p,b,q);Rw(t,h.eventId||m.eventId,r)}}}yP.N="internal.sendGtagEvent";function zP(a,b,c){}zP.publicName="sendPixel";function AP(a,b){}AP.N="internal.setAnchorHref";function BP(a){}BP.N="internal.setContainerConsentDefaults";function CP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}CP.publicName="setCookie";function DP(a){}DP.N="internal.setCorePlatformServices";function EP(a,b){}EP.N="internal.setDataLayerValue";function FP(a){}FP.publicName="setDefaultConsentState";function GP(a,b){}GP.N="internal.setDelegatedConsentType";function HP(a,b){}HP.N="internal.setFormAction";function IP(a,b,c){c=c===void 0?!1:c;}IP.N="internal.setInCrossContainerData";function JP(a,b,c){return!1}JP.publicName="setInWindow";function KP(a,b,c){}KP.N="internal.setProductSettingsParameter";function LP(a,b,c){if(!hh(a)||!hh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Rq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!cd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=sd(c,this.M,1);}LP.N="internal.setRemoteConfigParameter";function MP(a,b){}MP.N="internal.setTransmissionMode";function NP(a,b,c,d){var e=this;}NP.publicName="sha256";function OP(a,b,c){}
OP.N="internal.sortRemoteConfigParameters";function PP(a){}PP.N="internal.storeAdsBraidLabels";function QP(a,b){var c=void 0;return c}QP.N="internal.subscribeToCrossContainerData";var RP={},SP={};RP.getItem=function(a){var b=null;J(this,"access_template_storage");var c=oF(this).Gb();SP[c]&&(b=SP[c].hasOwnProperty("gtm."+a)?SP[c]["gtm."+a]:null);return b};RP.setItem=function(a,b){J(this,"access_template_storage");var c=oF(this).Gb();SP[c]=SP[c]||{};SP[c]["gtm."+a]=b;};
RP.removeItem=function(a){J(this,"access_template_storage");var b=oF(this).Gb();if(!SP[b]||!SP[b].hasOwnProperty("gtm."+a))return;delete SP[b]["gtm."+a];};RP.clear=function(){J(this,"access_template_storage"),delete SP[oF(this).Gb()];};RP.publicName="templateStorage";function TP(a,b){var c=!1;if(!gh(a)||!hh(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}TP.N="internal.testRegex";function UP(a){var b;return b};function VP(a){var b;return b}VP.N="internal.unsiloId";function WP(a,b){var c;return c}WP.N="internal.unsubscribeFromCrossContainerData";function XP(a){}XP.publicName="updateConsentState";function YP(a){var b=!1;return b}YP.N="internal.userDataNeedsEncryption";var ZP;function $P(a,b,c){ZP=ZP||new Uh;ZP.add(a,b,c)}function aQ(a,b){var c=ZP=ZP||new Uh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=eb(b)?ph(a,b):qh(a,b)}
function bQ(){return function(a){var b;var c=ZP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.M.D;if(e){var f=!1,g=e.Gb();if(g){wh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function cQ(){var a=function(c){return void aQ(c.N,c)},b=function(c){return void $P(c.publicName,c)};b(iF);b(pF);b(EG);b(GG);b(HG);b(OG);b(QG);b(LH);b($O());b(NH);b(hL);b(iL);b(EL);b(FL);b(GL);b(ML);b(BO);b(EO);b(RO);b(dP);b(gP);b(jP);b(mP);b(oP);b(zP);b(CP);b(FP);b(JP);b(NP);b(RP);b(XP);$P("Math",uh());$P("Object",Sh);$P("TestHelper",Wh());$P("assertApi",rh);$P("assertThat",sh);$P("decodeUri",xh);$P("decodeUriComponent",yh);$P("encodeUri",zh);$P("encodeUriComponent",Ah);$P("fail",Fh);$P("generateRandom",
Gh);$P("getTimestamp",Hh);$P("getTimestampMillis",Hh);$P("getType",Ih);$P("makeInteger",Kh);$P("makeNumber",Lh);$P("makeString",Mh);$P("makeTableMap",Nh);$P("mock",Qh);$P("mockObject",Rh);$P("fromBase64",aL,!("atob"in l));$P("localStorage",cP,!bP());$P("toBase64",UP,!("btoa"in l));a(hF);a(lF);a(GF);a(SF);a(ZF);a(dG);a(tG);a(CG);a(FG);a(IG);a(JG);a(KG);a(LG);a(MG);a(NG);a(PG);a(RG);a(KH);a(MH);a(OH);a(PH);a(QH);a(RH);a(SH);a(TH);a(YH);a(fI);a(gI);a(rI);a(wI);a(BI);a(KI);a(PI);a(bJ);a(dJ);a(rJ);a(sJ);
a(uJ);a(ZK);a($K);a(bL);a(cL);a(dL);a(eL);a(fL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(HL);a(IL);a(JL);a(KL);a(LL);a(OL);a(zO);a(DO);a(GO);a(PO);a(QO);a(SO);a(TO);a(UO);a(VO);a(WO);a(XO);a(YO);a(ZO);a(aP);a(rG);a(eP);a(fP);a(hP);a(iP);a(kP);a(nP);a(pP);a(qP);a(sP);a(tP);a(uP);a(wP);a(xP);a(yP);a(AP);a(BP);a(DP);a(EP);a(GP);a(HP);a(IP);a(KP);a(LP);a(MP);a(OP);a(PP);a(QP);a(TP);a(VP);a(WP);a(YP);aQ("internal.IframingStateSchema",
CO());
E(104)&&a(jL);E(160)?b(PO):b(MO);E(177)&&b(lP);return bQ()};var fF;
function dQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;fF=new Ne;eQ();vf=eF();var e=fF,f=cQ(),g=new ld("require",f);g.eb();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Rf(n,d[m]);try{fF.execute(n),E(120)&&dl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(If=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");dk[q]=
["sandboxedScripts"]}fQ(b)}function eQ(){fF.D.D.O=function(a,b,c){Cp.SANDBOXED_JS_SEMAPHORE=Cp.SANDBOXED_JS_SEMAPHORE||0;Cp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Cp.SANDBOXED_JS_SEMAPHORE--}}}function fQ(a){a&&nb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");dk[e]=dk[e]||[];dk[e].push(b)}})};function gQ(a){Rw(Lw("developer_id."+a,!0),0,{})};var hQ=Array.isArray;function iQ(a,b){return dd(a,b||null)}function X(a){return window.encodeURIComponent(a)}function jQ(a,b,c){Ac(a,b,c)}function kQ(a,b){if(!a)return!1;var c=Ik(Ok(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function lQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var uQ=l.clearTimeout,vQ=l.setTimeout;function wQ(a,b,c){if(Qr()){b&&A(b)}else return wc(a,b,c,void 0)}function xQ(){return l.location.href}function yQ(a,b){return nk(a,b||2)}function zQ(a,b){l[a]=b}function AQ(a,b,c){b&&(l[a]===void 0||c&&!l[a])&&(l[a]=b);return l[a]}function BQ(a,b){if(Qr()){b&&A(b)}else yc(a,b)}

var CQ={};var Z={securityGroups:{}};

Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},U:function(){return{}}}},Z.__access_template_storage.H="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage.runInSiloedMode=!1;
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=yQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.H="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v.runInSiloedMode=!1;

Z.securityGroups.rep=["google"],Z.__rep=function(a){var b=Im(a.vtp_containerId),c=Op(b,!0);if(c){var d,e;switch(c.prefix){case "AW":d=yJ;e=Xm.Z.Ea;break;case "DC":d=PJ;e=Xm.Z.Ea;break;case "GF":d=UJ;e=Xm.Z.Db;break;case "HA":d=$J;e=Xm.Z.Db;break;case "UA":d=xK;e=Xm.Z.Db;break;case "MC":d=xO(c,a.vtp_gtmEventId);e=Xm.Z.Ec;break;default:A(a.vtp_gtmOnFailure);return}d?(A(a.vtp_gtmOnSuccess),E(185)?Nq(b,d,e,a.vtp_remoteConfig):(Nq(a.vtp_containerId,d,e),a.vtp_remoteConfig&&Tq(b,a.vtp_remoteConfig||{}))):
A(a.vtp_gtmOnFailure)}else A(a.vtp_gtmOnFailure)},Z.__rep.H="rep",Z.__rep.isVendorTemplate=!0,Z.__rep.priorityOverride=0,Z.__rep.isInfrastructure=!1,Z.__rep.runInSiloedMode=!0;
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!fb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&Fg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},U:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.H="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!fb(g))throw e(f,{},"Keys must be strings.");if(c!==
"any"){try{if(Fg(g,d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},U:a}})}();


Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.H="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();




Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.H="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!fb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},U:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.H="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&
e!=="code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},U:a}})}();









Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Ow(String(b.streamId),d,c);Rw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.H="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get.runInSiloedMode=!1;




var Fp={dataLayer:ok,callback:function(a){ck.hasOwnProperty(a)&&eb(ck[a])&&ck[a]();delete ck[a]},bootstrap:0};
function DQ(){Ep();Lm();UB();xb(dk,Z.securityGroups);var a=Gm(Hm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;cp(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||N(142);Hf={Mo:Xf}}var EQ=!1;
function no(){try{if(EQ||!Um()){Lj();Ij.T=Li(18,"");
Ij.Eb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Ij.Za="ad_storage|analytics_storage|ad_user_data";Ij.Ca="5690";Ij.Ca="5690";Jm();if(E(109)){}og[8]=!0;var a=Dp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});jp(a);Bp();WE();rr();Ip();if(Mm()){oG();EB().removeExternalRestrictions(Em());}else{Xy();QB();Ff();Bf=Z;Cf=GE;Zf=new fg;dQ();DQ();lo||(ko=po());
yp();SD();eD();yD=!1;y.readyState==="complete"?AD():Bc(l,"load",AD);ZC();dl&&(vq(Jq),l.setInterval(Iq,864E5),vq(XE),vq(wC),vq(eA),vq(Mq),vq(bF),vq(HC),E(120)&&(vq(BC),vq(CC),vq(DC)),YE={},vq(ZE),Oi());el&&(Qn(),bq(),UD(),YD(),WD(),Gn("bt",String(Ij.D?2:Ij.O?1:0)),Gn("ct",String(Ij.D?0:Ij.O?1:Qr()?2:3)),VD());wE();$n(1);pG();bE();bk=ub();Fp.bootstrap=bk;Ij.R&&RD();E(109)&&yA();E(134)&&(typeof l.name==="string"&&zb(l.name,"web-pixel-sandbox-CUSTOM")&&Tc()?gQ("dMDg0Yz"):l.Shopify&&(gQ("dN2ZkMj"),Tc()&&gQ("dNTU0Yz")))}}}catch(b){$n(4),Fq()}}
(function(a){function b(){n=y.documentElement.getAttribute("data-tag-assistant-present");Qo(n)&&(m=h.ml)}function c(){m&&nc?g(m):a()}if(!l[Li(37,"__TAGGY_INSTALLED")]){var d=!1;if(y.referrer){var e=Ok(y.referrer);d=Kk(e,"host")===Li(38,"cct.google")}if(!d){var f=bs(Li(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(l[Li(37,"__TAGGY_INSTALLED")]=!0,wc(Li(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";Vj&&(v="OGT",w="GTAG");
var x=Li(23,"google.tagmanager.debugui2.queue"),z=l[x];z||(z=[],l[x]=z,wc("https://"+Mj.Bg+"/debug/bootstrap?id="+cg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Sr()));var B={messageType:"CONTAINER_STARTING",data:{scriptSource:nc,containerProduct:v,debug:!1,id:cg.ctid,targetRef:{ctid:cg.ctid,isDestination:vm()},aliases:ym(),destinations:wm()}};B.data.resume=function(){a()};Mj.Wm&&(B.data.initialPublish=!0);z.push(B)},h={io:1,pl:2,Dl:3,hk:4,ml:5};h[h.io]="GTM_DEBUG_LEGACY_PARAM";h[h.pl]="GTM_DEBUG_PARAM";h[h.Dl]="REFERRER";
h[h.hk]="COOKIE";h[h.ml]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Ik(l.location,"query",!1,void 0,"gtm_debug");Qo(p)&&(m=h.pl);if(!m&&y.referrer){var q=Ok(y.referrer);Kk(q,"host")===Li(24,"tagassistant.google.com")&&(m=h.Dl)}if(!m){var r=bs("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.hk)}m||b();if(!m&&Po(n)){var t=!1;Bc(y,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);l.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&EQ&&!po()["0"]?mo():no()});

})()

