tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.eventsBarInputs={},function(e,n){"use strict";const t=e(document);n.selectors={input:'[data-js="tribe-events-events-bar-input-control-input"]',inputWrapper:'[data-js="tribe-events-events-bar-input-control"]'},n.handleInputChange=function(e){const n=e.data.target;e.data.wrapper.toggleClass(e.data.inputClassFocus,""!==n.val().trim())},n.unbindInputEvents=function(t){t.find(n.selectors.inputWrapper).each((function(t,s){const i=e(s).find(n.selectors.input);i.length&&i.off()}))},n.bindInputEvents=function(t){t.find(n.selectors.inputWrapper).each((function(t,s){const i=s.className.match(/tribe-events-c-search__input-control--[a-z]+/);if(!i)return;const r=i[0]+"-focus",a=e(s),c=a.find(n.selectors.input);c.length&&(a.toggleClass(r,""!==c.val().trim()),c.on("change",{target:c,wrapper:a,inputClassFocus:r},n.handleInputChange))}))},n.unbindEvents=function(e,t,s){const i=e.data.container;n.unbindInputEvents(i),i.off("beforeAjaxSuccess.tribeEvents",n.unbindEvents)},n.bindEvents=function(e,t,s,i){s.find(n.selectors.inputWrapper).length&&(n.bindInputEvents(s),s.on("beforeAjaxSuccess.tribeEvents",{container:s},n.unbindEvents))},n.ready=function(){t.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,n.bindEvents)},e(n.ready)}(jQuery,tribe.events.views.eventsBarInputs),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.eventsBarInputs={}