tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.breakpoints={},function(e,t){"use strict";const n=e(document);t.selectors={container:'[data-js="tribe-events-view"]',dataScript:'[data-js="tribe-events-view-data"]',breakpointClassPrefix:"tribe-common--breakpoint-"},t.breakpoints={},t.setContainerClasses=function(e,n){Object.keys(n.breakpoints).forEach((function(i){const s=t.selectors.breakpointClassPrefix+i;t.breakpoints[i]=n.breakpoints[i],e.outerWidth()<n.breakpoints[i]?e.removeClass(s):e.addClass(s)}))},t.handleResize=function(e){t.setContainerClasses(e.data.container,e.data.data)},t.unbindEvents=function(e){e.off("resize.tribeEvents",t.handleResize).off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.bindEvents=function(e,n){e.on("resize.tribeEvents",{container:e,data:n},t.handleResize).on("beforeAjaxSuccess.tribeEvents",{container:e},t.deinit)},t.deinit=function(e,n,i){t.unbindEvents(e.data.container)},t.initTasks=function(n,i){n instanceof jQuery||(n=e(n)),t.bindEvents(n,i),t.setContainerClasses(n,i),n.data("tribeEventsBreakpoints",{initialized:!0})},t.init=function(n,i,s,a){s instanceof jQuery||(s=e(s));const o=s.data("tribeEventsBreakpoints");o&&o.initialized||t.initTasks(s,a)},t.setup=function(n){const i=e(n);if(!i.is(t.selectors.container))return;const s=i.find(t.selectors.dataScript);let a={};s.length&&(a=JSON.parse(s.text().trim())),t.initTasks(i,a)},t.ready=function(){n.on("afterSetup.tribeEvents",t.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.breakpoints),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.breakpoints={}