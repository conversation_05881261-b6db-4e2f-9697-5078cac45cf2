#!/usr/bin/env python3
"""
Simple GitHub Issues Import Script for Segishop Project
This creates all our project tasks as GitHub issues with proper organization.
"""

import requests
import json

def create_github_issues():
    print("🚀 Welcome to the Segishop Project Setup!")
    print("This will create all project tasks as GitHub issues.\n")
    
    # Get user input
    print("Please provide the following information:")
    username = input("Your GitHub username: ").strip()
    repo_name = input("Repository name (should be 'segishop-headless-migration'): ").strip()
    token = input("Your GitHub Personal Access Token: ").strip()
    
    if not username or not repo_name or not token:
        print("❌ Error: All fields are required!")
        return False
    
    # Setup API connection
    base_url = f"https://api.github.com/repos/{username}/{repo_name}"
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    print(f"\n🔗 Connecting to: {base_url}")
    
    # Test connection
    response = requests.get(base_url, headers=headers)
    if response.status_code != 200:
        print(f"❌ Error: Cannot access repository. Status: {response.status_code}")
        print("Please check your username, repository name, and token.")
        return False
    
    print("✅ Successfully connected to repository!")
    
    # Create labels first
    print("\n📋 Creating project labels...")
    labels = [
        {"name": "phase-1", "color": "0052cc", "description": "Phase 1: Setup & Infrastructure"},
        {"name": "phase-2", "color": "5319e7", "description": "Phase 2: Core Pages & Navigation"},
        {"name": "phase-3", "color": "b60205", "description": "Phase 3: E-commerce Core"},
        {"name": "phase-4", "color": "fbca04", "description": "Phase 4: Advanced Features"},
        {"name": "phase-5", "color": "0e8a16", "description": "Phase 5: Content & SEO"},
        {"name": "phase-6", "color": "006b75", "description": "Phase 6: Testing & Deployment"},
        {"name": "high-priority", "color": "d93f0b", "description": "High priority task"},
        {"name": "medium-priority", "color": "fbca04", "description": "Medium priority task"},
        {"name": "client-deliverable", "color": "ff6b6b", "description": "Produces client deliverable"}
    ]
    
    for label in labels:
        url = f"{base_url}/labels"
        response = requests.post(url, headers=headers, json=label)
        if response.status_code == 201:
            print(f"  ✅ Created label: {label['name']}")
        else:
            print(f"  ⚠️  Label {label['name']} might already exist")
    
    # Create milestones
    print("\n🎯 Creating project milestones...")
    milestones = [
        {"title": "Phase 1 Complete", "description": "Project Setup & Infrastructure completed"},
        {"title": "Phase 2 Complete", "description": "Core Pages & Navigation completed"},
        {"title": "Phase 3 Complete", "description": "E-commerce Core completed"},
        {"title": "Phase 4 Complete", "description": "Advanced Features completed"},
        {"title": "Phase 5 Complete", "description": "Content & SEO completed"},
        {"title": "Project Complete", "description": "Full migration completed and live"}
    ]
    
    milestone_numbers = {}
    for milestone in milestones:
        url = f"{base_url}/milestones"
        response = requests.post(url, headers=headers, json=milestone)
        if response.status_code == 201:
            milestone_data = response.json()
            milestone_numbers[milestone['title']] = milestone_data['number']
            print(f"  ✅ Created milestone: {milestone['title']}")
        else:
            print(f"  ⚠️  Milestone {milestone['title']} might already exist")
    
    # Create main phase issues
    print("\n📝 Creating phase issues...")
    phases = [
        {
            "title": "Phase 1: Project Setup & Infrastructure",
            "description": "Set up development environment, design system, and GraphQL integration",
            "labels": ["phase-1", "client-deliverable"],
            "milestone": "Phase 1 Complete",
            "tasks": [
                "1.1: Environment Setup & Planning (3-4 days)",
                "1.2: Design System & Component Library (4-5 days)", 
                "1.3: GraphQL Integration Setup (2-3 days)"
            ]
        },
        {
            "title": "Phase 2: Core Pages & Navigation", 
            "description": "Build homepage, navigation system, and basic page templates",
            "labels": ["phase-2", "client-deliverable"],
            "milestone": "Phase 2 Complete",
            "tasks": [
                "2.1: Homepage Development (4-5 days)",
                "2.2: Navigation & Menu System (3-4 days)",
                "2.3: Basic Pages (About, Contact, etc.) (3-4 days)"
            ]
        },
        {
            "title": "Phase 3: E-commerce Core",
            "description": "Build product catalog, shopping cart, and user authentication",
            "labels": ["phase-3", "client-deliverable"],
            "milestone": "Phase 3 Complete", 
            "tasks": [
                "3.1: Product Catalog System (6-7 days)",
                "3.2: Shopping Cart System (5-6 days)",
                "3.3: User Authentication System (4-5 days)"
            ]
        },
        {
            "title": "Phase 4: Advanced E-commerce Features",
            "description": "Implement checkout, payments, and advanced features",
            "labels": ["phase-4", "client-deliverable"],
            "milestone": "Phase 4 Complete",
            "tasks": [
                "4.1: Checkout Process (6-7 days)",
                "4.2: Advanced Features (Wishlist, Reviews, etc.) (5-6 days)"
            ]
        },
        {
            "title": "Phase 5: Content Management & SEO",
            "description": "Complete blog system, SEO optimization, and performance tuning",
            "labels": ["phase-5", "client-deliverable"],
            "milestone": "Phase 5 Complete",
            "tasks": [
                "5.1: Blog & Content Pages (4-5 days)",
                "5.2: SEO & Performance Optimization (3-4 days)"
            ]
        },
        {
            "title": "Phase 6: Testing & Deployment",
            "description": "Comprehensive testing, quality assurance, and go-live",
            "labels": ["phase-6", "client-deliverable"],
            "milestone": "Project Complete",
            "tasks": [
                "6.1: Testing & Quality Assurance (4-5 days)",
                "6.2: Deployment & Go-Live (3-4 days)"
            ]
        }
    ]
    
    phase_issues = {}
    for phase in phases:
        # Create phase issue body
        body = f"""# {phase['title']}

## Overview
{phase['description']}

## Main Tasks
"""
        for task in phase['tasks']:
            body += f"- [ ] {task}\n"
        
        body += """
## Success Criteria
- [ ] All tasks completed and tested
- [ ] Code reviewed and approved  
- [ ] Client demo completed successfully
- [ ] Documentation updated
- [ ] Ready for next phase

## Client Deliverables
This phase will deliver working functionality that can be demonstrated to the client.

## Notes
[Add any phase-specific notes here]
"""
        
        # Create the issue
        issue_data = {
            "title": phase['title'],
            "body": body,
            "labels": phase['labels']
        }
        
        if phase['milestone'] in milestone_numbers:
            issue_data['milestone'] = milestone_numbers[phase['milestone']]
        
        url = f"{base_url}/issues"
        response = requests.post(url, headers=headers, json=issue_data)
        
        if response.status_code == 201:
            issue = response.json()
            phase_issues[phase['title']] = issue['number']
            print(f"  ✅ Created issue #{issue['number']}: {phase['title']}")
        else:
            print(f"  ❌ Failed to create issue: {phase['title']}")
    
    # Create detailed task issues for Phase 1
    print("\n📋 Creating detailed Phase 1 tasks...")
    phase1_tasks = [
        {
            "title": "1.1.1: Development Environment Setup",
            "description": "Set up local WordPress, install GraphQL plugins, configure Node.js environment",
            "labels": ["phase-1", "high-priority"],
            "checklist": [
                "Set up local WordPress development environment",
                "Install and configure WPGraphQL plugin", 
                "Install and configure WooGraphQL plugin",
                "Set up Node.js development environment",
                "Configure Git repository with proper branching strategy"
            ]
        },
        {
            "title": "1.1.2: Project Structure Creation", 
            "description": "Create Next.js project with TypeScript, set up Tailwind CSS and tools",
            "labels": ["phase-1", "high-priority"],
            "checklist": [
                "Create Next.js 14 project with TypeScript",
                "Set up Tailwind CSS configuration",
                "Configure ESLint and Prettier",
                "Set up environment variables structure", 
                "Create folder structure for components, pages, and utilities"
            ]
        },
        {
            "title": "1.1.3: WordPress Backend Configuration",
            "description": "Configure WordPress for headless operation with proper security",
            "labels": ["phase-1", "high-priority"],
            "checklist": [
                "Install required headless WordPress plugins",
                "Configure CORS settings for API access",
                "Set up JWT authentication for secure API calls",
                "Configure GraphQL endpoint and test connectivity",
                "Document API endpoints and authentication flow"
            ]
        }
    ]
    
    for task in phase1_tasks:
        body = f"""# {task['title']}

## Description
{task['description']}

## Checklist
"""
        for item in task['checklist']:
            body += f"- [ ] {item}\n"
        
        body += """
## Definition of Done
- [ ] All checklist items completed
- [ ] Code implemented and working
- [ ] Documentation updated
- [ ] Ready for next task

## Resources
- [Add links to documentation]
- [Add links to tutorials if needed]

## Notes
[Add any task-specific notes here]
"""
        
        issue_data = {
            "title": task['title'],
            "body": body,
            "labels": task['labels']
        }
        
        if "Phase 1 Complete" in milestone_numbers:
            issue_data['milestone'] = milestone_numbers["Phase 1 Complete"]
        
        url = f"{base_url}/issues"
        response = requests.post(url, headers=headers, json=issue_data)
        
        if response.status_code == 201:
            issue = response.json()
            print(f"  ✅ Created task #{issue['number']}: {task['title']}")
        else:
            print(f"  ❌ Failed to create task: {task['title']}")
    
    print("\n🎉 Setup completed successfully!")
    print(f"\n📋 Next steps:")
    print(f"1. Go to: https://github.com/{username}/{repo_name}/issues")
    print(f"2. Create a new GitHub Project board")
    print(f"3. Add all these issues to your project board")
    print(f"4. Start working on task 1.1.1!")
    
    return True

if __name__ == "__main__":
    try:
        create_github_issues()
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("Please check your internet connection and try again.")
        input("Press Enter to exit...")
