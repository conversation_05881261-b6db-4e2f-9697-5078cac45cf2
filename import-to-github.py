#!/usr/bin/env python3
"""
GitHub Issues Import Script for Segishop Headless WordPress Migration
This script creates GitHub issues from our project roadmap structure.
"""

import json
import requests
import os
from datetime import datetime

class GitHubProjectImporter:
    def __init__(self, repo_owner, repo_name, github_token):
        self.repo_owner = repo_owner
        self.repo_name = repo_name
        self.github_token = github_token
        self.base_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"
        self.headers = {
            "Authorization": f"token {github_token}",
            "Accept": "application/vnd.github.v3+json"
        }
    
    def create_milestone(self, title, description, due_date=None):
        """Create a milestone for phase tracking"""
        url = f"{self.base_url}/milestones"
        data = {
            "title": title,
            "description": description,
            "state": "open"
        }
        if due_date:
            data["due_on"] = due_date
        
        response = requests.post(url, headers=self.headers, json=data)
        if response.status_code == 201:
            print(f"✅ Created milestone: {title}")
            return response.json()
        else:
            print(f"❌ Failed to create milestone {title}: {response.text}")
            return None
    
    def create_label(self, name, color, description=""):
        """Create a label for categorizing issues"""
        url = f"{self.base_url}/labels"
        data = {
            "name": name,
            "color": color,
            "description": description
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        if response.status_code == 201:
            print(f"✅ Created label: {name}")
            return response.json()
        else:
            print(f"⚠️  Label {name} might already exist or failed to create")
            return None
    
    def create_issue(self, title, body, labels=None, milestone=None, assignee=None):
        """Create a GitHub issue"""
        url = f"{self.base_url}/issues"
        data = {
            "title": title,
            "body": body,
            "labels": labels or [],
        }
        
        if milestone:
            data["milestone"] = milestone
        if assignee:
            data["assignee"] = assignee
        
        response = requests.post(url, headers=self.headers, json=data)
        if response.status_code == 201:
            issue = response.json()
            print(f"✅ Created issue #{issue['number']}: {title}")
            return issue
        else:
            print(f"❌ Failed to create issue {title}: {response.text}")
            return None
    
    def setup_labels(self):
        """Create all necessary labels for the project"""
        labels = [
            {"name": "phase-1", "color": "0052cc", "description": "Phase 1: Project Setup & Infrastructure"},
            {"name": "phase-2", "color": "5319e7", "description": "Phase 2: Core Pages & Navigation"},
            {"name": "phase-3", "color": "b60205", "description": "Phase 3: E-commerce Core"},
            {"name": "phase-4", "color": "fbca04", "description": "Phase 4: Advanced E-commerce Features"},
            {"name": "phase-5", "color": "0e8a16", "description": "Phase 5: Content Management & SEO"},
            {"name": "phase-6", "color": "006b75", "description": "Phase 6: Testing & Deployment"},
            {"name": "infrastructure", "color": "c2e0c6", "description": "Infrastructure and setup tasks"},
            {"name": "frontend", "color": "bfdadc", "description": "Frontend development tasks"},
            {"name": "backend", "color": "f9d0c4", "description": "Backend development tasks"},
            {"name": "design-system", "color": "fef2c0", "description": "Design system and UI components"},
            {"name": "ecommerce", "color": "d4c5f9", "description": "E-commerce functionality"},
            {"name": "client-deliverable", "color": "ff6b6b", "description": "Tasks that produce client deliverables"},
            {"name": "high-priority", "color": "d93f0b", "description": "High priority tasks"},
            {"name": "medium-priority", "color": "fbca04", "description": "Medium priority tasks"},
            {"name": "low-priority", "color": "0e8a16", "description": "Low priority tasks"}
        ]
        
        for label in labels:
            self.create_label(label["name"], label["color"], label["description"])
    
    def import_project_structure(self, json_file_path):
        """Import the entire project structure from JSON"""
        with open(json_file_path, 'r') as f:
            project_data = json.load(f)
        
        print(f"🚀 Starting import for: {project_data['project']}")
        
        # Setup labels first
        print("\n📋 Setting up labels...")
        self.setup_labels()
        
        # Create milestones
        print("\n🎯 Creating milestones...")
        milestones = {}
        for phase in project_data['phases']:
            if 'milestone' in phase:
                milestone = self.create_milestone(
                    phase['milestone'],
                    f"Completion of {phase['title']} - {phase['description']}"
                )
                if milestone:
                    milestones[phase['milestone']] = milestone['number']
        
        # Create issues for each phase
        print("\n📝 Creating issues...")
        for phase in project_data['phases']:
            # Create main phase issue
            phase_body = self.create_phase_issue_body(phase)
            milestone_number = milestones.get(phase.get('milestone'))
            
            phase_issue = self.create_issue(
                title=phase['title'],
                body=phase_body,
                labels=phase.get('labels', []) + ['client-deliverable'],
                milestone=milestone_number
            )
            
            # Create subtask issues
            for subtask in phase.get('subtasks', []):
                subtask_body = self.create_subtask_issue_body(subtask, phase_issue)
                
                subtask_issue = self.create_issue(
                    title=subtask['title'],
                    body=subtask_body,
                    labels=subtask.get('labels', []),
                    milestone=milestone_number
                )
                
                # Create individual task issues if they exist
                for task in subtask.get('tasks', []):
                    task_body = self.create_task_issue_body(task, subtask_issue)
                    priority_label = f"{task.get('priority', 'medium').lower()}-priority"
                    
                    self.create_issue(
                        title=task['title'],
                        body=task_body,
                        labels=subtask.get('labels', []) + [priority_label],
                        milestone=milestone_number
                    )
        
        print("\n🎉 Import completed successfully!")
    
    def create_phase_issue_body(self, phase):
        """Create the body content for a phase issue"""
        body = f"""# {phase['title']}

## Overview
{phase['description']}

**Duration:** {phase['duration']}

## Subtasks
"""
        for subtask in phase.get('subtasks', []):
            body += f"- [ ] {subtask['title']} ({subtask['duration']})\n"
        
        body += f"""
## Success Criteria
- [ ] All subtasks completed
- [ ] Code reviewed and approved
- [ ] Client demo completed
- [ ] Documentation updated

## Client Deliverables
This phase will deliver:
"""
        for subtask in phase.get('subtasks', []):
            if subtask.get('estimated_hours'):
                body += f"- {subtask['title']} (Est. {subtask['estimated_hours']} hours)\n"
        
        return body
    
    def create_subtask_issue_body(self, subtask, parent_issue):
        """Create the body content for a subtask issue"""
        body = f"""# {subtask['title']}

## Description
{subtask['description']}

**Duration:** {subtask['duration']}
**Estimated Hours:** {subtask.get('estimated_hours', 'TBD')}

## Parent Phase
Related to: #{parent_issue['number'] if parent_issue else 'TBD'}

## Tasks
"""
        for task in subtask.get('tasks', []):
            body += f"- [ ] {task['title']}\n"
        
        body += """
## Definition of Done
- [ ] All tasks completed
- [ ] Code implemented and tested
- [ ] Documentation updated
- [ ] Code reviewed
- [ ] Ready for client demo

## Notes
[Add any additional notes or considerations here]
"""
        return body
    
    def create_task_issue_body(self, task, parent_issue):
        """Create the body content for an individual task issue"""
        body = f"""# {task['title']}

## Description
{task['description']}

**Estimated Hours:** {task.get('estimated_hours', 'TBD')}
**Priority:** {task.get('priority', 'Medium')}

## Parent Subtask
Related to: #{parent_issue['number'] if parent_issue else 'TBD'}

## Checklist
"""
        for item in task.get('checklist', []):
            body += f"- [ ] {item}\n"
        
        body += f"""
## Technical Requirements
[Add specific technical requirements here]

## Definition of Done
- [ ] All checklist items completed
- [ ] Code implemented
- [ ] Tests written (if applicable)
- [ ] Documentation updated
- [ ] Code reviewed
- [ ] Deployed to staging (if applicable)

## Resources
- [Link to documentation]
- [Link to design files]
- [Link to related issues]
"""
        return body

def main():
    """Main function to run the import"""
    # Configuration
    REPO_OWNER = input("Enter GitHub username/organization: ")
    REPO_NAME = input("Enter repository name: ")
    GITHUB_TOKEN = input("Enter GitHub personal access token: ")
    
    if not all([REPO_OWNER, REPO_NAME, GITHUB_TOKEN]):
        print("❌ Missing required configuration. Please provide all values.")
        return
    
    # Create importer instance
    importer = GitHubProjectImporter(REPO_OWNER, REPO_NAME, GITHUB_TOKEN)
    
    # Import project structure
    json_file = "github-issues-import.json"
    if os.path.exists(json_file):
        importer.import_project_structure(json_file)
    else:
        print(f"❌ JSON file {json_file} not found. Please ensure it exists in the current directory.")

if __name__ == "__main__":
    main()
