tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.eventsBar={},function(e,t){"use strict";const n=e(document);t.selectors={eventsBar:'[data-js="tribe-events-events-bar"]',searchButton:'[data-js="tribe-events-search-button"]',searchButtonActiveClass:".tribe-events-c-events-bar__search-button--active",searchContainer:'[data-js="tribe-events-search-container"]'},t.keyCode={END:35,HOME:36,LEFT:37,RIGHT:39},t.deinitAccordion=function(e,t){tribe.events.views.accordion.deinitAccordion(0,e),tribe.events.views.accordion.deinitAccordionA11yAttrs(e,t),t.css("display","")},t.initAccordion=function(e,t,n){tribe.events.views.accordion.initAccordion(e)(0,t),tribe.events.views.accordion.initAccordionA11yAttrs(t,n)},t.handleSearchButtonClick=function(e){e.data.target.toggleClass(t.selectors.searchButtonActiveClass.className())},t.deinitSearchAccordion=function(e){const n=e.find(t.selectors.searchButton);n.removeClass(t.selectors.searchButtonActiveClass.className());const i=e.find(t.selectors.searchContainer);t.deinitAccordion(n,i),n.off("click",t.handleSearchButtonClick)},t.initSearchAccordion=function(e){const n=e.find(t.selectors.searchButton),i=e.find(t.selectors.searchContainer);t.initAccordion(e,n,i),n.on("click",{target:n},t.handleSearchButtonClick)},t.initState=function(e){e.find(t.selectors.eventsBar).data("tribeEventsState",{mobileInitialized:!1,desktopInitialized:!1})},t.deinitEventsBar=function(e){t.deinitSearchAccordion(e)},t.initEventsBar=function(e){const n=e.find(t.selectors.eventsBar);if(n.length){const i=n.data("tribeEventsState"),s=e.data("tribeEventsState").isMobile;s&&!i.mobileInitialized?(t.initSearchAccordion(e),i.desktopInitialized=!1,i.mobileInitialized=!0,n.data("tribeEventsState",i)):s||i.desktopInitialized||(t.deinitSearchAccordion(e),i.mobileInitialized=!1,i.desktopInitialized=!0,n.data("tribeEventsState",i))}},t.handleResize=function(e){t.initEventsBar(e.data.container)},t.handleClick=function(n){const i=e(n.target),s=Boolean(i.closest(t.selectors.searchButton).length),o=Boolean(i.closest(t.selectors.searchContainer).length);if(!s&&!o){const e=n.data.container.find(t.selectors.eventsBar),i=e.find(t.selectors.searchButton);if(i.hasClass(t.selectors.searchButtonActiveClass.className())){const n=e.find(t.selectors.searchContainer);i.removeClass(t.selectors.searchButtonActiveClass.className()),tribe.events.views.accordion.closeAccordion(i,n)}}},t.unbindEvents=function(e){e.off("resize.tribeEvents",t.handleResize),n.off("click",t.handleClick)},t.bindEvents=function(e){e.on("resize.tribeEvents",{container:e},t.handleResize),n.on("click",{container:e},t.handleClick)},t.deinit=function(e,n,i){const s=e.data.container;t.deinitEventsBar(s),t.unbindEvents(s),s.off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,n,i,s){i.find(t.selectors.eventsBar).length&&(t.initState(i),t.initEventsBar(i),t.bindEvents(i),i.on("beforeAjaxSuccess.tribeEvents",{container:i},t.deinit))},t.ready=function(){n.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready),t.viewSelector={container:'[data-js="tribe-events-view-selector"]',button:'[data-js="tribe-events-view-selector-button"]',listContainer:'[data-js="tribe-events-view-selector-list-container"]',activeClass:"tribe-events-c-view-selector__button--active",ariaExpanded:"aria-expanded"},t.closeViewSelector=function(e){const n=e.find(t.viewSelector.button),i=e.find(t.viewSelector.listContainer);n.removeClass(t.viewSelector.activeClass),n.attr(t.viewSelector.ariaExpanded,"false"),i.hide().attr("aria-hidden","true")},t.openViewSelector=function(e){const n=e.find(t.viewSelector.button),i=e.find(t.viewSelector.listContainer);n.addClass(t.viewSelector.activeClass),n.attr(t.viewSelector.ariaExpanded,"true"),i.show().attr("aria-hidden","false")},t.initViewSelectorA11y=function(){const n=e(t.viewSelector.container);if(!n.length)return;const i=n.find(t.viewSelector.button),s=n.find(t.viewSelector.listContainer);n.on("keydown",(function(e){"Escape"!==e.key&&27!==e.keyCode||(t.closeViewSelector(n),i.focus())})),s.on("focusout",(function(e){setTimeout((function(){const e=document.activeElement;s[0].contains(e)||e===i[0]||t.closeViewSelector(n)}),10)})),s.on("keydown",(function(e){if("Tab"===e.key||9===e.keyCode){const i=s.find("a:visible"),o=i[0],c=i[i.length-1];e.shiftKey||document.activeElement!==c||t.closeViewSelector(n),e.shiftKey&&document.activeElement===o&&t.closeViewSelector(n)}}))},e(t.initViewSelectorA11y)}(jQuery,tribe.events.views.eventsBar),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.eventsBar={}