(()=>{var t={};t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),(()=>{var e;t.g.importScripts&&(e=t.g.location+"");var r=t.g.document;if(!e&&r&&(r.currentScript&&(e=r.currentScript.src),!e)){var n=r.getElementsByTagName("script");if(n.length)for(var o=n.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=n[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=e})(),t.p=window.wcpayAssets.url,(()=>{"use strict";window.wp.domReady;const t=t=>"undefined"!=typeof wcpayConfig?wcpayConfig[t]:e(t),e=t=>{let e=null;if("undefined"!=typeof wcpay_upe_config)e=wcpay_upe_config;else{if("object"!=typeof wc||void 0===wc.wcSettings)return null;e=wc.wcSettings.getSetting("woocommerce_payments_data")||{}}return e[t]||null},r=t=>"object"==typeof wcpayExpressCheckoutParams&&wcpayExpressCheckoutParams.hasOwnProperty(t)?wcpayExpressCheckoutParams[t]:null,n=(e,n={})=>{var o,c,a;const i=null!==(o=t("platformTrackerNonce"))&&void 0!==o?o:null===(c=r("nonce"))||void 0===c?void 0:c.platform_tracker,p=null!==(a=t("ajaxUrl"))&&void 0!==a?a:r("ajax_url"),s=new FormData;s.append("tracksNonce",i),s.append("action","platform_tracks"),s.append("tracksEventName",e),s.append("tracksEventProp",JSON.stringify(n)),fetch(p,{method:"post",body:s}).then((t=>t.json()))};if(window.wcPayFrontendTracks&&window.wcPayFrontendTracks.length){for(const t of window.wcPayFrontendTracks)n(t.event,t.properties);window.wcPayFrontendTracks=[]}})()})()