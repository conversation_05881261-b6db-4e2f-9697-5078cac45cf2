tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.viewport={},function(e,t){"use strict";const n=e(window),i=e(document);t.options={MOBILE_BREAKPOINT:tribe.events.views.breakpoints.breakpoints.medium||768},t.setViewport=function(e){let n=e.data("tribeEventsState");n||(n={}),n.isMobile=e.outerWidth()<t.options.MOBILE_BREAKPOINT,e.data("tribeEventsState",n)},t.handleResize=function(e){const n=e.data.container;t.setViewport(n),n.trigger("resize.tribeEvents")},t.unbindEvents=function(e){n.off("resize",t.handleResize)},t.bindEvents=function(e){n.on("resize",{container:e},t.handleResize)},t.deinit=function(e,n,i){const s=e.data.container;t.unbindEvents(s),s.off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,n,i,s){t.bindEvents(i),t.setViewport(i),i.on("beforeAjaxSuccess.tribeEvents",{container:i},t.deinit)},t.ready=function(){i.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.viewport),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.viewport={}