var $=jQuery.noConflict(),kapeeOwlParam=kapeeOwlParam||{},kapee_options=kapee_options||{};(function($){"use strict";var kapee=kapee||{};kapee.kapeePreLoader=function(){var loader=$('.kapee-site-preloader');if(loader.length){$(window).on('beforeunload',function(){loader.fadeIn(500,function(){loader.fadeIn(100)})});$(window).on('load',function(){loader.fadeOut(800)})}}
kapee.init=function(){kapee.$doc=$(document);kapee.$html=$('html');kapee.$body=$(document.body);kapee.$window=$(window);kapee.$windowWidth=$(window).width();kapee.$windowHeight=$(window).height();kapee.ajaxXHR=null;kapee.isPostLoading=!1;kapee.$tooltip=$('.product-buttons a,.whishlist-button a');kapee.$swatches=kapee.$doc.find('div.kapee-swatches-wrap');kapee.$swatchForm=kapee.$doc.find('form.kapee-swatches-wrap.variations_form');this.isCheckRTL();this.mobileDevice();this.BrowserDetection();this.addSpinner();this.skeletonEffect();this.backToTop();this.imagelazyload();this.reinitLazyload();this.initNanoScroller();this.initMagnaficPopup();this.newsLetterPopup();this.kapeeMegamenu();this.mobileMenu();this.promoBar();this.stickyHeader();this.StickyHeaderScrollUP();this.stickySidebar();this.canvasSidebar();this.openMiniSearch();this.widgetMenuToggle();this.widgetToggle();this.footerWidgetCollapse();this.widgetMaxLimitItem();this.kapeeOwlCarousel();this.MasonryGrid();this.postGalleryCarousel();this.loadmorePosts();this.socialShare();this.portfolioFilters();this.portfolioLoadMore();this.productLiveSearch();this.swapLoginSignupFrom();this.userLoginSignupPopup();this.miniCartWidget();this.addToWishlist();this.wishlistCount();this.addToCompare();this.compareCount();this.removeToCompare();this.ProductLoopQuantityField();this.addToCart();this.addToCartAjax();this.stickyAddToCart();this.productQuickView();this.productShowFilter();this.productShowHideFilters();this.productFilterAjax();this.productHover();this.productSwatch();this.variationsImageChange();this.initAjaxLoad();this.loadmoreProducts();this.tooltip();this.productGallerySummarySticky();this.productGallerySlider();this.productImageZoom();this.productPhotoSwipe();this.productSaleCountdown();this.productReviewLink();this.productPriceSummary();this.getProductSizeChart();this.getProductTermsConditions();this.productQuantityPlusMinus();this.productQuickShop();this.productBoughtTogetherInit();this.wooProductTabsAccordian();this.wooProductTabsToggle();this.wooCheckoutStep();this.resetVariations();this.variationChangeevent();this.wcfm_vendor();this.MiniCartUpdateQuantity();this.kapeeEqualTabsHeight();this.kapeeTabEffect();this.kapeeAjaxTab();this.kapeeResponsiveTab();this.kapeeProgressbar();this.kapeeCounterUp();this.imageGaleryMasonry();this.BackgroundParallax()};kapee.isCheckRTL=function(){$('html[dir="rtl"] body').addClass('rtl');var kapee_rtl=!1;if($('body,html').hasClass('rtl')){kapee_rtl=!0}
return kapee_rtl};kapee.mobileDevice=function(){var window_size=jQuery('body').innerWidth();if(window_size<991){jQuery('body').addClass('kapee-mobile-device')}else{jQuery('body').removeClass('kapee-mobile-device')}
kapee.$window.on('resize',function(){var window_size=jQuery('body').innerWidth();if(window_size<991){jQuery('body').addClass('kapee-mobile-device')}else{jQuery('body').removeClass('kapee-mobile-device')}})};kapee.BrowserDetection=function(){if(navigator.userAgent.search("MSIE")>=0){jQuery('body').addClass('browser-msie')}else if(navigator.userAgent.search("Chrome")>=0){jQuery('body').addClass('browser-chrome')}else if(navigator.userAgent.search("Firefox")>=0){jQuery('body').addClass('browser-firefox')}else if(navigator.userAgent.search("Safari")>=0&&navigator.userAgent.search("Chrome")<0){jQuery('body').addClass('browser-safari')}else if(navigator.userAgent.search("Opera")>=0){jQuery('body').addClass('browser-opera')}};kapee.addSpinner=function(){$(document).ajaxStart(function(){$('.wcml_currency_switcher').append('<span class="kapee-spinner"></span>')});$(document).ajaxComplete(function(){kapee.reinitLazyload();$('.wcml_currency_switcher > span').remove()})};kapee.skeletonEffect=function(){if(!kapee_options.skeleton_effect){return}
if(kapee.$body.hasClass('kapee-skeleton')){kapee.$body.removeClass('kapee-skeleton')}};kapee.backToTop=function(){var el=$('.kapee-back-to-top');kapee.$window.on('scroll',function(){if(el.length>0){if(kapee.$window.scrollTop()>150){el.fadeIn(400)}else{el.fadeOut(400)}}});el.on('click',function(e){$('html,body').animate({scrollTop:0},600);return!1})};kapee.imagelazyload=function(){if(kapee.$body.find('.lazy').length>0&&kapee_options.lazy_load){var lazy_args=[];lazy_args.afterLoad=function(element){element.removeClass('lazy');element.removeClass('loading');element.addClass('lazy-loaded')};lazy_args.effect="fadeIn";lazy_args.enableThrottle=!0;lazy_args.throttle=250;lazy_args.effectTime=1000;lazy_args.threshold=0;kapee.$body.find('.lazy').lazy(lazy_args)}};kapee.reinitLazyload=function(){if(!kapee_options.lazy_load)return;$(document).on('shown.bs.tab','a[data-toggle="tab"]',function(e){kapee.imagelazyload()});$(document).on('removed_from_cart',function(e){kapee.imagelazyload()});$(window).bind('mousewheel DOMMouseScroll',function(event){if(event.originalEvent.wheelDelta>0||event.originalEvent.detail<0){}else{kapee.imagelazyload()}})}
kapee.initNanoScroller=function(){if($(window).width()<1024)return;$(".kapee-scroll").nanoScroller({paneClass:'kapee-scroll-pane',sliderClass:'kapee-scroll-slider',contentClass:'kapee-scroll-content',preventPageScrolling:!1});$('body').on('wc_fragments_refreshed wc_fragments_loaded added_to_cart',function(){$(".widget_shopping_cart .kapee-scroll").nanoScroller({paneClass:'kapee-scroll-pane',sliderClass:'kapee-scroll-slider',contentClass:'kapee-scroll-content',preventPageScrolling:!1})});$('.sidebar-inner .kapee-scroll').nanoScroller({destroy:!0})}
kapee.initMagnaficPopup=function(){var wordpress_galery=$(document).find('.gallery');wordpress_galery.each(function(index){var current_gallery=$(this);$(current_gallery).magnificPopup({delegate:'img',type:'image',removalDelay:500,callbacks:{beforeOpen:function(){this.st.image.markup=this.st.image.markup.replace('mfp-figure','mfp-figure mfp-with-anim');this.st.mainClass='mfp-with-zoom mfp-img-mobile'},elementParse:function(item){item.src=item.el.attr('src')}},image:{verticalFit:!0},gallery:{enabled:!0,navigateByImgClick:!0},})});$('.product-360-degree-btn a').magnificPopup({type:'inline',midClick:!0,mainClass:'mfp-fade'});init_magnificpopup('.portfolios-list','a.project-zoom');init_magnificpopup('.kapee-gallery-carousel','.owl-item:not(.cloned) a');init_magnificpopup('.kapee-portfolio-image:not(.kapee-gallery-carousel)','.kapee-post-gallery__image a');init_magnificpopup('.related.portfolios .kapee-carousel','.owl-item:not(.cloned) a.project-zoom');init_magnificpopup('.kapee-portfolios-carousel','.owl-item:not(.cloned) a.project-zoom');init_magnificpopup('.kapee-image-gallery.image-gallery-normal-grid','a');init_magnificpopup('.kapee-image-gallery.image-gallery-masonry-grid','a');init_magnificpopup('.kapee-image-gallery.image-gallery-carousel','.owl-item:not(.cloned) a');function init_magnificpopup(container,delegate){var container_wrap=$(document).find(container);if(typeof('container_wrap')!=='undefined'&&container_wrap!=''){container_wrap.each(function(index){var portfolio_item=$(this);$(portfolio_item).magnificPopup({delegate:delegate,type:'image',removalDelay:500,callbacks:{beforeOpen:function(){this.st.image.markup=this.st.image.markup.replace('mfp-figure','mfp-figure mfp-with-anim');this.st.mainClass='mfp-with-zoom mfp-img-mobile'}},image:{verticalFit:!0},gallery:{enabled:!0,navigateByImgClick:!1},})})}}
$('.link-popup').magnificPopup({type:'image',removalDelay:500,callbacks:{beforeOpen:function(){this.st.image.markup=this.st.image.markup.replace('mfp-figure','mfp-figure mfp-with-anim');this.st.mainClass='mfp-with-zoom mfp-img-mobile'}},image:{verticalFit:!0},});var $ombed_vids=$(".kapee-video-popup");if($ombed_vids.length>0){$ombed_vids.each(function(){var $mfp_popup_link_non_html5=$(this);$($mfp_popup_link_non_html5).magnificPopup({disableOn:320,type:'iframe',mainClass:'mfp-fade product-video-popup',removalDelay:160,preloader:!1,fixedContentPos:!1,iframe:{patterns:{youtube:{index:'youtube.com/',id:function(url){var m=url.match(/[\\?\\&]v=([^\\?\\&]+)/);if(!m||!m[1])return null;return m[1]},src:'//www.youtube.com/embed/%id%?autoplay=1&rel=0'},youtu:{index:'youtu.be',id:'/',src:'//www.youtube.com/embed/%id%?autoplay=1&rel=0'},vimeo:{index:'vimeo.com/',id:function(url){var m=url.match(/(https?:\/\/)?(www.)?(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/);if(!m||!m[5])return null;return m[5]},src:'//player.vimeo.com/video/%id%?autoplay=1'},}}})})}};kapee.newsLetterPopup=function(){var popup_enable=kapee_options.newsletter_args.popup_enable,popup_display_on=kapee_options.newsletter_args.popup_display_on,popup_delay=kapee_options.newsletter_args.popup_delay,x_scroll=kapee_options.newsletter_args.popup_x_scroll,show_for_mobile=kapee_options.newsletter_args.show_for_mobile,popup_exit=!1,startinterval=!1,popup_closed=Cookies.set('kapee_newsletter_closed'),$news_letter_wrap=$('.kapee-newsletter-popup'),from_button=!1;if(!$news_letter_wrap.length){return!1}
if(jQuery(document).find('#yith-woocompare').length>0){return!1}
if(!popup_enable||kapee_options.maintenance_mode||(!show_for_mobile&&$(window).width()<768)){return!1}
var newsletter_btn=$('.header-newsletter');newsletter_btn.on('click',function(){from_button=!0;show_popup()});if(popup_closed=='do-not-show'){return!1}
if(popup_display_on=='exit'){jQuery(document).on('mouseleave',function(e){show_popup()})}else if(popup_display_on=='scroll'){jQuery(window).scroll(function(){var h=jQuery(document).height()-jQuery(window).height();var sp=jQuery(window).scrollTop();var p=parseInt(sp/h*100);if(p>=x_scroll){show_popup()}})}else{if(popup_delay){setTimeout(function(){show_popup()},popup_delay*1000)}else{show_popup()}}
$news_letter_wrap.find('.mc4wp-form').submit(function(){Cookies.set('kapee_newsletter_closed','do-not-show',{expires:1,path:'/'})});function show_popup(){if(popup_exit&&!from_button)
return;popup_exit=!0;$.magnificPopup.open({type:'inline',removalDelay:500,items:{src:'.kapee-newsletter-popup',},callbacks:{open:function(){var popupWrap=$('.kapee-newsletter-popup');popupWrap.addClass('animated fadeInLeft');if(from_button)
$('#newsletter-donotshow').parent('div').hide()},beforeClose:function(){var popupWrap=$('.kapee-newsletter-popup');popupWrap.removeClass('fadeInLeft').addClass('fadeOutRight')},close:function(){this.content.removeClass('animated fadeOutRight');if($('#newsletter-donotshow:checked')&&$('#newsletter-donotshow:checked').val()=='do-not-show'){Cookies.set('kapee_newsletter_closed','do-not-show',{expires:1,path:'/'})}
if(from_button){$('#newsletter-donotshow').parent('div').show();from_button=!1}}},})}};kapee.wowAnimation=function(){if($('.wow').length>0){var wow=new WOW({boxClass:'wow',animateClass:'animated',offset:50,mobile:!1});wow.init()}}
kapee.kapeeMegamenu=function(){var main_menu_wrap=$('.main-navigation').find('ul.menu');main_menu_wrap.on('mouseover',' > li.kapee-megamenu-dropdown',function(e){setOffset($(this))});jQuery(window).resize(function(){main_menu_wrap.on('mouseover',' > li.kapee-megamenu-dropdown',function(e){setOffset($(this))})});var setOffset=function(li){var megaMenuWrapper=li.find(' > .kapee-megamenu-wrapper'),megaMenuHolder=li.find(' .kapee-megamenu-holder');megaMenuWrapper.attr('style','');var container=$('.site-header .container'),containerWidth=container.outerWidth(),containerOffsetLeft=container.offset().left+15,containerPaddingLeft=parseInt(container.css('padding-left')),containerPaddingRight=parseInt(container.css('padding-right')),viewportWidth=containerWidth-containerPaddingLeft-containerPaddingRight;if(li.hasClass('kapee-megamenu-item-full-width')){megaMenuHolder.css({width:viewportWidth})}
var megaMenuWrapperWidth=megaMenuWrapper.outerWidth(),megaMenuWrapperOffset=megaMenuWrapper.offset();if(!megaMenuWrapperWidth||!megaMenuWrapperOffset)return;var mega_menu_wrapOffsetRight=viewportWidth-megaMenuWrapperOffset.left-megaMenuWrapperWidth;if($('body').hasClass('rtl')&&mega_menu_wrapOffsetRight+megaMenuWrapperWidth+containerOffsetLeft>=viewportWidth&&(li.hasClass('kapee-megamenu-dropdown'))){var toLeft=mega_menu_wrapOffsetRight+megaMenuWrapperWidth-viewportWidth+containerOffsetLeft;megaMenuWrapper.css({right:-toLeft})}else if(megaMenuWrapperOffset.left+megaMenuWrapperWidth-containerOffsetLeft>=viewportWidth&&(li.hasClass('kapee-megamenu-dropdown'))){var toRight=megaMenuWrapperOffset.left+megaMenuWrapperWidth-viewportWidth-containerOffsetLeft;megaMenuWrapper.css({left:-toRight})}}}
kapee.mobileMenu=function(){$(document).on('click','.mobile-nav-tabs li',function(e){if(!$(this).hasClass("active")){var cn=$(this).data("menu");$(this).parent().find(".active").removeClass("active");$(this).addClass("active");$(".mobile-nav-content").removeClass("active").fadeOut(300);$(".mobile-"+cn+"-menu").addClass("active").fadeIn(300)}});var $mobileMenu=$('.kapee-mobile-menu'),$closeSidebar=$('.kapee-mask-overaly');$('.mobile-navbar .navbar-toggle,.mobile-element .navbar-toggle').on('click',function(e){e.preventDefault();if(!$mobileMenu.hasClass('opened')){$mobileMenu.addClass('opened');$closeSidebar.addClass('opened')}});kapee.$body.on('click','.kapee-mask-overaly, .kapee-mobile-menu .close-sidebar',function(e){if($mobileMenu.hasClass('opened')){$mobileMenu.removeClass('opened');$closeSidebar.removeClass('opened')}});$('.mobile-main-menu li.menu-item-has-children').append('<span class="menu-toggle"></span>');$mobileMenu.on('click','.menu-item-has-children > .menu-toggle',function(e){e.preventDefault();$(this).closest('li').siblings().find('ul').slideUp();$(this).closest('li').siblings().removeClass('active');$(this).closest('li').siblings().find('li').removeClass('active');$(this).closest('li').children('ul').slideToggle();$(this).closest('li').toggleClass('active')});kapee.$body.on('click','.kapee-mask-overaly',function(e){if($mobileMenu.hasClass('opened')){$mobileMenu.removeClass('opened');$closeSidebar.removeClass('opened')}});kapee.$window.on('resize',function(){if(kapee.$window.width()>991){if($mobileMenu.hasClass('opened')){$mobileMenu.removeClass('opened');$closeSidebar.removeClass('opened')}}})};kapee.promoBar=function(){var $promo_bar=$('.kapee-promo-bar'),$siteWrapper=$('.site-wrapper'),$siteHeader=$('.site-header'),$stickyElements=$('.header-sticky'),position_type=$promo_bar.attr('data-position_type'),position=$promo_bar.attr('data-position'),promo_barHeight=$promo_bar.outerHeight(),adminBarHeight=$('#wpadminbar').outerHeight();if(position=='top'&&position_type=='fixed'){$siteWrapper.css({'margin-top':promo_barHeight+'px'})}
var promo_bar_key='kapee_promo_bar_close';$('.promo-bar-close').on('click',function(event){event.preventDefault();if($(this).hasClass('promo-bar-dismiss')){Cookies.set(promo_bar_key,!0,{path:'/'})}
if(position=='top'&&position_type=='fixed'){$siteWrapper.css({'margin-top':'0px'})}
if(position=='top'&&position_type=='fixed'){if(adminBarHeight){if($siteHeader.hasClass('header-sticked')){$stickyElements.css({'top':adminBarHeight+'px'})}}else{if($siteHeader.hasClass('header-sticked')){$stickyElements.css({'top':'0px'})}}}
$promo_bar.slideUp('slow',function(){$(this).remove()})})};kapee.stickyHeader=function(){var $header=$('.site-header'),$stickyElements=$('.header-sticky'),$firstSticky=$stickyElements.first(),headerHeight=$header.outerHeight(),$window=$(window),isSticked=!1,adminBarHeight=$('#wpadminbar').outerHeight(),stickShowAfter=headerHeight+5;$(window).on('scroll',function(){var windowSize=$(window).width();kapee.promoBar();if(windowSize>992&&(!kapee_options.sticky_header)){return!1}
if(windowSize<=992&&windowSize>480&&(!kapee_options.sticky_header_tablet)){return!1}
if(windowSize<=480&&(!kapee_options.sticky_header_mobile)){return!1}
var stickyOffset=stickShowAfter;var currentScroll=$(this).scrollTop();if(currentScroll>stickyOffset){stickHeader();if($('.header-sticky .categories-menu-wrapper').hasClass('opened-categories')){$('.header-sticky .categories-menu-wrapper').removeClass('opened-categories')}}else{unstickHeader()}});function stickHeader(){if(isSticked){return}
isSticked=!0;$header.addClass('header-sticked');var $promo_bar=$('.kapee-promo-bar'),position_type=$promo_bar.attr('data-position_type'),position=$promo_bar.attr('data-position'),promo_barHeight=$promo_bar.outerHeight(),adminBarHeight=$('#wpadminbar').outerHeight();if(position=='top'&&position_type=='fixed'){if(adminBarHeight){adminBarHeight=parseInt(adminBarHeight)+parseInt(promo_barHeight)}else{adminBarHeight=parseInt(promo_barHeight)}}
$stickyElements.css({'top':adminBarHeight+'px'})}
function unstickHeader(){if(!isSticked){return}
isSticked=!1
$header.removeClass('header-sticked');$stickyElements.css({'top':0})}};kapee.StickyHeaderScrollUP=function(){if(!kapee_options.sticky_header||!kapee_options.sticky_header_scroll_up){return}
var origPosition=$(window).scrollTop();$(window).scroll(function(e){var currentScroll=$(window).scrollTop();if(currentScroll>origPosition){$('.site-header').removeClass('scroll-up').addClass('scroll-down')}else{$('.site-header').removeClass('scroll-down').addClass('scroll-up')}
origPosition=currentScroll})};kapee.stickySidebar=function(){if(!kapee_options.sticky_sidebar){return}
if(typeof($.fn.stick_in_parent)!='undefined'){$(document).ready(function(){if($(window).width()<=768){return}
var sticky_sidebar=$('#secondary .sidebar-inner');var offset=15;if($('#header .header-sticky')[0]){offset=$('#header .header-sticky').height()+30}
sticky_sidebar.stick_in_parent({offset_top:offset});$(window).resize(function(){if($(window).width()<=768){sticky_sidebar.trigger('sticky_kit:detach')}else{sticky_sidebar.stick_in_parent({offset_top:offset})}})})}};kapee.canvasSidebar=function(){var sidebar_canvas=$('.kapee-canvas-sidebar .kp-canvas-sidebar, .kapee-mobile-navbar .kp-canvas-sidebar');var secondary=$('#secondary');var closeSidebar=$('.kapee-mask-overaly');sidebar_canvas.on('click',function(e){e.preventDefault();if(!secondary.hasClass('opened')){secondary.addClass('opened');setTimeout(function(){kapee.imagelazyload()},1000);closeSidebar.addClass('opened')}});kapee.$body.on('click','.kapee-mask-overaly, .close-sidebar',function(e){e.preventDefault();secondary.removeClass('opened');closeSidebar.removeClass('opened')});kapee.$window.on('resize',function(){if(kapee.$window.width()>767){if(secondary.hasClass('opened')){secondary.removeClass('opened');closeSidebar.removeClass('opened')}}})};kapee.openMiniSearch=function(){var sidebar_canvas=$('.header-mini-search > a');var search_popup=$('.kapee-search-popup');var closeSidebar=$('.kapee-search-popup .close-sidebar');sidebar_canvas.on('click',function(e){e.preventDefault();if(!search_popup.hasClass('opened')){search_popup.addClass('opened')}});closeSidebar.on('click',function(e){e.preventDefault();if(search_popup.hasClass('opened')){search_popup.removeClass('opened')}})};kapee.widgetMenuToggle=function(){if(kapee_options.widget_menu_toggle){$('#secondary .widget .menu-item > a').each(function(){if($(this).siblings('ul.sub-menu').length>0){var $childIndicator=$('<span class="child-indicator"></span>');$(this).siblings('.sub-menu').hide();$('.current-menu-item > .sub-menu').show();$('.current-menu-parent > .sub-menu').show();if($(this).siblings('.sub-menu').is(':visible')){$childIndicator.addClass('open-item')}
$childIndicator.on('click',function(){$(this).parent().siblings('.sub-menu').toggle('fast',function(){if($(this).is(':visible')){$childIndicator.addClass('open-item')}else{$childIndicator.removeClass('open-item')}});return!1});$(this).append($childIndicator)}});$('#secondary .widget .cat-item > a').each(function(){if($(this).siblings('ul.children').length>0){var $childIndicator=$('<span class="child-indicator"></span>');$(this).siblings('.children').hide();$('.current-cat > .children').show();$('.current-cat-parent > .children').show();if($(this).siblings('.children').is(':visible')){$childIndicator.addClass('open-item')}
$childIndicator.on('click',function(){$(this).parent().siblings('.children').toggle('fast',function(){if($(this).is(':visible')){$childIndicator.addClass('open-item')}else{$childIndicator.removeClass('open-item')}});return!1});if(!$(this).find('.child-indicator').length>0){$(this).append($childIndicator)}}})}};kapee.widgetToggle=function(){if(kapee_options.widget_toggle){$(document).find('.widget-area .widget').addClass('widget-toggle').removeClass('closed');$(document).on('click','.widget-area .widget .widget-title, .dokan-widget-area .widget .widget-title',function(e){e.stopImmediatePropagation();if($(this).next().is(':visible')){$(this).parent().addClass('closed')}else{$(this).parent().removeClass('closed')}
$(this).next().stop().slideToggle(200)})}};kapee.footerWidgetCollapse=function(){if($(window).width()>576){return}
$(document).on('click','.kapee-mobile-device .footer-widget-collapse .widget .widget-title',function(e){var $title=$(this);var $widget=$title.parent();var $content=$widget.find('> *:not(.widget-title)');if($widget.hasClass('footer-widget-opened')){$widget.removeClass('footer-widget-opened');$content.stop().slideUp(200)}else{$widget.addClass('footer-widget-opened');$content.stop().slideDown(200)}})};kapee.widgetMaxLimitItem=function(){if(kapee_options.widget_hide_max_limit_item){var js_translate_text=kapee_options.js_translate_text;$('.widget .widget-title + ul').hideMaxListItems({'max':kapee_options.number_of_show_widget_items,'speed':500,'moreText':js_translate_text.show_more,'lessText':js_translate_text.show_less})}};kapee.kapeeOwlCarousel=function(){if(kapeeOwlParam.length===0||typeof kapeeOwlParam.owlCarouselArg==='undefined'){return}
function add_owl_overlayclass(){$('.owl-stage-outer .product-wrapper').mouseenter(function(){var slider_elemnt=$(this).closest('.products.kapee-carousel');slider_elemnt.find('.owl-stage-outer').addClass('overlay')}).mouseleave(function(){var slider_elemnt=$(this).closest('.products.kapee-carousel');slider_elemnt.find('.owl-stage-outer').removeClass('overlay')})}
setTimeout(function(){add_owl_overlayclass()},1000)
$.each(kapeeOwlParam.owlCarouselArg,function(id,owlCarouselArg){var loop=(owlCarouselArg.slider_loop)?!0:!1,autoplay=(owlCarouselArg.slider_autoplay)?!0:!1,autoplayHoverPause=(autoplay&&owlCarouselArg.slider_autoplayHoverPause)?!0:!1,autoplaytimeout=owlCarouselArg.slider_autoplaytimeout,smartspeed=parseInt(owlCarouselArg.slider_smartspeed),rewind=(owlCarouselArg.slider_rewind)?!0:!1,nav=(owlCarouselArg.slider_nav)?!0:!1,nav_mobile=(owlCarouselArg.slider_nav_mobile)?!0:!1,center=(owlCarouselArg.slider_center)?!0:!1,dots=(owlCarouselArg.slider_dots)?!0:!1,autoHeight=(owlCarouselArg.slider_autoHeight)?!0:!1,touchDrag=(owlCarouselArg.slider_touchDrag)?!0:!1,touchDrag_mobile=(owlCarouselArg.slider_touchDrag_mobile)?!0:!1,animateIn=owlCarouselArg.slider_animatein,animateOut=owlCarouselArg.slider_animateout,margin=owlCarouselArg.slider_margin,rs_extra_large=(owlCarouselArg.rs_extra_large>0)?owlCarouselArg.rs_extra_large:5,rs_large=(owlCarouselArg.rs_large>0)?owlCarouselArg.rs_large:4,rs_medium=(owlCarouselArg.rs_medium>0)?owlCarouselArg.rs_medium:3,rs_small=(owlCarouselArg.rs_small>0)?owlCarouselArg.rs_small:2,rs_extra_small=(owlCarouselArg.rs_extra_small>0)?owlCarouselArg.rs_extra_small:2,numItems=null,slider_element=null;if($('#'+id).hasClass('kapee-carousel')){numItems=$('#'+id).children().length;slider_element=$('#'+id)}else{numItems=$('#'+id).find('.kapee-carousel').children().length;slider_element=$('#'+id).find('.kapee-carousel')}
slider_element.owlCarousel({autoplay:autoplay,autoplayHoverPause:autoplayHoverPause,autoplayTimeout:autoplaytimeout,smartSpeed:smartspeed,rewind:rewind,nav:nav,center:center,navText:['',''],dots:dots,autoHeight:autoHeight,touchDrag:touchDrag,animateIn:animateIn,animateOut:animateOut,margin:margin,rtl:(kapee.isCheckRTL())?!0:!1,responsive:{0:{items:rs_extra_small,loop:(numItems>=rs_extra_small&&loop)?!0:!1,nav:nav_mobile,mouseDrag:!1,touchDrag:touchDrag_mobile},576:{items:rs_small,loop:(numItems>=rs_small&&loop)?!0:!1,nav:nav_mobile,mouseDrag:!1,touchDrag:touchDrag_mobile},768:{items:rs_medium,loop:(numItems>=rs_medium&&loop)?!0:!1,nav:nav_mobile,},992:{items:rs_large,loop:(numItems>=rs_large&&loop)?!0:!1,},1200:{items:rs_extra_large,loop:(numItems>=rs_extra_large&&loop)?!0:!1,}},onInitialized:function(){slider_element.addClass('owl-theme')}}).on('changed.owl.carousel',function(event){kapee.imagelazyload()})})};kapee.MasonryGrid=function(){if($('.articles-list.masonry-grid').length){kapee.$body.imagesLoaded(function(){kapee.$body.find('.articles-list.masonry-grid').isotope({itemSelector:'.post',layoutMode:'masonry'})})}};kapee.postGalleryCarousel=function(){$('.kapee-gallery-carousel').owlCarousel({loop:!0,autoplay:!1,autoplayTimeout:3000,rtl:(kapee.isCheckRTL())?!0:!1,smartSpeed:750,nav:!0,navText:['',''],dots:!0,items:1});$('.kapee-gallery-carousel').addClass('owl-theme owl-center')};kapee.loadmorePosts=function(){$('.kapee-blog-load-more .kapee-load-more').on('click',function(){var load_more_btn=$(this);var page=parseInt(load_more_btn.attr('data-page'));var attr=load_more_btn.attr('data-attribute');var post_wrap=load_more_btn.closest('.kapee-element').find('.articles-list');var wrap_id=load_more_btn.closest('.kapee-element').attr('id');var data={action:'kapee_loadmore_posts',nonce:kapee_options.nonce,attr:attr,page:page,};if(load_more_btn.hasClass('process')){return!1}
kapee.loadAjaxPost(load_more_btn,data,post_wrap,wrap_id)});var animationFrame=function(){$('.kapee-blog-load-more a.infinity-scroll').each(function(i,val){var load_more_btn=$(this);var page=parseInt(load_more_btn.attr('data-page'));var attr=load_more_btn.attr('data-attribute');var post_wrap=load_more_btn.closest('.kapee-element').find('.articles-list');var wrap_id=load_more_btn.closest('.kapee-element').attr('id');var bottomOffset=post_wrap.offset().top+post_wrap.height()-$(window).scrollTop();if(bottomOffset<window.innerHeight&&bottomOffset>0){if(load_more_btn.hasClass('process')){kapee.isPostLoading=!0}else{kapee.isPostLoading=!1}
var page=parseInt(load_more_btn.attr('data-page'));if(!load_more_btn.hasClass('kapee-loadmore-disabled')){load_more_btn.trigger('click')}}})}
var scrollHandler=function(){requestAnimationFrame(animationFrame)};$(window).scroll(scrollHandler)}
kapee.socialShare=function(){kapee.$doc.on('click','.social-print',function(){window.print();return!1});kapee.$doc.on('click','.social-share a',function(){var link=jQuery(this).attr('href');if(link!='#'){window.open(link,'TIEshare','height=450,width=760,resizable=0,toolbar=0,menubar=0,status=0,location=0,scrollbars=0');return!1}})};kapee.portfolioFilters=function(){if(!$('.portfolios-list').length>0)return;var $layoutMode='masonry';if($('.portfolios-list').length){$('.portfolios-list').each(function(){if($(this).hasClass('simple-grid')){$layoutMode='fitRows'}
var portfolio_container=$(this);portfolio_container.imagesLoaded(function(){portfolio_container.isotope({itemSelector:'.portfolio-post-loop',isOriginLeft:!$('body').hasClass('rtl'),layoutMode:$layoutMode})})})}
$('.filter-categories').on('click','a',function(e){e.preventDefault();var $portfolio_container=$(this).closest('.portfolio-filter').next('.portfolios-list');if($portfolio_container.hasClass('simple-grid')){$layoutMode='fitRows'}
$('.filter-categories').find('.active').removeClass('active');$(this).addClass('active');var filterValue=$(this).attr('data-filter');$portfolio_container.isotope({filter:filterValue,layoutMode:$layoutMode,})})};kapee.portfolioLoadMore=function(){$('.kapee-portfolio-load-more .kapee-load-more').on('click',function(){var $this=$(this),portfolio_wrap=$this.parent().parent().parent().find('.portfolios-list'),data_attr=$this.parent().data(),atts=data_attr.attribute,page=parseInt($this.parent().attr('data-page')),load_more_label=data_attr.load_more_label,loading_finished_msg=data_attr.loading_finished_msg;$this.addClass('request-running');if($this.hasClass('kapee-loadmore-disabled')){return}
$this.html('<span class="loading"> '+kapee_options.js_translate_text.loading_txt+'</span>');var data={attr:atts,page:page,nonce:kapee_options.nonce,action:'kapee_loadmore_portfolios',};$.ajax({url:kapee_options.ajax_url,data:data,dataType:'json',method:'POST',success:function(response){var portfolio_items=$(response.html);if(portfolio_items){if(portfolio_wrap.hasClass('masonry-grid')){portfolio_wrap.append(portfolio_items).isotope('appended',portfolio_items);portfolio_wrap.imagesLoaded().progress(function(){portfolio_wrap.isotope('layout')})}else{portfolio_wrap.append(portfolio_items)}}
if($.trim(response.show_bt)=='0'){$this.addClass('disabled kapee-loadmore-disabled').html(loading_finished_msg)}else{$this.parent().attr('data-page',page+1);$this.html(load_more_label)}},error:function(response){console.log('ajax error')},complete:function(){kapee.isPostLoading=!1;kapee.imagelazyload();kapee.initMagnaficPopup();$this.removeClass('request-running')},})});var animationFrame=function(){$('.kapee-portfolio-load-more a.infinity-scroll').each(function(i,val){var $this=$(this),portfolio_wrap=$this.parent().parent().parent().find('.portfolios-list');var bottomOffset=portfolio_wrap.offset().top+portfolio_wrap.height()-$(window).scrollTop();if(bottomOffset<window.innerHeight&&bottomOffset>0){if($this.hasClass('request-running')){kapee.isPostLoading=!0}else{kapee.isPostLoading=!1}
if(!$this.hasClass('kapee-loadmore-disabled')){if(!kapee.isPostLoading){kapee.isPostLoading=!0;$this.trigger('click')}}}})}
var scrollHandler=function(){requestAnimationFrame(animationFrame)};$(window).scroll(scrollHandler)};kapee.productLiveSearch=function(){if(!kapee_options.product_ajax_search){return!1}
$('.trending-search-results').hide();var serviceUrl=kapee_options.ajax_url+'?action=kapee_ajax_search';$('.kapee-ajax-search').each(function(){var append=$(this).find('.search-results-wrapper'),container=$(this),search_categories=$(this).find('.categories-filter'),product_cat='';if(search_categories.length&&search_categories.val()!==''){serviceUrl+='&product_cat='+search_categories.val()}
$(this).find('.search-field').keyup(function(){var search_text=$(this).val();if(search_text.length<3){$('.trending-search-results').show()}else{$('.trending-search-results').hide()}});$(this).find('.search-field').focus(function(){var search_text=$(this).val();if(search_text.length<3){$('.trending-search-results').show()}else{$('.trending-search-results').hide()}});$(this).find('.search-field').focusout(function(){});$(this).find('.search-field').devbridgeAutocomplete({minChars:3,appendTo:append,triggerSelectOnValidInput:!1,serviceUrl:serviceUrl,onSearchStart:function(){container.find('.search-submit').removeClass('kapee-spinner');container.find('.search-submit').addClass('kapee-spinner');$('.trending-search-results').hide()},onSelect:function(suggestion){if(suggestion.id!=-1){window.location.href=suggestion.url}},onSearchComplete:function(){container.find('.search-submit').removeClass('kapee-spinner')},beforeRender:function(container){$(container).removeAttr('style')},formatResult:function(suggestion,currentValue){var pattern='('+$.Autocomplete.utils.escapeRegExChars(currentValue)+')';var html='';if(suggestion.img)html+='<img class="search-image" src="'+suggestion.img+'">';html+='<div class="search-name">'+suggestion.value.replace(new RegExp(pattern,'gi'),'<strong>$1<\/strong>')+'</div>';if(suggestion.price)html+='<span class="search-price">'+suggestion.price+'</span>';if(suggestion.rating)html+='<span class="search-rating">'+suggestion.rating+'</span>';return html}});if(search_categories.length){var searchForm=$(this).find('.search-field').devbridgeAutocomplete();search_categories.on('change',function(e){if(search_categories.val()!=''){searchForm.setOptions({serviceUrl:serviceUrl+'&product_cat='+search_categories.val()})}else{searchForm.setOptions({serviceUrl:serviceUrl})}
searchForm.hide();searchForm.onValueChange()})}});$(document).mouseup(function(e){var container=$(".kapee-ajax-search");if(!container.is(e.target)&&container.has(e.target).length===0){$(".trending-search-results").hide()}})};kapee.swapLoginSignupFrom=function(){var userSignup=$('.new-signup');var userSignin=$('.user-signin');userSignup.on('click',function(e){e.preventDefault();$('.customer-login').removeClass('active')
$('.customer-signup').addClass('active')});userSignin.on('click',function(e){e.preventDefault();$('.customer-signup').removeClass('active');$('.customer-login').addClass('active')})}
kapee.userLoginSignupPopup=function(){if(!kapee_options.login_register_popup||$('body').hasClass('woocommerce-account')||$('body').hasClass('woocommerce-checkout'))return!1;$('.customer-signinup').magnificPopup({type:'inline',preloader:!1,removalDelay:500,items:{src:'#kapee-signin-up-popup',},callbacks:{open:function(){var closeSidebar=$('.kapee-mask-overaly');var mobileSidebar=$('.kapee-mobile-menu');closeSidebar.removeClass('opened');mobileSidebar.removeClass('opened');$('.kapee-signin-up-popup').addClass('animated fadeInLeft')},beforeClose:function(){var popupWrap=$('.kapee-signin-up-popup');popupWrap.removeClass('fadeInLeft').addClass('fadeOutRight')},close:function(){$('.kapee-signin-up-popup').removeClass('animated fadeOutRight');$('.kapee-signin-up-popup').find('.signin-up-error-message').remove()}}});$(document).on('click','#kapee-signin-up-popup .woocommerce-login-button .button',function(e){var $this=$(this);var $loginform=$this.closest('form');$this.addClass('loading')});$(document).on('click','#kapee-signin-up-popup .woocommerce-form-register .woocommerce-Button.button',function(e){var $this=$(this),$regform=$this.closest('form');$this.addClass('loading')})};kapee.miniCartWidget=function(){if(!kapee_options.header_minicart_popup){return!1}
var headerCart=$('.header-cart');var miniCartSidebar=$('.kapee-minicart-slide');var closeSidebar=$('.kapee-mask-overaly');var mobileSidebar=$('.kapee-mobile-menu');headerCart.on('click',function(e){if($('body').hasClass('woocommerce-cart')||$('body').hasClass('woocommerce-checkout')){return};e.preventDefault();kapee.imagelazyload();if(!miniCartSidebar.hasClass('opened')){miniCartSidebar.addClass('opened');closeSidebar.addClass('opened')}
setTimeout(function(){kapee.imagelazyload()},200)});kapee.$body.on('click','.kapee-mask-overaly, .close-sidebar',function(e){e.preventDefault();miniCartSidebar.removeClass('opened');closeSidebar.removeClass('opened');mobileSidebar.removeClass('opened')});kapee.$doc.keyup(function(e){if(e.keyCode===27){miniCartSidebar.removeClass('opened');closeSidebar.removeClass('opened')}})};kapee.addToWishlist=function(){kapee.$body.on("click",".add_to_wishlist",function(){var tooltip_hide=('.yith-wcwl-add-to-wishlist a');$(tooltip_hide).tooltip('hide');$(this).addClass("loading")})}
kapee.wishlistCount=function(){var kapee_ajax_wishlist_count=function(){$.ajax({type:'POST',url:yith_wcwl_l10n.ajax_url,data:{action:'kapee_ajax_wishlist_count',nonce:kapee_options.nonce,},beforeSend:function(){},complete:function(){},success:function(data){$('span.header-wishlist-count').html(data);kapee.tooltip()}})};$('body').on('added_to_wishlist removed_from_wishlist',kapee_ajax_wishlist_count)};kapee.addToCompare=function(){var button=$("a.compare");kapee.$body.on("click","a.compare",function(){$(this).addClass("loading")});kapee.$body.on("yith_woocompare_open_popup",function(){button.removeClass("loading");kapee.$body.addClass("compare-opened")});kapee.$body.on('click','#cboxClose, #cboxOverlay',function(){kapee.$body.removeClass("compare-opened")})}
kapee.removeToCompare=function(){kapee.$body.on("click",".compare-list tr.remove a",function(){$(this).addClass('loading')})}
kapee.ProductLoopQuantityField=function(){$(".woocommerce .product-buttons").on("change input",".quantity .qty",function(){var add_to_cart_button=$(this).parents(".product").find(".add_to_cart_button");add_to_cart_button.attr("data-quantity",$(this).val())});$(".woocommerce .product-buttons").on("keypress",".quantity .qty",function(e){if((e.which||e.keyCode)===13){$(this).parents(".product").find(".add_to_cart_button").trigger("click")}})}
kapee.compareCount=function(){$('body').on('yith_woocompare_open_popup woocompare_open_popup_mod',function(){$.ajax({type:'POST',url:kapee_options.ajax_url,data:{action:'kapee_ajax_compare_count',nonce:kapee_options.ajax_nonce,},beforeSend:function(){},complete:function(){},success:function(data){$('span.header-compare-count').html(data);kapee.tooltip()},error:function(errorThrown){}})});$(window).on('yith_woocompare_product_removed',function(){$('body').trigger('woocompare_open_popup_mod')});$('.yith-woocompare-widget').on('click','li a.remove, a.clear-all',function(e){e.preventDefault();var product_id=$(this).data('product_id');$.ajax({type:'POST',url:kapee_options.ajax_url,data:{action:'kapee_ajax_compare_count',nonce:kapee_options.ajax_nonce,id:product_id},beforeSend:function(){},complete:function(){},success:function(data){$('label.compare-count').html(data);kapee.tooltip()},error:function(errorThrown){}})})};kapee.addToCart=function(){$('body').on('added_to_cart',function(event,fragments,cart_hash){if($('.header-cart').length>0){if(kapee_options.product_open_cart_mini=='1'){$('.header-cart').trigger('click')}}});$(document.body).on('updated_cart_totals',function(){setTimeout(function(){kapee.imagelazyload()},200)})};kapee.addToCartAjax=function(){if(!kapee_options.product_add_to_cart_ajax){return}
$('.single_add_to_cart_button').addClass('single_add_to_cart_ajax_button');kapee.$body.find('form.cart').on('click','.single_add_to_cart_button',function(e){var $productWrapper=$(this).parents('.single-product-page');if($productWrapper.hasClass('product-type-external'))return;var $form=$(this).closest('form.cart'),$singleBtn=$(this),product_id=$form.find('input[name=add-to-cart]').val()||$singleBtn.val();if($singleBtn.hasClass('disabled')){return}
if($singleBtn.hasClass('quick-buy-proceed')){return}
if($form.length>0){e.preventDefault()}else{return}
var data={action:'kapee_ajax_add_to_cart','add-to-cart':product_id,nonce:kapee_options.nonce,};$form.serializeArray().forEach(function(element){data[element.name]=element.value});if($singleBtn.hasClass('loading')){return}
$singleBtn.addClass('loading');$(document.body).trigger('adding_to_cart',[$singleBtn,data]);$.ajax({type:'post',url:kapee_options.ajax_url,data:data,beforeSend:function(response){$singleBtn.removeClass('added').removeClass('not-added')},success:function(response){if(response.error&response.product_url){window.location=response.product_url;return}else{if(typeof wc_add_to_cart_params!=='undefined'){if(wc_add_to_cart_params.cart_redirect_after_add==='yes'){window.location=wc_add_to_cart_params.cart_url;return}}
if(response.notices.indexOf('error')>0){$('.woocommerce-notices-wrapper').empty().append(response.notices);$singleBtn.addClass('not-added').removeClass('loading')}else{if(kapee_options.product_open_cart_mini=='1'){$('.header-cart').trigger('click')}
$singleBtn.addClass('added').removeClass('loading');$(document.body).trigger('added_to_cart',[response.fragments,response.cart_hash,$singleBtn])}}},})})};kapee.stickyAddToCart=function(){if(!$('body').hasClass('single-product')||$('.kapee-sticky-add-to-cart').length==0||$(window).width()<992||$('.entry-summary form .single_add_to_cart_button').length==0){return}
$('body').on('click','.kapee-sticky-add-to-cart .button',function(event){event.preventDefault();if($(this).hasClass('variable')||$(this).hasClass('grouped')){$('html, body').animate({scrollTop:$(".variations").offset().top-70},1000)}else{$(this).addClass('loading');$('.entry-summary .single_add_to_cart_button').click();event.stopPropagation()}});$(document.body).on('added_to_cart',function(){$('.kapee-sticky-add-to-cart .button').removeClass('loading')});var cartButtonOffset=$('.entry-summary form .single_add_to_cart_button').offset().top;$(window).scroll(function(e){var documentHeight=$(document).height();var windowHeight=$(this).height();var scrollTop=$(this).scrollTop();var bottomscroll=parseInt(windowHeight+scrollTop)+100;if(scrollTop>cartButtonOffset){$('.kapee-sticky-add-to-cart').slideDown();$('.kapee-back-to-top').addClass('sticky-add-cart')}else if(scrollTop<cartButtonOffset){$('.kapee-sticky-add-to-cart').slideUp();$('.kapee-back-to-top').removeClass('sticky-add-cart')}})};kapee.productQuickView=function(){kapee.$doc.on("click",".quickview-btn",function(e){e.preventDefault();var $btn=$(this),pid;if($btn.hasClass('loading'))return;$btn.addClass('loading');var pid=$btn.attr('data-id');$.ajax({url:kapee_options.ajax_url,data:{action:'kapee_product_quick_view',pid:pid,nonce:kapee_options.nonce,},timeout:10000,success:function(response){$.magnificPopup.open({alignTop:!0,removalDelay:500,overflowY:'scroll',items:{src:'<div class="animated fadeInLeft kapee-quick-view woocommerce">'+response+'</div>',type:'inline'},callbacks:{open:function(){},beforeClose:function(){var quickViewWrap=$('.kapee-quick-view');quickViewWrap.addClass('fadeOutRight')},},});if($('.woocommerce-product-gallery').find('.single-product-gallery').length>0){$('.single-product-gallery').not('.slick-initialized').slick({slidesToShow:1,slidesToScroll:1})}
$('.woocommerce-product-gallery__image a').on('click',function(e){e.preventDefault()});setTimeout(function(){var form_variation=$('.product-quick-view').find('.variations_form');if(form_variation.length>0){form_variation.trigger('check_variations');form_variation.trigger('reset_image');form_variation.wc_variation_form();form_variation.find('select').change();kapee.singlePageSwatch()}},1000);$btn.removeClass('loading');kapee.addToCartAjax();kapee.productQuickShop();kapee.tooltip();kapee.imagelazyload()},error:function(error){console.log(error);$btn.removeClass('loading')},})})}
kapee.productShowFilter=function(){$('.product-show .show-number').on('change',function(){if(!kapee.$body.hasClass('kapee-catalog-ajax-filter')){$(this).closest('form').submit()}})}
kapee.priceSlider=function(){if(typeof woocommerce_price_slider_params==='undefined'){return!1}
if(!$('#main-content').find('.widget_price_filter').length){return!1}
$('input#min_price, input#max_price').hide();$('.price_slider, .price_label').show();var min_price=$('.price_slider_amount #min_price').data('min'),max_price=$('.price_slider_amount #max_price').data('max'),current_min_price=parseInt(min_price,10),current_max_price=parseInt(max_price,10);if($('.price_slider_amount #min_price').val()!=''){current_min_price=parseInt($('.price_slider_amount #min_price').val(),10)}
if($('.price_slider_amount #max_price').val()!=''){current_max_price=parseInt($('.price_slider_amount #max_price').val(),10)}
$(document.body).on('price_slider_create price_slider_slide',function(event,min,max){if(woocommerce_price_slider_params.currency_pos==='left'){$('.price_slider_amount span.from').html(woocommerce_price_slider_params.currency_symbol+min);$('.price_slider_amount span.to').html(woocommerce_price_slider_params.currency_symbol+max)}else if(woocommerce_price_slider_params.currency_pos==='left_space'){$('.price_slider_amount span.from').html(woocommerce_price_slider_params.currency_symbol+' '+min);$('.price_slider_amount span.to').html(woocommerce_price_slider_params.currency_symbol+' '+max)}else if(woocommerce_price_slider_params.currency_pos==='right'){$('.price_slider_amount span.from').html(min+woocommerce_price_slider_params.currency_symbol);$('.price_slider_amount span.to').html(max+woocommerce_price_slider_params.currency_symbol)}else if(woocommerce_price_slider_params.currency_pos==='right_space'){$('.price_slider_amount span.from').html(min+' '+woocommerce_price_slider_params.currency_symbol);$('.price_slider_amount span.to').html(max+' '+woocommerce_price_slider_params.currency_symbol)}
$(document.body).trigger('price_slider_updated',[min,max])});if(typeof $.fn.slider!=='undefined'){$('.price_slider').slider({range:!0,animate:!0,min:min_price,max:max_price,values:[current_min_price,current_max_price],create:function(){$('.price_slider_amount #min_price').val(current_min_price);$('.price_slider_amount #max_price').val(current_max_price);$(document.body).trigger('price_slider_create',[current_min_price,current_max_price])},slide:function(event,ui){$('input#min_price').val(ui.values[0]);$('input#max_price').val(ui.values[1]);$(document.body).trigger('price_slider_slide',[ui.values[0],ui.values[1]])},change:function(event,ui){$(document.body).trigger('price_slider_change',[ui.values[0],ui.values[1]])}})}};kapee.productShowHideFilters=function(){$('.archive.woocommerce').on('click','.kapee-product-filter-btn',function(e){var $this=$(this),filter_content=$('#kapee-filter-widgets');$this.toggleClass("active");filter_content.toggleClass('active');filter_content.slideToggle('slow')})}
kapee.productFilterAjax=function(){if(!kapee.$body.hasClass('kapee-catalog-ajax-filter')){return}
$(document.body).on('price_slider_change',function(event,ui){var form=$('.price_slider').closest('form').get(0),$form=$(form),url=$form.attr('action')+'?'+$form.serialize();$(document.body).trigger('kapee_shop_filter_ajax',url,$(this))});kapee.$body.on('click','.widget_product_categories ul a, .widget_rating_filter ul a, .widget_layered_nav_filters ul a, .widget_product_tag_cloud a',function(e){e.preventDefault();var url=$(this).attr('href');$(document.body).trigger('kapee_shop_filter_ajax',url,$(this))});kapee.$body.on('click','.products-view a',function(e){e.preventDefault();var $this=$(this),url=$this.attr('href');$this.siblings().removeClass("active");$this.addClass('active');$(document.body).trigger('kapee_shop_filter_ajax',url,$this)});$('.archive.woocommerce').on('change','.product-show .show-number',function(e){e.stopPropagation();var $this=$(this);var url=window.location.href.replace(/&per_page(=[^&]*)?|^per_page(=[^&]*)?&?/g,'').replace(/\?per_page(=[^&]*)?|^per_page(=[^&]*)?&?/g,'?').replace(/\?$/g,'');if(url.indexOf('?')!==-1){url=url+('&per_page='+$this.val())}else{url=url+('?per_page='+$this.val())}
$(document.body).trigger('kapee_shop_filter_ajax',url,$this)});$(document).on('click','.kapee-pagination .page-numbers:not(.current)',function(e){e.preventDefault();var $this=$(this);var url=$this.attr('href');$(document.body).trigger('kapee_shop_filter_ajax',url,$this)});$('.archive.woocommerce').find('.woocommerce-ordering').off('change');$('.archive.woocommerce').on('change','.woocommerce-ordering',function(e){var $this=$(this);var $select=$(e.currentTarget).find('.orderby');var url=window.location.href.replace(/&orderby(=[^&]*)?|^orderby(=[^&]*)?&?/g,'').replace(/\?orderby(=[^&]*)?|^orderby(=[^&]*)?&?/g,'?').replace(/\?$/g,'');if(url.indexOf('?')!==-1){url=url+('&orderby='+$select.val())}else{url=url+('?orderby='+$select.val())}
$(document.body).trigger('kapee_shop_filter_ajax',url,$this)});$('.archive.woocommerce').on('click','.kapee_widget_product_sorting li',function(e){e.preventDefault();var $this=$(this);var element=$this.find('a');var url=$this.find('a').attr('href');$this.siblings().removeClass("chosen");if($this.hasClass('chosen')){$this.removeClass('chosen')}else{$this.addClass('chosen')};$(document.body).trigger('kapee_shop_filter_ajax',url,element)});$('.archive.woocommerce').on('click','.widget_layered_nav_filters a, .kapee-clear-filters-wrapp a',function(e){e.preventDefault();var $this=$(this);var url=$this.attr('href');$(document.body).trigger('kapee_shop_filter_ajax',url,$this)});$('.archive.woocommerce').on('click','.widget_layered_nav li,.widget_rating_filter li,.widget_layered_nav_filters li',function(e){e.preventDefault();var $this=$(this);var element=$this.find('a');var url=$this.find('a').attr('href');if($this.hasClass('chosen')){$this.removeClass('chosen')}else{$this.addClass('chosen')};$(document.body).trigger('kapee_shop_filter_ajax',url,element)});$(document.body).on('kapee_shop_filter_ajax',function(e,url,element){var $page_title=$('#page-title'),$product_container=$('#main-content .products-wrap'),$main_content=$('#main-content');$('#kapee-filter-widgets').slideUp(200);if($product_container.length>0){var position=$('.products').offset().top-200;$('html, body').stop().animate({scrollTop:position},1200)}
if('?'==url.slice(-1)){url=url.slice(0,-1)}
url=url.replace(/%2C/g,',');history.pushState(null,null,url);$(document.body).trigger('kapee_ajax_filter_before_send_request',[url,element]);if(kapee.ajaxXHR){kapee.ajaxXHR.abort()}
kapee.ajaxXHR=$.get(url,function(res){$page_title.replaceWith($(res).find('#page-title').clone());var content_res=$(res).find('#main-content').html(),page_title_res=$(res).find('#page-title').html();$main_content.html('').append(content_res);$(document.body).trigger('kapee_ajax_filter_request_end',[res,url])},'html')});$(document.body).on('kapee_ajax_filter_before_send_request',function(){var $product_container=$('#main-content .products-wrap');if(kapee_options.skeleton_effect){$product_container.parent().addClass('kapee-skeleton')}else{$product_container.addClass('products_overlay');$product_container.append('<div class="kapee_product_loading loading"></div>')}
$('#secondary').removeClass('opened');$('.kapee-mask-overaly').removeClass('opened')});$(document.body).on('kapee_ajax_filter_request_end',function(){kapee.priceSlider();kapee.kapeeOwlCarousel();kapee.initAjaxLoad();kapee.tooltip();kapee.imagelazyload();kapee.widgetToggle();kapee.widgetMenuToggle();kapee.widgetMaxLimitItem();kapee.canvasSidebar();kapee.stickySidebar();kapee.swatchInLoop();kapee.productQuickView();kapee.addToCompare();$(document).trigger('yith_wcwl_reload_fragments');$('.kapee-mask-overaly').removeClass('opened')})}
kapee.productHover=function(){$('.product-style-4.grid-view .product').mouseenter(function(){var product_elemnt=$(this);var product_info_elemnt=product_elemnt.find('.product-info');var variations_elemnt=product_elemnt.find('.product-variations');var variations_height=variations_elemnt.height();if(variations_height&&variations_height>0){product_info_elemnt.css('transform','translate3d(0px, -'+variations_height+'px, 0px)')}}).mouseleave(function(){var product_elemnt=$(this);var product_info_elemnt=product_elemnt.find('.product-info');if(product_info_elemnt){product_info_elemnt.css('transform','translate3d(0px, 0px, 0px)')}})};kapee.productSwatch=function(){this.singlePageSwatch();this.swatchInLoop()}
kapee.singlePageSwatch=function(){var self=this,variationForm=kapee.$doc.find('form.kapee-swatches-wrap.variations_form'),$term=variationForm.find('.swatch-term'),$activeTerm=variationForm.find('.swatch-term:not(.swatch-disabled)');self.$swatchForm=variationForm;$term.each(function(){var $this=$(this),term=$this.attr('data-term'),attr=$this.parent().attr('data-attribute'),$selectbox=self.$swatchForm.find('select#'+attr),val=$selectbox.val();if(val!=''&&term==val){$(this).addClass('swatch-selected')}});$activeTerm.unbind('click').on('click',function(){var $this=$(this),term=$this.attr('data-term'),attr=$this.parent().attr('data-attribute'),$selectbox=self.$swatchForm.find('select#'+attr);if($this.hasClass('swatch-disabled')){return!1}
$selectbox.val(term).trigger('change');$this.closest('.kapee-swatches').find('.swatch-selected').removeClass('swatch-selected');$this.addClass('swatch-selected')});self.$swatchForm.on('woocommerce_update_variation_values',function(){self.$swatchForm.find('select').each(function(){var $this=$(this);var $swatch=$this.parent().parent().find('.kapee-swatches');$swatch.find('.swatch-term').removeClass('swatch-enabled').addClass('swatch-disabled');$this.find('option.enabled').each(function(){var val=$(this).val();$swatch.find('.swatch-term[data-term="'+val+'"]').removeClass('swatch-disabled').addClass('swatch-enabled')})})});self.$swatchForm.on('reset_data',function(){$term.each(function(){var $this=$(this),term=$this.attr('data-term'),attr=$this.parent().attr('data-attribute'),$selectbox=self.$swatchForm.find('select#'+attr),val=$selectbox.val();if(val!=''&&term==val){$(this).addClass('swatch-selected')}else{$(this).removeClass('swatch-selected')}})})}
kapee.swatchInLoop=function(){var self=this,swatchesInLoop=kapee.$doc.find('div.kapee-swatches-wrap');self.$swatches=swatchesInLoop;self.$swatches.each(function(){var $swatches=$(this),$term=$swatches.find('.swatch-term:not(.swatch-disabled)'),$resetBtn=$swatches.find('.reset_variations--loop'),$product=$swatches.closest('.product'),variationData=$.parseJSON($swatches.attr('data-product_variations'));if($swatches.find('.kapee-swatches').length==0){$swatches.addClass('swatch-empty')}
$term.unbind('click').on('click',function(){var $this=$(this);if($this.hasClass('swatch-disabled')){return!1}
var term=$this.attr('data-term');$product.find('.swatch-term').removeClass('swatch-disabled swatch-enabled');$this.parent().find('.swatch-term.swatch-selected').removeClass('swatch-selected');if($this.hasClass('swatch-selected')){$this.parent().removeClass('swatch-activated');$product.removeClass('swatch-product-swatched');if(!$product.find('.swatch-selected').length){$resetBtn.trigger('click')}}else{$this.parent().addClass('swatch-activated');$this.addClass('swatch-selected');$product.addClass('swatch-product-swatched')}
var attributes=self.getChosenAttributes($swatches),currentAttributes=attributes.data;if(attributes.count===attributes.chosenCount){self.updateAttributes($swatches,variationData);var matching_variations=self.findMatchingVariations(variationData,currentAttributes),variation=matching_variations.shift();if(variation){self.foundVariation($swatches,variation)}else{$resetBtn.trigger('click')}}else{self.updateAttributes($swatches,variationData)}});$resetBtn.unbind('click').on('click',function(){$product.removeClass('swatch-product-swatched');$swatches.removeAttr('data-variation_id');$swatches.find('.swatch-swatch').removeClass('swatch-activated');$swatches.find('.swatch-term').removeClass('swatch-enabled swatch-disabled swatch-selected');self.variationsImageUpdate(!1,$product);$(this).hide();return!1})})};kapee.getChosenAttributes=function($swatches){var data={},count=0,chosen=0,$swatch=$swatches.find('.kapee-swatches');$swatch.each(function(){var attribute_name='attribute_'+$(this).attr('data-attribute'),value=$(this).find('.swatch-term.swatch-selected').attr('data-term')||'';if(value.length>0){chosen++}
count++;data[attribute_name]=value});return{'count':count,'chosenCount':chosen,'data':data,}}
kapee.updateAttributes=function($swatches,variationData){var self=this,attributes=self.getChosenAttributes($swatches),currentAttributes=attributes.data,available_options_count=0,$swatch=$swatches.find('.kapee-swatches');$swatch.each(function(idx,el){var current_attr_sw=$(el),current_attr_name='attribute_'+current_attr_sw.attr('data-attribute'),selected_attr_val=current_attr_sw.find('.swatch-term.swatch-selected').attr('data-term'),selected_attr_val_valid=!0,checkAttributes=$.extend(!0,{},currentAttributes);checkAttributes[current_attr_name]='';var variations=self.findMatchingVariations(variationData,checkAttributes);for(var num in variations){if(typeof variations[num]!=='undefined'){var variationAttributes=variations[num].attributes;for(var attr_name in variationAttributes){if(variationAttributes.hasOwnProperty(attr_name)){var attr_val=variationAttributes[attr_name],variation_active='';if(attr_name===current_attr_name){if(variations[num].variation_is_active){variation_active='enabled'}
if(attr_val){current_attr_sw.find('.swatch-term[data-term="'+attr_val+'"]').addClass('swatch-'+variation_active)}else{current_attr_sw.find('.swatch-term').addClass('swatch-'+variation_active)}}}}}}
available_options_count=current_attr_sw.find('.swatch-term.swatch-enabled').length;if(selected_attr_val&&(available_options_count===0||current_attr_sw.find('.swatch-term.swatch-enabled[data-term="'+self.addSlashes(selected_attr_val)+'"]').length===0)){selected_attr_val_valid=!1}
current_attr_sw.find('.swatch-term:not(.swatch-enabled)').addClass('swatch-disabled');if(selected_attr_val){if(!selected_attr_val_valid){current_attr_sw.find('.swatch-term.swatch-selected').removeClass('swatch-selected')}}else{current_attr_sw.find('.swatch-term.swatch-selected').removeClass('swatch-selected')}})}
kapee.addSlashes=function(string){string=string.replace(/'/g,'\\\'');string=string.replace(/"/g,'\\\"');return string}
kapee.findMatchingVariations=function(variationData,settings){var matching=[];for(var i=0;i<variationData.length;i++){var variation=variationData[i];if(this.isMatch(variation.attributes,settings)){matching.push(variation)}}
return matching}
kapee.isMatch=function(variation_attributes,attributes){var match=!0;for(var attr_name in variation_attributes){if(variation_attributes.hasOwnProperty(attr_name)){var val1=variation_attributes[attr_name];var val2=attributes[attr_name];if(val1!==undefined&&val2!==undefined&&val1.length!==0&&val2.length!==0&&val1!==val2){match=!1}}}
return match}
kapee.foundVariation=function($swatches,variation){var self=this,$product=$swatches.closest('.product');$swatches.attr('data-variation_id',variation.variation_id);self.variationsImageUpdate(variation,$product)}
kapee.setVariationAttr=function($el,attr,value){if(undefined===$el.attr('data-o_'+attr)){$el.attr('data-o_'+attr,(!$el.attr(attr))?'':$el.attr(attr))}
if(!1===value){$el.removeAttr(attr)}else{$el.attr(attr,value)}}
kapee.resetVariationAttr=function($el,attr){if(undefined!==$el.attr('data-o_'+attr)){$el.attr(attr,$el.attr('data-o_'+attr))}}
kapee.variationsImageUpdate=function(variation,$product){var self=this,$product_img=$product.find('img.front-image');if(variation&&variation.image_src&&variation.image.src&&variation.image_src.length>1){self.setVariationAttr($product_img,'src',variation.image_src[0]);self.setVariationAttr($product_img,'srcset',variation.image_srcset);self.setVariationAttr($product_img,'sizes',variation.image_sizes)}else{self.resetVariationAttr($product_img,'src');self.resetVariationAttr($product_img,'srcset');self.resetVariationAttr($product_img,'sizes')}}
kapee.variationsImageChange=function(){$.fn.wc_variations_image_update=function(variation){var $form=this,$product=$form.closest('.product'),$product_gallery=$product.find('.images'),$gallery_img=$product.find('.single-product-thumbnails .slick-slide[data-slick-index="0"] img'),$product_img_wrap=$product_gallery.find('.woocommerce-product-gallery__image, .woocommerce-product-gallery__image--placeholder').eq(0),$product_img=$product_img_wrap.find('.wp-post-image'),$product_link=$product_img_wrap.find('a').eq(0);if(variation&&variation.image&&variation.image.src&&variation.image.src.length>1){$product_img.wc_set_variation_attr('src',variation.image.src);$product_img.wc_set_variation_attr('height',variation.image.src_h);$product_img.wc_set_variation_attr('width',variation.image.src_w);$product_img.wc_set_variation_attr('srcset',variation.image.srcset);$product_img.wc_set_variation_attr('sizes',variation.image.sizes);$product_img.wc_set_variation_attr('title',variation.image.title);$product_img.wc_set_variation_attr('alt',variation.image.alt);$product_img.wc_set_variation_attr('data-src',variation.image.full_src);$product_img.wc_set_variation_attr('data-large_image',variation.image.full_src);$product_img.wc_set_variation_attr('data-large_image_width',variation.image.full_src_w);$product_img.wc_set_variation_attr('data-large_image_height',variation.image.full_src_h);$product_img_wrap.wc_set_variation_attr('data-thumb',variation.image.src);$gallery_img.wc_set_variation_attr('src',variation.image.gallery_thumbnail_src);$gallery_img.wc_set_variation_attr('srcset',variation.image.gallery_thumbnail_src);$product_link.wc_set_variation_attr('href',variation.image.full_src)}else{$product_img.wc_reset_variation_attr('src');$product_img.wc_reset_variation_attr('width');$product_img.wc_reset_variation_attr('height');$product_img.wc_reset_variation_attr('srcset');$product_img.wc_reset_variation_attr('sizes');$product_img.wc_reset_variation_attr('title');$product_img.wc_reset_variation_attr('alt');$product_img.wc_reset_variation_attr('data-src');$product_img.wc_reset_variation_attr('data-large_image');$product_img.wc_reset_variation_attr('data-large_image_width');$product_img.wc_reset_variation_attr('data-large_image_height');$product_img_wrap.wc_reset_variation_attr('data-thumb');$gallery_img.wc_reset_variation_attr('src');$gallery_img.wc_reset_variation_attr('srcset');$product_link.wc_reset_variation_attr('href')}
window.setTimeout(function(){$form.wc_maybe_trigger_slide_position_reset(variation);kapee.productImageZoom();$(window).trigger('resize')},20)}};kapee.initAjaxLoad=function(){var button=$('.kapee-ajax-load');button.each(function(i,val){var _option=$(this).data();if(_option!==undefined){var page=_option.total_page,container=_option.container,container_element=_option.container_element,layout=_option.layout,load_more_label=_option.load_more_label,loading_finished_msg=_option.loading_finished_msg,loading_msg=kapee_options.js_translate_text.loading_txt,isLoading=!1,anchor=$(val).find('a'),next=$(anchor).attr('href'),i=2;if(layout=='load-more-button'){$(val).on('click','a',function(e){e.preventDefault();anchor=$(val).find('a');next=$(anchor).attr('href');$(anchor).html('<span class="loading"> '+loading_msg+'</span>');getData()})}else if(layout=='infinity-scroll'){var waiting=!1,endScrollHandle;kapee.$window.on('scroll',function(){if(kapee.$body.find('.infinity-scroll').is(':in-viewport')){kapee.$body.find('.infinity-scroll a').trigger('click')}}).trigger('scroll');kapee.$body.on('click','.kapee-pagination a.button',function(e){if(waiting){return}
waiting=!0;e.preventDefault();var $el=$(this);$el.html('<span class="loading"> '+loading_msg+'</span>');if($el.data('requestRunning')){return}
$el.data('requestRunning',!0);var $pagination=$el.closest('.kapee-pagination'),container=_option.container,container_element=_option.container_element,$products=$pagination.prev('.'+container),href=$el.attr('href');$.get(href,function(response){var content=$(response).find('#primary .'+container).children('.'+container_element),$pagination_html=$(response).find('.kapee-pagination').html();$pagination.html($pagination_html);if($('.masonry-grid').length>0){$products.append(content).isotope('appended',content);$products.imagesLoaded().progress(function(){$products.isotope('layout')})}else{$products.append(content)}
$pagination.find('a').data('requestRunning',!1);waiting=!1;kapee.initMagnaficPopup();kapee.kapeeOwlCarousel();kapee.tooltip();kapee.imagelazyload();kapee.widgetToggle();kapee.widgetMenuToggle();kapee.widgetMaxLimitItem();kapee.canvasSidebar();kapee.stickySidebar();kapee.swatchInLoop();kapee.productQuickView();kapee.addToCompare();$(document.body).trigger('kapee_shop_ajax_loading_end')})})}
var getData=function(){$.get(next+'',function(data){var content=$('.'+container,data).wrapInner('').html(),newElement=$('.'+container,data).find('.'+container_element);next=$(anchor,data).attr('href');if($('.masonry-grid').length>0){$('.'+container).append(newElement).isotope('appended',newElement);$('.'+container).imagesLoaded().progress(function(){$('.'+container).isotope('layout')})}else{$('.'+container).append(newElement)}
$(anchor).text(load_more_label);if(page>i){if(kapee_options.permalink=='plain'){var link=next.replace(/paged=+[0-9]+/gi,'paged='+(i+1))}else{var link=next.replace(/page\/+[0-9]+\//gi,'page/'+(i+1)+'/')}
$(anchor).attr('href',link)}else{$(anchor).text(loading_finished_msg);$(anchor).attr('href','javascript:void(0);').addClass('disabled')}
isLoading=!1;i++;kapee.initMagnaficPopup();kapee.kapeeOwlCarousel();kapee.tooltip();kapee.imagelazyload();kapee.widgetToggle();kapee.widgetMenuToggle();kapee.widgetMaxLimitItem();kapee.canvasSidebar();kapee.stickySidebar();kapee.swatchInLoop();kapee.productQuickView();kapee.addToCompare();$(document).trigger('yith_wcwl_reload_fragments')})}}})}
kapee.loadmoreProducts=function(){var load_more_products_button=$('.kapee-products-load-more');$('.kapee-products-load-more .kapee-load-more').on('click',function(){var load_more_btn=$(this);var page=parseInt(load_more_btn.attr('data-page'));var attr=load_more_btn.attr('data-attribute');var post_wrap=load_more_btn.closest('.kapee-element').find('.products-wrap');var wrap_id=load_more_btn.closest('.kapee-element').attr('id');var data={action:'kapee_loadmore_product',nonce:kapee_options.nonce,attr:attr,page:page,};if(load_more_btn.hasClass('loading')){return!1}
kapee.loadAjaxPost(load_more_btn,data,post_wrap,wrap_id)});var animationFrame=function(){$('.kapee-products-load-more a.infinity-scroll').each(function(i,val){var load_more_btn=$(this);var page=parseInt(load_more_btn.attr('data-page'));var attr=load_more_btn.attr('data-attribute');var post_wrap=load_more_btn.closest('.kapee-element').find('.products-wrap');var wrap_id=load_more_btn.closest('.kapee-element').attr('id');var bottomOffset=post_wrap.offset().top+post_wrap.height()-$(window).scrollTop();if(bottomOffset<window.innerHeight&&bottomOffset>0){var page=parseInt(load_more_btn.attr('data-page'));if(!load_more_btn.hasClass('kapee-loadmore-disabled')){if(!kapee.isPostLoading){kapee.isPostLoading=!0;load_more_btn.trigger('click')}}}})}
var scrollHandler=function(){requestAnimationFrame(animationFrame)};$(window).scroll(scrollHandler)}
kapee.loadAjaxPost=function(btn,data,element_wrap,parantElement){var load_more_label=btn.data('load_more_label');var loading_finished_msg=btn.data('loading_finished_msg');var label_txt='';btn.addClass('process');if(btn.hasClass('kapee-loadmore-disabled')){return}
btn.html('<span class="loading"> '+kapee_options.js_translate_text.loading_txt+'</span>');$.ajax({url:kapee_options.ajax_url,data:data,dataType:'json',method:'POST',success:function(response){var items=$(''+response.html+'');if($.trim(response.success)=='ok'){if($('.masonry-grid').length>0){setTimeout(function(){element_wrap.imagesLoaded().masonry().append(items).masonry('appended',items).masonry('layout')},500)}else{element_wrap.append(items)}
if($.trim(response.show_bt)=='0'){$('#'+parantElement+' .kapee-load-more').addClass('disabled kapee-loadmore-disabled').html(loading_finished_msg)}else{$('#'+parantElement+' .kapee-load-more').attr('data-page',data.page+1);btn.html(load_more_label)}}},error:function(data){console.log('ajax error')},complete:function(){kapee.isPostLoading=!1;kapee.imagelazyload();kapee.initMagnaficPopup();kapee.swatchInLoop();kapee.tooltip();kapee.productQuickView();kapee.addToCompare();$(document).trigger('yith_wcwl_reload_fragments');btn.removeClass('process')},})}
kapee.tooltip=function(){if(!kapee_options.product_tooltip){return}
var tooltip_left=('.whishlist-button a,.yith-wcwl-add-to-wishlist .yith-wcwl-wishlistaddedbrowse a,.product-style-2:not(.list-view) .product-buttons .cart-button,.product-style-2:not(.list-view) .product-buttons .compare-button a,.product-style-2:not(.list-view) .product-buttons .quickview-button a');$(tooltip_left).tooltip({animation:!1,container:'body',trigger:'hover',placement:!kapee.isCheckRTL()?'left':'right',title:function(){if($(this).find('.added_to_cart').length>0){return $(this).find('.add_to_cart_button').text()}
return $(this).text()}});$(document).on('yith_wcwl_fragments_loaded',function(e){$('.whishlist-button .yith-wcwl-add-to-wishlist .yith-wcwl-add-button a,.whishlist-button .yith-wcwl-add-to-wishlist .yith-wcwl-wishlistexistsbrowse a').tooltip({animation:!1,container:'body',trigger:'hover',placement:!kapee.isCheckRTL()?'left':'right',title:function(){return $(this).text()}})});$('.products:not(.list-view):not(.product-style-1.grid-columns-4):not(.product-style-1.grid-columns-3):not(.product-style-1.grid-columns-6):not(.product-style-3.grid-columns-4):not(.product-style-3.grid-columns-3):not(.product-style-3.grid-columns-6):not(.product-style-4.grid-columns-4):not(.product-style-4.grid-columns-3):not(.product-style-4.grid-columns-6):not(.product-style-5.grid-columns-4):not(.product-style-5.grid-columns-3):not(.product-style-5.grid-columns-6) .product-buttons .cart-button,.has-sidebar .products:not(.list-view):not(.product-style-1.grid-columns-4):not(.product-style-1.grid-columns-6):not(.product-style-3.grid-columns-4):not(.product-style-3.grid-columns-6):not(.product-style-4.grid-columns-4):not(.product-style-4.grid-columns-6):not(.product-style-5.grid-columns-4):not(.product-style-5.grid-columns-6) .product-buttons .cart-button,.product-buttons .compare-button a,.product-buttons .quickview-button a,.kapee-tooltip').tooltip({animation:!1,container:'body',trigger:'hover',title:function(){if($(this).find('.added_to_cart').length>0)return $(this).find('.add_to_cart_button').text();return $(this).text()}})}
kapee.productGallerySummarySticky=function(){if($(window).width()<=767||(!$('.kapee-product-sticky').length>0)||'undefined'===typeof($.fn.stick_in_parent)){return}
var $wrapper=$('.kapee-product-sticky');var $gallery=$wrapper.find('.woocommerce-product-gallery');var $summary=$wrapper.find('.entry-summary');var offset=50;if(0===$wrapper.length){return}
setTimeout(function(){if(kapee_options.sticky_product_image||kapee_options.sticky_product_summary){if($summary.outerHeight()<$gallery.outerHeight()){stickySummary()}else{stickyGallery()}
$(window).resize(function(){if($(window).width()<=767){$gallery.trigger('sticky_kit:detach');$summary.trigger('sticky_kit:detach')}else{if($summary.outerHeight()<$gallery.outerHeight()){stickySummary()}else{stickyGallery()}}})}},100);function stickyGallery(){if(!kapee_options.sticky_product_image){return}
$gallery.stick_in_parent({offset_top:offset,sticky_class:'kapee-is-stucked'})}
function stickySummary(){if(!kapee_options.sticky_product_summary){return}
$summary.stick_in_parent({offset_top:offset,sticky_class:'kapee-is-stucked'})}}
kapee.productGallerySlider=function(){if(!$.isFunction($.fn.slick)){return}
if($('.woocommerce-product-gallery').find('.single-product-gallery').length>0){$('.single-product-gallery').not('.slick-initialized').slick({slidesToShow:('product-gallery-horizontal'==kapee_options.product_gallery_layout)?2:1,slidesToScroll:1,asNavFor:('product-gallery-left'==kapee_options.product_gallery_layout||'product-gallery-bottom'==kapee_options.product_gallery_layout)?'.single-product-thumbnails':'',fade:('product-gallery-left'==kapee_options.product_gallery_layout||'product-gallery-bottom'==kapee_options.product_gallery_layout)?!0:!1,dots:('product-gallery-horizontal'==kapee_options.product_gallery_layout||'product-gallery-center'==kapee_options.product_gallery_layout)?!0:!1,infinite:!1,adaptiveHeight:!0,rtl:(kapee.isCheckRTL())?!0:!1,responsive:[{breakpoint:639,settings:{dots:!0}}]})}
if($('.woocommerce-product-gallery').find('.single-product-thumbnails').length>0){$('.single-product-thumbnails').not('.slick-initialized').slick({slidesToShow:4,slidesToScroll:1,asNavFor:'.single-product-gallery',infinite:!1,arrows:!0,focusOnSelect:!0,vertical:('product-gallery-left'==kapee_options.product_gallery_layout)?!0:!1,rtl:('product-gallery-bottom'==kapee_options.product_gallery_layout&&kapee.isCheckRTL())?!0:!1,responsive:[{breakpoint:639,settings:{slidesToShow:4,infinite:!1,vertical:!1,rtl:(kapee.isCheckRTL())?!0:!1,}}]})}
$(document).on('found_variation','.variations_form',function(es,variation){if(variation&&variation.image&&variation.image.src&&variation.image.src.length>1){$('.single-product-gallery, .single-product-thumbnails').slick('slickGoTo',0)}}).on('reset_image',function(){$('.single-product-gallery, .single-product-thumbnails').slick('slickGoTo',0)})};kapee.productImageZoom=function(){if(kapee_options.product_image_zoom){var $wc_gallery=$('.woocommerce-product-gallery'),zoomTarget=$wc_gallery.find('.woocommerce-product-gallery__image > a'),width=zoomTarget.children('img').attr('data-large_image_width'),zoom_options=$.extend({touch:!1});if('ontouchstart' in document.documentElement){zoom_options.on='click'}
setTimeout(function(){zoomTarget.parent().trigger('zoom.destroy').children('.zoomImg').remove()},500);if('undefined'!=typeof width&&zoomTarget.width()<width){zoomTarget.trigger('zoom.destroy');zoomTarget.zoom(zoom_options);zoomTarget.find(':hover').length&&zoomTarget.trigger('mouseover')}}};kapee.productPhotoSwipe=function(){if(kapee_options.product_PhotoSwipe){if($('.woocommerce-product-gallery').length===0){return}
var $wc_gallery=$('.woocommerce-product-gallery');typeof $wc_gallery.data('product_gallery')=='undefined'&&$wc_gallery.wc_product_gallery();this.$wc_gallery=$wc_gallery;this.wc_gallery=$wc_gallery.data('product_gallery');$wc_gallery.children('.woocommerce-product-gallery__trigger').remove();$wc_gallery.off('click','.woocommerce-product-gallery__image a').on('click',kapee.preventDefault);$wc_gallery.on('click','.woocommerce-product-gallery__image a',this.openPhotoswipe.bind(this));$wc_gallery.on('click','.kapee-product-image-full',this.openPhotoswipe.bind(this))}else{$('.woocommerce-product-gallery__image a').on('click',function(e){e.preventDefault()})}};kapee.openPhotoswipe=function(e){if(wc_single_product_params.photoswipe_options){var slider=this.$wc_gallery.find('.single-product-gallery').slick("getSlick");if(slider){wc_single_product_params.photoswipe_options.index=slider.currentSlide}
this.wc_gallery.openPhotoswipe(e);e.stopPropagation()}}
kapee.productSaleCountdown=function(){$('.product-countdown').each(function(){var $this=$(this),template='',end_date=$this.data('end-date');if($this.data('countdown-style')=='countdown-box'){template='<span class="days">%-D<span>'+kapee_options.js_translate_text.days_text+'</span></span><span class="hour">%H<span>'+kapee_options.js_translate_text.hours_text+'</span></span><span class="minute">%M<span>'+kapee_options.js_translate_text.mins_text+'</span></span><span class="second">%S<span>'+kapee_options.js_translate_text.secs_text+'</span></span>'}else{template='%-D'+kapee_options.js_translate_text.sdays_text+':%H'+kapee_options.js_translate_text.shours_text+':%M'+kapee_options.js_translate_text.smins_text+':%S'+kapee_options.js_translate_text.ssecs_text}
$this.countdown(end_date,function(event){$(this).html(event.strftime(template))})})};kapee.productReviewLink=function(){$('body').on('click','a.kapee-rating-review-link',function(){$('.reviews_tab a').click();return!0})};kapee.productPriceSummary=function(){$(document).on('click','.kapee-mobile-device .exclamation-mark.open',function(){var _this=$(this);_this.removeClass('open');_this.find('.kapee-arrow').addClass('active')});$(document).on('click','.kapee-mobile-device .product-price-summary .kapee-close',function(){$('.product-price-summary .kapee-arrow').removeClass('active');$('.product-price-summary').addClass('open')})};kapee.getProductSizeChart=function(){$('.kapee-ajax-size-chart').on('click',function(e){e.preventDefault();var id=$(this).data('id');var data={action:'kapee_ajax_get_size_chart','id':id,nonce:kapee_options.nonce,};var chart_btn=$(this);if(chart_btn.hasClass('loading')){return!1}
chart_btn.addClass('loading');$.ajax({type:'post',url:kapee_options.ajax_url,data:data,beforeSend:function(response){},complete:function(response){chart_btn.removeClass('loading')},success:function(response){$(this).magnificPopup({removalDelay:500,items:{src:response,type:'inline'},callbacks:{open:function(){var popupWrap=$('.kapee-product-sizechart');popupWrap.addClass('animated fadeInLeft')},beforeClose:function(){var popupWrap=$('.kapee-product-sizechart');popupWrap.removeClass('fadeInLeft').addClass('fadeOutRight')},close:function(){var popupWrap=$('.kapee-product-sizechart');popupWrap.removeClass('animated fadeOutRight')}},}).magnificPopup('open')},})})}
kapee.getProductTermsConditions=function(){var cache=[];$('.kapee-ajax-block').on('click',function(e){e.preventDefault();$(document).find('.product-term-detail').removeClass('active');var html="<div class='product-term-detail kapee-arrow active'> <span class='loading'></span></div>";var process=!1;var $this=$(this);var content=$(this).closest('.product-term-wrap').find('.product-term-detail');var id=$(this).data('id');if(id==''){return!1}
if(cache['term_condition_'+id]){$(".product-term-detail").not(content).removeClass('active');if(content.length){content.addClass('active')}else{$(this).after(html);$this.closest('.product-term-wrap').find('.product-term-detail').addClass('active').html(cache['term_condition_'+id])}
return}
var data={action:'kapee_ajax_get_product_terms_conditions','id':id,nonce:kapee_options.nonce,};$(this).after(html);$.ajax({type:'post',url:kapee_options.ajax_url,data:data,beforeSend:function(response){},complete:function(response){content.removeClass('loading')},success:function(response){$this.closest('.product-term-wrap').find('.product-term-detail').addClass('active').html(response);cache['term_condition_'+id]=response},})});$(document).mouseup(function(e){var container=$(".product-term-wrap");if(!container.is(e.target)&&container.has(e.target).length===0){$(".product-term-detail").removeClass('active')}});$(document).on('click','.product-term-wrap .kapee-close',function(){$(".product-term-detail").removeClass('active')})}
kapee.productQuantityPlusMinus=function(){$(document).on('click','.quantity .plus, .quantity .minus',function(){var $qty=$(this).closest('.quantity').find('.qty'),currentVal=parseFloat($qty.val()),max=parseFloat($qty.attr('max')),min=parseFloat($qty.attr('min')),step=$qty.attr('step');if(!currentVal||currentVal===''||currentVal==='NaN')currentVal=0;if(max===''||max==='NaN')max='';if(min===''||min==='NaN')min=0;if(step==='any'||step===''||step===undefined||parseFloat(step)==='NaN')step='1';if($(this).is('.plus')){if(max&&(max==currentVal||currentVal>max)){$qty.val(max)}else{$qty.val(currentVal+parseFloat(step))}}else{if(min&&(min==currentVal||currentVal<min)){$qty.val(min)}else if(currentVal>0){$qty.val(currentVal-parseFloat(step))}}
$qty.trigger('change')})};kapee.productQuickShop=function(){$('body').on('click','.kapee_quick_buy_button',function(){if(kapee_options.product_add_to_cart_ajax){$('.single_add_to_cart_button').addClass('quick-buy-proceed')}
var $this=$(this);var product_id=$(this).attr('data-kapee-product-id');var product_type=$(this).attr('data-product-type');var selected=$('form.cart input#kapee_quick_buy_product_'+product_id);var productform=selected.parent();var submit_btn=productform.find('[type="submit"]');var is_disabled=submit_btn.is(':disabled');if(!$(this).closest('.woocommerce-variation-add-to-cart').hasClass('woocommerce-variation-add-to-cart-disabled')){$this.addClass('loading')}
if(is_disabled){$('html, body').animate({scrollTop:submit_btn.offset().top-200},900)}else{if(!$this.hasClass('disable')){productform.append('<input type="hidden" value="true" name="kapee_quick_buy" />')}
productform.find('.single_add_to_cart_button').trigger('click')}});$('form.cart').change(function(){var is_submit_disabled=$(this).find('[type="submit"]').is(':disabled');if(is_submit_disabled){$('.kapee_quick_buy_button').attr('disabled','disable')}else{$('.kapee_quick_buy_button').removeAttr('disabled')}})}
kapee.productBoughtTogetherInit=function(){if(kapee.$body.find('.kapee-bought-together-products').length===0){return}
var self=this;$('body').on('click','.kapee-bought-together-products .product-checkbox input[type=checkbox]',function(){if($(this).is(":checked")){$(this).closest('.product-wrapper').removeClass('kapee-disable-product')}else{$(this).closest('.product-wrapper').addClass('kapee-disable-product')}
self.productBoughtTogetherChangeEvent()});self.productBoughtTogetherCheckAllItems();self.productBoughtTogetherAddToCart();$('body').on('found_variation',function(event,variation){$('.kapee-bought-together-products .current-item .item-price').each(function(){if($(this).data('type')=='variable'){$(this).data('itemprice',variation.display_price);$(this).html(self.kapee_woo_formated_price(variation.display_price));self.productBoughtTogetherChangeEvent()}})})}
kapee.productBoughtTogetherChangeEvent=function(){var self=this;$('.add-items-to-cart').addClass('loading');var total_price=self.product_bought_together_get_total_price();var addon_total_price=self.product_addons_get_total_price();var total_addon=self.product_bought_together_product_count();if(total_addon){$('.add-items-to-cart').removeAttr("disabled")}else{$('.add-items-to-cart').attr("disabled",!0)}
$('.addons-item .addon-count').html(total_addon);$('.addons-item span.items-price').html(self.kapee_woo_formated_price(addon_total_price));$('.items-total span.total-price').html(self.kapee_woo_formated_price(total_price));$('.add-items-to-cart').removeClass('loading')}
kapee.kapee_woo_formated_price=function(number){var self=this;return self.kapee_formated_price(number,kapee_options.price_thousand_separator,kapee_options.price_decimal_separator,kapee_options.price_decimals,kapee_options.currency_symbol,kapee_options.price_format)}
kapee.kapee_formated_price=function(number,thousand_sep,decimal_sep,tofixed,symbol,woo_price_format){var before_text='';var after_text='';number=number||0;tofixed=!isNaN(tofixed=Math.abs(tofixed))?tofixed:2;symbol=symbol!==undefined?symbol:"$";thousand_sep=thousand_sep||",";decimal_sep=decimal_sep||".";var negative=number<0?"-":"",i=parseInt(number=Math.abs(+number||0).toFixed(tofixed),10)+"",j=(j=i.length)>3?j%3:0;symbol='<span class="woocommerce-Price-currencySymbol">'+symbol+'</span>';switch(woo_price_format){case '%1$s%2$s':before_text+=symbol;break;case '%1$s %2$s':before_text+=symbol+' ';break;case '%2$s%1$s':after_text+=symbol;break;case '%2$s %1$s':after_text+=' '+symbol;break;default:before_text+=symbol}
var woo_price_return=before_text+negative+(j?i.substr(0,j)+thousand_sep:"")+i.substr(j).replace(/(\d{3})(?=\d)/g,"$1"+thousand_sep)+(tofixed?decimal_sep+Math.abs(number-i).toFixed(tofixed).slice(2):"")+after_text;woo_price_return='<span class="woocommerce-Price-amount amount">'+woo_price_return+'</span>';return woo_price_return}
kapee.productBoughtTogetherCheckAllItems=function(){var self=this;$('body').on('click','.check-all-items',function(){$('.kapee-together-product:checkbox').not(this).prop('checked',this.checked);if($(this).is(":checked")){$('.kapee-together-product:checkbox').prop('checked',!0)}else{$('.kapee-together-product:checkbox').prop("checked",!1)}
self.productBoughtTogetherChangeEvent()})}
kapee.BoughtTogetherIsVariationProduct=function(){var product_type=$('.kapee-bought-together-products .current-item .item-price').data('type');if(product_type=='variable'){return!0}
return!1}
kapee.BoughtTogetherCurrentProductid=function(){var product_id=$('.kapee-bought-together-products .current-item .item-price').data('id');return product_id}
kapee.BoughtTogetherVariationAvailable=function(){if($(".single_add_to_cart_button").length===0||$(".single_add_to_cart_button").hasClass("disabled")||$(".single_add_to_cart_button").hasClass("wc-variation-is-unavailable")){return!1}
return!0}
kapee.productBoughtTogetherAddToCart=function(){var self=this;$('body').on('click','.add-items-to-cart:not(.loading)',function(e){e.preventDefault();var is_variation_product=kapee.BoughtTogetherIsVariationProduct();var variation_product_id=kapee.BoughtTogetherCurrentProductid();if(is_variation_product&&kapee.BoughtTogetherVariationAvailable()===!1){alert(kapee_options.js_translate_text.variation_unavailable);return}
var self_this=$(this);self_this.addClass('loading');var all_product_ids=self.product_bought_together_get_checked_product_ids();var msg='';if(all_product_ids.length===0){msg=kapee_options.bought_together_error}else{setTimeout(function(){for(var i=0;i<all_product_ids.length;i++){if(is_variation_product&&all_product_ids[i]==variation_product_id){var variation_id=$('.variations_form .variations_button').find('input[name^=variation_id]').val();var variation={};if($('.variations_form').find('select[name^=attribute]').length){$('.variations_form').find('select[name^=attribute]').each(function(){var attribute=$(this).attr("name");var attributevalue=$(this).val();variation[attribute]=attributevalue})}else{$('.variations_form').find('.select').each(function(){var attribute=$(this).attr("data-attribute-name");var attributevalue=$(this).find('.selected').attr('data-name');variation[attribute]=attributevalue})}
$.ajax({type:"POST",async:!1,url:kapee_options.ajax_url,data:{'product_id':all_product_ids[i],'variation_id':variation_id,'variation':variation,'action':'kapee_all_add_to_cart'},success:function(response){self.product_bought_together_refresh_fragments(response)}})}else{$.ajax({type:"POST",async:!1,url:kapee_options.ajax_url,data:{'product_id':all_product_ids[i],'action':'kapee_all_add_to_cart'},success:function(response){self.product_bought_together_refresh_fragments(response)}})}}
var miniCartSidebar=$('.kapee-minicart-slide');var closeSidebar=$('.kapee-mask-overaly');if(!miniCartSidebar.hasClass('opened')){miniCartSidebar.addClass('opened');closeSidebar.addClass('opened');self.initNanoScroller()}
self_this.removeClass('loading')},300)}
if(msg!=''){$('.kapee-wc-message').html(msg).show(100);self_this.removeClass('loading');setTimeout(function(){$('.kapee-wc-message').slideUp(500)},3000)}})}
kapee.product_bought_together_get_total_price=function(){var tprice=0,itemprice=0;itemprice=$('.items-total-price .item-price').data('itemprice');tprice=parseFloat(itemprice);$('.kapee-bought-together-products .product-checkbox input[type=checkbox]').each(function(){if($(this).is(':checked')){tprice+=parseFloat($(this).data('price'))}});return tprice}
kapee.product_addons_get_total_price=function(){var tprice=0;$('.kapee-bought-together-products .product-checkbox input[type=checkbox]').each(function(){if($(this).is(':checked')){tprice+=parseFloat($(this).data('price'))}});return tprice}
kapee.product_bought_together_product_count=function(){var pcount=0;$('.kapee-bought-together-products .product-checkbox input[type=checkbox]').each(function(){if($(this).is(':checked')){pcount++}});return pcount}
kapee.product_bought_together_get_checked_product_ids=function(){var pids=[],pidd;pidd=$('.items-total-price .item-price').data('id');pids.push(pidd);$('.kapee-bought-together-products .product-checkbox input[type=checkbox]').each(function(){if($(this).is(':checked')){pids.push($(this).data('id'))}});return pids}
kapee.product_bought_together_refresh_fragments=function(response){var frags=response.fragments;if(frags){$.each(frags,function(key){$(key).addClass('updating')})}
if(frags){$.each(frags,function(key,value){$(key).replaceWith(value)})}}
kapee.wooProductTabsAccordian=function(){if(($('.woocommerce-tabs.accordion-layout').length>0)||($('.woocommerce-tabs.tabs-layout').length>0)){var $accordion=$('.tab-content-wrap');var hash=window.location.hash;var url=window.location.href;if(hash.toLowerCase().indexOf('comment-')>=0||hash==='#reviews'||hash==='#tab-reviews'){$accordion.find('.title-reviews').addClass('open')}else if(url.indexOf('comment-page-')>0||url.indexOf('cpage=')>0){$accordion.find('.title-reviews').addClass('open')}else if(hash==='#tab-additional_information'){$accordion.find('.title-additional_information').addClass('open')}else{$accordion.find('.accordion-title').first().addClass('open')}
$accordion.on('click','.accordion-title',function(e){e.preventDefault();$(this).parent().siblings().find('.woocommerce-Tabs-panel').slideUp('fast');$(".accordion-title").not($(this)).removeClass("open");$(this).toggleClass("open").next().slideToggle('fast')});$(document).on('click','a.woocommerce-review-link',function(e){$accordion.find('.accordion-title').removeClass("open");$accordion.find('.title-reviews').addClass("open")})}}
kapee.wooProductTabsToggle=function(){if($('.woocommerce-tabs.toggle-layout').length>0){var $accordion=$('.tab-content-wrap');$accordion.find('.accordion-title').addClass('open');$accordion.find('.woocommerce-Tabs-panel').css("display","block");$accordion.on('click','.accordion-title',function(e){e.preventDefault();var accordion=$(this);var accordionContent=accordion.next('.woocommerce-Tabs-panel');accordion.toggleClass("open");accordionContent.slideToggle(250)});$(document).on('click','a.woocommerce-review-link',function(e){e.stopPropagation()})}}
kapee.wooCheckoutStep=function(){var btn_next=$('.checkout-next-step'),wooStepWrap=$('#checkout-wrapper .panel.panel-default'),mutistepCheckout=$('#multi-step-checkout');if(mutistepCheckout.length>0){var formscrollTo=mutistepCheckout.offset().top-30}
btn_next.on('click',function(){$(window).unbind('beforeunload');var currentPanel=$(this).closest("div.panel.panel-default"),this_btn=$(this),currentstep=currentPanel.data('step'),steptitle=currentPanel.data('steptitle'),nextPanel_id=this_btn.data("next"),nextPanel=$("#"+nextPanel_id),billing=$('#customer_billing_details'),shipping=$('#customer_shipping_details'),selector=null,valid=!1,$offset=30,posted_data={};this_btn.addClass('loading');$('form.woocommerce-checkout .woocommerce-NoticeGroup.woocommerce-NoticeGroup-checkout').remove();setTimeout(function(){if(steptitle=='billing'||steptitle=='shipping'){if(steptitle=='billing'){selector=billing}
if(steptitle=='shipping'){selector=shipping}
$(selector).find('.validate-required input').each(function(){posted_data[$(this).attr('name')]=$(this).val()});$(selector).find('.validate-required select').each(function(){posted_data[$(this).attr('name')]=$(this).val()});$(selector).find('.input-checkbox').each(function(){if($(this).is(':checked')){posted_data[$(this).attr('name')]=$(this).val()}});var data={action:'kapee_validate_checkout',type:steptitle,posted_data:posted_data,nonce:kapee_options.nonce,};$.ajax({type:'POST',url:kapee_options.ajax_url,data:data,async:!1,success:function(response){valid=response.valid;if(!response.valid){currentPanel.find('.panel-body').prepend(response.html);$('html, body').animate({scrollTop:$('form.woocommerce-checkout').offset().top-$offset},'slow')}
this_btn.removeClass('loading')},complete:function(){}})}else{valid=!0}
if(valid){}else{return!1}
if($(nextPanel).hasClass('hidden')){currentPanel.removeClass('active');currentPanel.addClass('completed');currentPanel.find('.panel-collapse').slideUp('slow').removeClass('show');this_btn.removeClass('loading');$(nextPanel).removeClass('hidden');$(nextPanel).addClass('active');$(nextPanel).find('.panel-collapse').slideDown('slow').addClass('show');$('html, body').animate({scrollTop:formscrollTo},'slow')}
return!1},300)});$('.edit-action').on('click',function(){var currentPanel=$(this).closest("div.panel.panel-default"),this_btn=$(this),currentstep=currentPanel.data('step');currentPanel.removeClass('completed').addClass('active');currentPanel.find('.panel-collapse').slideDown('slow').addClass('show');kapee_deactivate_steps(currentstep)});function kapee_deactivate_steps(currentstep){var panels=mutistepCheckout.find('.panel');panels.each(function(index){var current_panel=$(this),step=current_panel.data('step');if(currentstep<step){var stepwrap=$("#step-"+step);if(stepwrap.hasClass('active')){stepwrap.find('.panel-collapse').slideUp('slow').removeClass('show');stepwrap.removeClass('active');stepwrap.addClass('hidden')}
if(stepwrap.hasClass('completed')){stepwrap.removeClass('completed');stepwrap.addClass('hidden')}}})}}
kapee.resetVariations=function(){var price_html='',discount_html='',price_summary_html='',$price_summer_inner_price=$(document).find(".woocommerce div.entry-summary > p.price"),$price_summer_inner_discount=$(document).find(".woocommerce div.entry-summary .product-price-discount"),$price_summer_inner_price_summary=$(document).find(".woocommerce div.entry-summary .product-price-summary");if($price_summer_inner_price.length){price_html=$price_summer_inner_price.html()}
if($price_summer_inner_discount.length){discount_html=$price_summer_inner_discount.html()}
if($price_summer_inner_price_summary.length){price_summary_html=$price_summer_inner_price_summary.html()}
$(document).find(".variations_form").on("reset_data",function(){$(document).find(".woocommerce div.entry-summary > p.price").html(price_html);$(document).find(".woocommerce div.entry-summary .product-price-discount").html(discount_html);$(document).find(".woocommerce div.entry-summary .product-price-summary").html(price_summary_html);if($(document).find(".woocommerce .kapee-quick-buy").length){$(document).find(".kapee_quick_buy_button").addClass('disabled')}})}
kapee.variationChangeevent=function(){$(".single_variation_wrap").on("show_variation",function(event,variation){if($(document).find(".woocommerce .kapee-quick-buy").length){$(document).find(".kapee_quick_buy_button").removeClass('disabled')}});$(document).find(".variations_form").on("woocommerce_variation_select_change",function(){if(kapee_options.disable_variation_price_change){return}
$(document).find(".single_variation_wrap").on("show_variation",function(event,variation){var sell_price=variation.display_price,regular_price=variation.display_regular_price,formate_sell_price,formate_regular_price,total=0,discount_per=0,discount=regular_price-sell_price,formate_discount,price_html=variation.price_html,test_var=1;formate_sell_price=kapee.kapee_formated_price(sell_price,kapee_options.price_thousand_separator,kapee_options.price_decimal_separator,kapee_options.price_decimals,kapee_options.currency_symbol,kapee_options.price_format);formate_regular_price=kapee.kapee_formated_price(regular_price,kapee_options.price_thousand_separator,kapee_options.price_decimal_separator,kapee_options.price_decimals,kapee_options.currency_symbol,kapee_options.price_format);formate_discount=kapee.kapee_formated_price(discount,kapee_options.price_thousand_separator,kapee_options.price_decimal_separator,kapee_options.price_decimals,kapee_options.currency_symbol,kapee_options.price_format);if(sell_price!=regular_price){$(document).find('.woocommerce div.entry-summary .product-price-discount').show();$(document).find('.product-price-summary .discount').show();$(document).find('.product-price-summary .overall-discount').show();discount_per=Math.round(((regular_price-sell_price)/regular_price)*100);$(document).find('.woocommerce div.entry-summary > p.price').html('<ins>'+formate_sell_price+'</ins><del> '+formate_regular_price+'</del>');$(document).find('.woocommerce div.entry-summary .product-price-discount .on-sale span').html(discount_per);$(document).find('.product-price-summary .regular-price > span').html(formate_regular_price);$(document).find('.product-price-summary .selling-price span').html(formate_sell_price);$(document).find('.product-price-summary .discount span').html(discount_per+'%');$(document).find('.product-price-summary .total-discount span').html(formate_sell_price);$(document).find('.product-price-summary .overall-discount .amount-discount').html(formate_discount);$(document).find('.product-price-summary .overall-discount .percentage-discount').html('('+discount_per+'%)')}else{$(document).find('.woocommerce div.entry-summary > p.price').html(formate_regular_price);$(document).find('.product-price-summary .regular-price > span').html(formate_regular_price);$(document).find('.product-price-summary .selling-price span').html(formate_regular_price);$(document).find('.product-price-summary .total-discount span').html(formate_regular_price);$(document).find('.woocommerce div.entry-summary .product-price-discount').hide();$(document).find('.product-price-summary .discount').hide();$(document).find('.product-price-summary .overall-discount').hide()}})})}
kapee.wcfm_vendor=function(){if($('#_kp_product_ids').length<=0){return!1}
if(typeof $wcfm_product_select_args==='undefined'){return!1}
$('#_kp_product_ids').select2($wcfm_product_select_args)};kapee.MiniCartUpdateQuantity=function(){$(document).on('change','.woocommerce-mini-cart .qty',function(){var quantity=parseFloat($(this).val());var max=parseFloat($(this).attr('max'));if(max!=='NaN'&&max<quantity){quantity=max;$(this).val(max)}
$(this).parents('.woocommerce-mini-cart-item').addClass('loading');var cart_item_key=$(this).parents('.woocommerce-mini-cart-item').attr('data-cart_item_key');console.log(cart_item_key);var data={quantity:quantity,cart_item_key:cart_item_key,nonce:kapee_options.nonce,action:'kapee_update_cart_widget_quantity',};$.ajax({url:kapee_options.ajax_url,data:data,dataType:'json',method:'POST',success:function(response){if(!response){return}
$(document.body).trigger('added_to_cart',[response.fragments,response.cart_hash])}})})};kapee.kapeeEqualTabsHeight=function(){setTimeout(function(){$('.products-tab-content').each(function(){var $this=$(this);if($this.find('.tab-content').length){$this.find('.tab-content').css({'height':'auto'});var elem_height=0;$this.find('.tab-content').each(function(){var this_elem_h=$(this).height();if(elem_height<this_elem_h){elem_height=this_elem_h}});$this.find('.tab-content').height(elem_height)}})},4000)};kapee.kapeeTabEffect=function(){$(document).on('click','.products-tabs .nav-tabs a',function(event){var tab_id=$(this).attr('href');var tab_animated='animate__fadeInUp';tab_animated=(tab_animated==undefined||tab_animated=="")?'':tab_animated;if(tab_animated==""){return!1}
$(tab_id).find('.owl-item.active').each(function(i){var t=$(this);var style=$(this).attr("style");var defaultcss=$(this).attr('default-style');style=(style==undefined)?'':style;if(defaultcss==undefined){$(this).attr('default-style',style)}else{style=$(this).attr('default-style')}
var delay=i*100;t.attr("style",style+";-webkit-animation-delay:"+delay+"ms;"+"-moz-animation-delay:"+delay+"ms;"+"-o-animation-delay:"+delay+"ms;"+"animation-delay:"+delay+"ms;").addClass(tab_animated+' animate__animated').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend',function(){t.removeClass(tab_animated+' animate__animated');t.attr("style",style)})});$(tab_id).addClass('active').siblings().removeClass('active')})}
kapee.kapeeAjaxTab=function(){$(document).on('click','.products-tab-container [data-ajax="1"]',function(event){var datasource=$(this).data('datasource');var limit=$(this).data('limit');var slider_data=$(this).data('slider_data');var tab_id=$(this).attr('href');var current_tab=$(this);var data={action:'kapee_get_products_tab_data',nonce:kapee_options.nonce,datasource:datasource,limit:limit,slider_data:slider_data,};var container=$(this).closest('.products-tab-container').find('.tab-content');$.post(kapee_options.ajax_url,data,function(response){var items=$(''+response.html+'');container.html(items);kapee.kapeeOwlCarousel()})})}
kapee.kapeeResponsiveTab=function(){$(document).on('click','[data-trigger="tab"]',function(event){var href=$(this).attr('href');event.preventDefault();$('[data-toggle="tab"][href="'+href+'"]').trigger('click')})}
kapee.kapeeProgressbar=function(){$('.progress .progress-bar').each(function(){if(typeof($.fn.waypoint)!='undefined'){$(this).waypoint(function(){var width=$(this).attr('data-value');$(this).animate({width:width+'%'},{duration:1000,easing:'swing'})},{offset:'100%',triggerOnce:!0})}})}
kapee.kapeeCounterUp=function(){if(!$.isFunction($.fn.counterUp)){return}
jQuery('.counter').counterUp({delay:20,time:2000})}
kapee.imageGaleryMasonry=function(){if(!$('.kapee-image-gallery.image-gallery-masonry-grid').length>0)return;var $layoutMode='masonry';if($('.kapee-image-gallery.image-gallery-masonry-grid').length){$('.kapee-image-gallery.image-gallery-masonry-grid').each(function(){var image_gallery_container=$(this);image_gallery_container.imagesLoaded(function(){image_gallery_container.isotope({itemSelector:'.kapee-gallery',isOriginLeft:!$('body').hasClass('rtl'),layoutMode:$layoutMode})})})}};kapee.BackgroundParallax=function(){if($(window).width()<=1024){return}
$('.kapee-parallax-background').each(function(){var $this=$(this);if($this.hasClass('wpb_column')){$this.find('> .vc_column-inner').parallax("50%",0.3)}else{$this.parallax("50%",0.3)}})};$(document).ready(function(){kapee.init()});$(document).ready(function(){$(window).on('vc_reload',function(){kapee.init()})});$(window).on('load',function(){var kapee_pre_loader=$('.kapee-site-preloader');if(kapee_pre_loader.length){kapee_pre_loader.fadeOut(800)}})})(jQuery)