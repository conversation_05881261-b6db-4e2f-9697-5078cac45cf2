tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.multidayEvents={},function(e,t){"use strict";const n=e(document);t.selectors={},t.selectorPrefixes={month:".tribe-events-calendar-month__"},t.selectorSuffixes={multidayEvent:"multiday-event",hiddenMultidayEvent:"multiday-event-hidden",multidayEventBarInner:"multiday-event-bar-inner",multidayEventBarInnerFocus:"multiday-event-bar-inner--focus",multidayEventBarInnerHover:"multiday-event-bar-inner--hover"},t.findVisibleMultidayEvents=function(e,n){const i=n.closest(t.selectors.multidayEvent).data("event-id");return e.find(t.selectors.multidayEvent+"[data-event-id="+i+"]")},t.toggleHoverClass=function(e){e.data.target.toggleClass(t.selectors.multidayEventBarInnerHover.className())},t.toggleFocusClass=function(e){e.data.target.toggleClass(t.selectors.multidayEventBarInnerFocus.className())},t.unbindMultidayEvents=function(n){n.find(t.selectors.hiddenMultidayEvent).each((function(t,n){e(n).off()}))},t.bindMultidayEvents=function(n){n.find(t.selectors.hiddenMultidayEvent).each((function(i,s){const o=e(s);t.findVisibleMultidayEvents(n,o).each((function(n,i){const s=e(i).find(t.selectors.multidayEventBarInner);o.on("mouseenter mouseleave",{target:s},t.toggleHoverClass).on("focus blur",{target:s},t.toggleFocusClass)}))}))},t.deinitSelectors=function(){t.selectors={}},t.initSelectors=function(e){const n=t.selectorPrefixes[e];Object.keys(t.selectorSuffixes).forEach((function(e){t.selectors[e]=n+t.selectorSuffixes[e]}))},t.unbindEvents=function(e,n,i){const s=e.data.container;t.deinitSelectors(),t.unbindMultidayEvents(s),s.off("beforeAjaxSuccess.tribeEvents",t.unbindEvents)},t.bindEvents=function(e,n){const i=n.slug;-1!==e.data("tribeEventsMultidayEventsAllowedViews").indexOf(i)&&(t.initSelectors(i),t.bindMultidayEvents(e),e.on("beforeAjaxSuccess.tribeEvents",{container:e},t.unbindEvents))},t.initAllowedViews=function(e){e.trigger("beforeMultidayEventsInitAllowedViews.tribeEvents",[e]),e.data("tribeEventsMultidayEventsAllowedViews",["month"]),e.trigger("afterMultidayEventsInitAllowedViews.tribeEvents",[e])},t.init=function(e,n,i,s){t.initAllowedViews(i),t.bindEvents(i,s)},t.ready=function(){n.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.multidayEvents),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.multidayEvents={}