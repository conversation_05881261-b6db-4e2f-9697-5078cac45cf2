tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.icalLinks={},function(e,n){"use strict";n.selectors={icalLinks:".tribe-events-c-subscribe-dropdown",icalLinksButton:".tribe-events-c-subscribe-dropdown__button",icalLinksButtonText:".tribe-events-c-subscribe-dropdown__button-text",icalLinksButtonActiveClass:"tribe-events-c-subscribe-dropdown__button--active",icalLinksListContainer:".tribe-events-c-subscribe-dropdown__content",icalLinksListContainerShow:"tribe-events-c-subscribe-dropdown__content--show",icalLinksIcon:".tribe-events-c-subscribe-dropdown__button-icon",icalLinksIconRotate:"tribe-events-c-subscribe-dropdown__button-icon--rotate"},n.handleIcalLinksButtonClick=function(t){t.stopPropagation();const i=e(t.target).closest(n.selectors.icalLinksButton),s=i.siblings(n.selectors.icalLinksListContainer),c=i.find(n.selectors.icalLinksIcon);n.handleAccordionToggle(t),e(n.selectors.icalLinksListContainer).not(s).hide(),e(n.selectors.icalLinksIcon).not(c).removeClass(n.selectors.icalLinksIconRotate),c.toggleClass(n.selectors.icalLinksIconRotate),s.toggle()},n.handleAccordionToggle=function(t){const i=e(t.target).closest(n.selectors.icalLinksButton),s=i.find(n.selectors.icalLinksButtonText);i&&s&&n.handleToggleAccordionExpanded(s)},n.handleToggleAccordionExpanded=function(t){"true"===t.attr("aria-expanded")?(t.attr("aria-expanded",!1),e(n.selectors.icalLinksIcon).removeClass(n.selectors.icalLinksIconRotate)):(t.attr("aria-expanded",!0),e(n.selectors.icalLinksIcon).addClass(n.selectors.icalLinksIconRotate))},n.resetAccordions=function(){e(n.selectors.icalLinksListContainer).hide(),e(n.selectors.icalLinksButtonText).attr("aria-expanded",!1),e(n.selectors.icalLinksIcon).removeClass(n.selectors.icalLinksIconRotate)},n.handleClickOutside=function(t){e(t.target).closest(n.selectors.icalLinks).length||n.resetAccordions()},n.bindEvents=function(t){e(document).on("click",n.selectors.icalLinksButton,n.handleIcalLinksButtonClick),e(document).on("click, focusin",n.handleClickOutside)},n.unbindEvents=function(t){t.find(n.selectors.icalLinksButton).off("click",n.handleIcalLinksButtonClick),e(document).off("click",n.handleClickOutside)},n.deinit=function(e,t,i){const s=e.data.container;n.unbindEvents(s),s.off("beforeAjaxSuccess.tribeEvents",n.deinit)},n.init=function(e,t,i,s){i.find(n.selectors.icalLinks).length&&(n.bindEvents(i),i.on("beforeAjaxSuccess.tribeEvents",{container:i},n.deinit))},e(document).ready((function(){n.init(null,0,e("body"),{})})),e(n.ready)}(jQuery,tribe.events.views.icalLinks),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.icalLinks={}