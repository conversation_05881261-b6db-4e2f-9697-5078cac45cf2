(()=>{function e(e,n){if(e){if("string"==typeof e)return t(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function n(e,t,n){if(n){var r=e.getElementsByClassName(t)[0];r&&(r.textContent=n)}}function r(t,r){for(var l=0,a=Object.entries(r);l<a.length;l++){var o=(i=a[l],u=2,function(e){if(Array.isArray(e))return e}(i)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,l,a,o,i=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(i.push(r.value),i.length!==t);u=!0);}catch(e){s=!0,l=e}finally{try{if(!u&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw l}}return i}}(i,u)||e(i,u)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());n(t,o[0],o[1])}var i,u}!function(){var t,n=function(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=e(t))){r&&(t=r);var l=0,a=function(){};return{s:a,n:function(){return l>=t.length?{done:!0}:{done:!1,value:t[l++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var e=r.next();return i=e.done,e},e:function(e){u=!0,o=e},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw o}}}}(document.getElementsByClassName("cm-contact-form"));try{for(n.s();!(t=n.n()).done;)t.value.onsubmit=function(e){var t,n,l,a,o,i,u,s;e.preventDefault();var m=e.target,c=null===(t=m.getElementsByClassName("firstName")[0])||void 0===t?void 0:t.value,f=null===(n=m.getElementsByClassName("lastName")[0])||void 0===n?void 0:n.value,v=null===(l=m.getElementsByClassName("email")[0])||void 0===l?void 0:l.value,d=null===(a=m.getElementsByClassName("telephone")[0])||void 0===a?void 0:a.value,y=null===(o=m.getElementsByClassName("consent_check")[0])||void 0===o?void 0:o.checked,b=(null===(i=m.getElementsByClassName("list_id")[0])||void 0===i?void 0:i.value)||null;jQuery.post(null===(u=ce4wp_form_submit_data)||void 0===u?void 0:u.url,{action:"ce4wp_form_submission",nonce:null===(s=ce4wp_form_submit_data)||void 0===s?void 0:s.nonce,first_name:c,last_name:f,email:v,telephone:d,consent:y,list_id:b}).done((function(){var e,t=m.parentElement,n=null===(e=t.getElementsByClassName("onSubmissionSetting")[0])||void 0===e?void 0:e.value;if("redirect"===n){var l,a=new RegExp("^(https?)://"),o=null===(l=t.getElementsByClassName("redirect")[0])||void 0===l?void 0:l.value;a.test(o)||(o="https://"+o),window.location.href=o}else m.style.visibility="hidden","summary"===n&&r(t,{firstNameSummary:c?c+" ":null,lastNameSummary:f,telephoneSummary:d,emailSummary:v}),t.getElementsByClassName("onSubmission")[0].style.display="block"}))}}catch(e){n.e(e)}finally{n.f()}}()})()