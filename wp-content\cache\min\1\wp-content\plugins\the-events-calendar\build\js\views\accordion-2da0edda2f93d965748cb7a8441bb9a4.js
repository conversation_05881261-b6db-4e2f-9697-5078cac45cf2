tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.accordion={},function(n,e){"use strict";const t=n(document);e.selectors={accordionTrigger:'[data-js~="tribe-events-accordion-trigger"]'},e.setOpenAccordionA11yAttrs=function(n,e){n.attr("aria-expanded","true"),e.attr("aria-hidden","false")},e.setCloseAccordionA11yAttrs=function(n,e){n.attr("aria-expanded","false"),e.attr("aria-hidden","true")},e.closeAllAccordions=function(i){i.find(e.selectors.accordionTrigger).each((function(i,o){const r=n(o),c=r.attr("aria-controls"),a=t.find("#"+c);e.closeAccordion(r,a)}))},e.openAccordion=function(n,t){e.setOpenAccordionA11yAttrs(n,t),t.css("display","block")},e.closeAccordion=function(n,t){e.setCloseAccordionA11yAttrs(n,t),t.css("display","")},e.toggleAccordion=function(t){t.preventDefault(),t.stopPropagation();var i=t.data.container,o=n(t.data.target),r=o.attr("aria-controls"),c=i.find("#"+r);"true"===o.attr("aria-expanded")?e.closeAccordion(o,c):e.openAccordion(o,c)},e.deinitAccordionA11yAttrs=function(n,e){n.removeAttr("aria-expanded").removeAttr("aria-controls"),e.removeAttr("aria-hidden")},e.initAccordionA11yAttrs=function(n,e){n.attr("aria-expanded","false").attr("aria-controls",e.attr("id")),e.attr("aria-hidden","true")},e.deinitAccordion=function(t,i){n(i).off("click",e.toggleAccordion)},e.initAccordion=function(t){return function(i,o){n(o).on("click",{target:o,container:t},e.toggleAccordion),n(o).on("keydown",(function(e){13!==e.keyCode&&32!==e.keyCode||(e.preventDefault(),e.stopPropagation(),n(o).trigger("click"))})),n(o).attr("tabindex")||n(o).attr("tabindex","0")}},e.unbindAccordionEvents=function(n){n.find(e.selectors.accordionTrigger).each(e.deinitAccordion)},e.bindAccordionEvents=function(n){n.find(e.selectors.accordionTrigger).each(e.initAccordion(n))},e.unbindEvents=function(n,t,i){const o=n.data.container;e.unbindAccordionEvents(o),o.off("beforeAjaxSuccess.tribeEvents",e.unbindEvents)},e.bindEvents=function(n,t,i,o){e.bindAccordionEvents(i),i.on("beforeAjaxSuccess.tribeEvents",{container:i},e.unbindEvents)},e.ready=function(){tribe.events.views.manager&&t.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,e.bindEvents)},n(e.ready)}(jQuery,tribe.events.views.accordion),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.accordion={}