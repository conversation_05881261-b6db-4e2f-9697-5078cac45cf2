tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.manager={},function(e,t,n){"use strict";var r=e(window);n.nonces=null,n.selectors={container:'[data-js="tribe-events-view"]',form:'[data-js="tribe-events-view-form"]',link:'[data-js="tribe-events-view-link"]',dataScript:'[data-js="tribe-events-view-data"]',loader:".tribe-events-view-loader",loaderText:".tribe-events-view-loader__text",hiddenElement:".tribe-common-a11y-hidden",nonceScript:'[data-js="tribe-events-view-nonce-data"]'},n.lastLocation={origin:"",pathname:""},n.doingPopstate=!1,n.currentAjaxRequest=null,n.$lastContainer=e(),n.$containers=e(),n.cleanup=function(t){var r=e(t),a=r.find(n.selectors.form),i=r.find(n.selectors.dataScript),o={};i.length&&(o=JSON.parse(i.text().trim())),r.trigger("beforeCleanup.tribeEvents",[r,o]),r.find(n.selectors.link).off("click.tribeEvents",n.onLinkClick),a.length&&a.off("submit.tribeEvents",n.onSubmit),r.trigger("afterCleanup.tribeEvents",[r,o])},n.setup=function(t,r){var a=e(e.find(n.selectors.nonceScript)),i=e(r),o=i.find(n.selectors.form),s=i.find(n.selectors.dataScript),c={};a.length&&(n.nonces=JSON.parse(e(a[0]).text().trim()),a.remove()),s.length&&(c=JSON.parse(s.text().trim())),i.trigger("beforeSetup.tribeEvents",[t,i,c]),i.find(n.selectors.link).on("click.tribeEvents",n.onLinkClick),o.length&&o.on("submit.tribeEvents",n.onSubmit),i.trigger("afterSetup.tribeEvents",[t,i,c])},n.getContainer=function(t){var r=e(t);return r.is(n.selectors.container)?r:r.parents(n.selectors.container).eq(0)},n.getContainerData=function(e){var t=e.find(n.selectors.dataScript);if(t.length)return JSON.parse(t.text().trim())},n.shouldManageUrl=function(e){var t=e.data("view-manage-url");return void 0===t||/^(true|1|on|yes)$/.test(String(t))},n.updateUrl=function(e){if(!n.doingPopstate&&n.shouldManageUrl(e)){var r=e.find(n.selectors.dataScript);if(r.length){var a=JSON.parse(r.text().trim());t.isObject(a)&&(t.isUndefined(a.url)||t.isUndefined(a.title)||(document.title=a.title,window.history.pushState(null,a.title,a.url),n.lastLocation.pathname=document.location.pathname,n.lastLocation.origin=document.location.origin))}}},n.onLinkClick=async function(t){var r=n.getContainer(this);r.trigger("beforeOnLinkClick.tribeEvents",t),t.preventDefault();var a=n.getContainerData(r),i=e(this).attr("href"),o=a.prev_url,s=n.shouldManageUrl(r),c=r.data("view-shortcode"),l={prev_url:encodeURI(decodeURI(o)),url:encodeURI(decodeURI(i)),should_manage_url:s};return c&&(l.shortcode=c),await n.request(l,r),r.trigger("afterOnLinkClick.tribeEvents",t),!1},n.onSubmit=async function(t){var r=n.getContainer(this);r.trigger("beforeOnSubmit.tribeEvents",t),t.preventDefault();var a=e(this),i={view_data:Qs.parse(a.serialize())["tribe-events-views"]};return await n.request(i,r),r.trigger("afterOnSubmit.tribeEvents",t),!1},n.onPopState=async function(e){var t=e.originalEvent.target,r=t.location.href,a=n.getLastContainer();if(n.lastLocation.origin===t.location.origin&&n.lastLocation.pathname===t.location.pathname)return!1;if(n.lastLocation.pathname=document.location.pathname,n.lastLocation.origin=document.location.origin,!a)return!1;n.currentAjaxRequest&&n.currentAjaxRequest.abort(),n.doingPopstate=!0,a.trigger("beforePopState.tribeEvents",e);var i={url:r};return await n.request(i,a),!1},n.setupRequestData=function(t,r){var a=n.shouldManageUrl(r),i=n.getContainerData(r);t.url||(t.url=i.url),t.prev_url||(t.prev_url=i.prev_url),t.should_manage_url=a,n.nonces&&(t=e.extend(t,n.nonces));var o=r.data("tribeRequestData");return e.isPlainObject(o)?e.extend(o,t):t},n.request=async function(t,r,a={}){wp.hooks.doAction("tec.events.beforeRequest",t,r),r.trigger("beforeRequest.tribeEvents",[t,r]);var i=n.getAjaxSettings(r);i.data=n.setupRequestData(t,r),i.data=n.shirinkUrlComponents(r,i.data);try{const e=new URL(i.url,window.location.origin);e.search=new URLSearchParams(i.data).toString(),e.toString().length>2048&&(i.method="POST")}catch(e){}n.currentAjaxRequest=await e.ajax(e.extend(i,a)),n.ajaxComplete(r),r.trigger("afterRequest.tribeEvents",[t,r]),wp.hooks.doAction("tec.events.afterRequest",t,r)},n.shirinkUrlComponents=(e,r)=>{const{home_url:a,rest_url:i,rest_nonce:o,rest_method:s}=n.getContainerData(e);return t.reduce(r,((e,t,n)=>("prev_url"===n?e.pu=t.replace(a,""):"url"===n?e.u=t.replace(a,""):"should_manage_url"===n?e.smu=t:e[n]=t,e)),{})},n.getAjaxSettings=function(e){return{url:e.data("view-rest-url"),method:e.data("view-rest-method")||"POST",async:!0,beforeSend:n.ajaxBeforeSend,success:n.ajaxSuccess,error:n.ajaxError,context:e}},n.ajaxBeforeSend=function(e,t){var r=this,a=r.find(n.selectors.loader);if(r.trigger("beforeAjaxBeforeSend.tribeEvents",[e,t]),a.length){a.removeClass(n.selectors.hiddenElement.className());var i=a.find(n.selectors.loaderText);i.text(i.text())}r.attr("aria-busy","true"),r.trigger("afterAjaxBeforeSend.tribeEvents",[e,t])},n.ajaxComplete=function(e){var t=e.find(n.selectors.loader);e.trigger("beforeAjaxComplete.tribeEvents",[]),t.length&&t.addClass(n.selectors.hiddenElement.className()),e.trigger("afterAjaxComplete.tribeEvents",[]),n.doingPopstate&&(n.doingPopstate=!1),n.currentAjaxRequest=null},n.ajaxSuccess=function(t,r,a){var i=this;i.trigger("beforeAjaxSuccess.tribeEvents",[t,r,a]);var o=e(t.html),s=o.find(n.selectors.nonceScript);o=o.not(n.selectors.nonceScript),s.length&&(n.nonces=JSON.parse(e(s[0]).text().trim())),n.cleanup(i),document.dispatchEvent(new CustomEvent("containerReplaceBefore.tribeEvents",{detail:i})),i.replaceWith(o),i=o,n.setup(0,i),document.dispatchEvent(new CustomEvent("containerReplaceAfter.tribeEvents",{detail:i})),n.selectContainers(),n.updateUrl(i),i.trigger("afterAjaxSuccess.tribeEvents",[t,r,a]),n.shouldManageUrl(i)&&(n.$lastContainer=i)},n.ajaxError=function(e,t){this.trigger("beforeAjaxError.tribeEvents",[e,t]),this.trigger("afterAjaxError.tribeEvents",[e,t])},n.selectContainers=function(){return n.$containers=e(n.selectors.container),n.$containers},n.getLastContainer=function(){return n.$lastContainer.length||(n.$lastContainer=n.$containers.filter('[data-view-manage-url="1"]').eq(0)),n.$lastContainer},n.ready=function(){n.selectContainers().each(n.setup),n.lastLocation={origin:document.location.origin,pathname:document.location.pathname}},e(n.ready),r.on("popstate",n.onPopState)}(jQuery,window.underscore||window._,tribe.events.views.manager),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.manager={}