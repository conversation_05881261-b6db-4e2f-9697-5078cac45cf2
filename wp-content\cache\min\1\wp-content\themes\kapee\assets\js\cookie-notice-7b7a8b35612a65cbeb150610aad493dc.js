(function($){"use strict";$(document).ready(function(){var cnDomNode=$('#cookie-notice');$(document).on('click','.cn-set-cookie',function(e){e.preventDefault();$(this).setCookieNotice($(this).data('cookie-set'))});if(cnArgs.onScroll=='1'){var cnHandleScroll=function(){var win=$(this);if(win.scrollTop()>parseInt(cnArgs.onScrollOffset)){win.setCookieNotice('accept');win.off('scroll',cnHandleScroll)}};$(window).on('scroll',cnHandleScroll)}
if(document.cookie.indexOf('cookie_notice_accepted')===-1){if(cnArgs.hideEffect==='fade'){cnDomNode.fadeIn(300)}else if(cnArgs.hideEffect==='slide'){cnDomNode.slideDown(300)}else{cnDomNode.show()}
$('body').addClass('cookies-not-accepted')}else{cnDomNode.removeCookieNotice()}});$.fn.setCookieNotice=function(cookie_value){var cnTime=new Date(),cnLater=new Date(),cnDomNode=$('#cookie-notice'),cnSelf=this;cnLater.setTime(parseInt(cnTime.getTime())+parseInt(cnArgs.cookieTime)*1000);cookie_value=cookie_value==='accept'?!0:!1;document.cookie=cnArgs.cookieName+'='+cookie_value+';expires='+cnLater.toGMTString()+';'+(cnArgs.cookieDomain!==undefined&&cnArgs.cookieDomain!==''?'domain='+cnArgs.cookieDomain+';':'')+(cnArgs.cookiePath!==undefined&&cnArgs.cookiePath!==''?'path='+cnArgs.cookiePath+';':'');$.event.trigger({type:"setCookieNotice",value:cookie_value,time:cnTime,expires:cnLater});if(cnArgs.hideEffect==='fade'){cnDomNode.fadeOut(300,function(){cnSelf.removeCookieNotice()})}else if(cnArgs.hideEffect==='slide'){cnDomNode.slideUp(300,function(){cnSelf.removeCookieNotice()})}else{cnSelf.removeCookieNotice()}};$.fn.removeCookieNotice=function(cookie_value){$('#cookie-notice').remove();$('body').removeClass('cookies-not-accepted')}})(jQuery)