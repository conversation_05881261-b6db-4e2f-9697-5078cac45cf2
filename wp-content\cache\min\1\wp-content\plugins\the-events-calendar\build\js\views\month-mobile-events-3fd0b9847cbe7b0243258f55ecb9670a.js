tribe.events=tribe.events||{},tribe.events.views=tribe.events.views||{},tribe.events.views.monthMobileEvents={},function(e,t){"use strict";const n=e(document);t.selectors={calendar:'[data-js="tribe-events-month-grid"]',calendarDay:'[data-js="tribe-events-calendar-month-day-cell-mobile"]',calendarDaySelectedClass:".tribe-events-calendar-month__day-cell--selected",mobileEvents:'[data-js="tribe-events-calendar-month-mobile-events"]',mobileEventsMobileDayShowClass:".tribe-events-calendar-month-mobile-events__mobile-day--show",mobileEventsDefaultNotices:".tribe-events-header__messages--mobile:not(.tribe-events-header__messages--day)"},t.closeAllEvents=function(n){n.find(t.selectors.calendarDay).each((function(s,i){const a=e(i),o=a.attr("aria-controls");let l=e("");o&&(l=n.find("#"+o),tribe.events.views.accordion.closeAccordion(a,l)),t.closeMobileEvents(a,l)}))},t.handleMobileDayClick=function(e,n){const s=e.find(t.selectors.mobileEventsDefaultNotices),i=e.find(t.selectors.mobileEventsMobileDayShowClass).length>0;n&&!i?s.removeClass("tribe-common-a11y-hidden"):s.addClass("tribe-common-a11y-hidden")},t.openMobileEvents=function(e,n){e.attr("aria-controls")&&tribe.events.views.accordion.openAccordion(e,n),e.addClass(t.selectors.calendarDaySelectedClass.className()),n.addClass(t.selectors.mobileEventsMobileDayShowClass.className())},t.closeMobileEvents=function(e,n){e.attr("aria-controls")&&tribe.events.views.accordion.closeAccordion(e,n),e.removeClass(t.selectors.calendarDaySelectedClass.className()),n.removeClass(t.selectors.mobileEventsMobileDayShowClass.className())},t.toggleMobileEvents=function(n){const s=n.data.container,i=e(n.data.target),a=i.attr("aria-controls");let o=e("");a&&(o=s.find("#"+a)),i.hasClass(t.selectors.calendarDaySelectedClass.className())?(t.closeMobileEvents(i,o),t.handleMobileDayClick(s,!0)):(t.closeAllEvents(s),t.handleMobileDayClick(s,!1),t.openMobileEvents(i,o))},t.unbindCalendarEvents=function(n){n.find(t.selectors.calendar).find(t.selectors.calendarDay).each((function(n,s){e(s).off("click",t.toggleMobileEvents)}))},t.bindCalendarEvents=function(n){const s=n.find(t.selectors.calendar);s.find(t.selectors.calendarDay).each((function(i,a){e(a).on("click",{target:a,container:n,calendar:s},t.toggleMobileEvents)}))},t.initState=function(e){const n=e.find(t.selectors.mobileEvents),s={desktopInitialized:!e.data("tribeEventsState").isMobile};n.data("tribeEventsState",s)},t.handleResize=function(e){const n=e.data.container,s=n.find(t.selectors.mobileEvents),i=s.data("tribeEventsState");n.data("tribeEventsState").isMobile?(t.handleMobileDayClick(n,!0),i.desktopInitialized&&(i.desktopInitialized=!1)):i.desktopInitialized||(t.closeAllEvents(n),i.desktopInitialized=!0),s.data("tribeEventsState",i)},t.deinit=function(e,n,s){const i=e.data.container;t.unbindCalendarEvents(i),i.off("resize.tribeEvents",t.handleResize).off("beforeAjaxSuccess.tribeEvents",t.deinit)},t.init=function(e,n,s,i){s.find(t.selectors.mobileEvents).length&&(t.handleMobileDayClick(s,!0),t.initState(s),t.bindCalendarEvents(s),s.on("resize.tribeEvents",{container:s},t.handleResize).on("beforeAjaxSuccess.tribeEvents",{container:s},t.deinit))},t.ready=function(){n.on("afterSetup.tribeEvents",tribe.events.views.manager.selectors.container,t.init)},e(t.ready)}(jQuery,tribe.events.views.monthMobileEvents),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.views=window.tec.events.views||{},window.tec.events.views.monthMobileEvents={}