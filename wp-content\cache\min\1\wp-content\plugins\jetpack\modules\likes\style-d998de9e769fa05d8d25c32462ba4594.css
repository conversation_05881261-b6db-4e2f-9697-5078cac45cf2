#jp-post-flair{padding-top:.5em}div.sharedaddy,#content div.sharedaddy,#main div.sharedaddy{clear:both}div.sharedaddy h3.sd-title{margin:0 0 1em 0;display:inline-block;line-height:1.2;font-size:9pt;font-weight:700}div.sharedaddy h3.sd-title::before{content:"";display:block;width:100%;min-width:30px;border-top:1px solid #dcdcde;margin-bottom:1em}div.jetpack-likes-widget-wrapper{width:100%;min-height:50px;position:relative}div.jetpack-likes-widget-wrapper .sd-link-color{font-size:12px}div.jetpack-comment-likes-widget-wrapper{width:100%;position:relative;min-height:31px}div.jetpack-comment-likes-widget-wrapper iframe{margin-bottom:0}#likes-other-gravatars{display:none;position:absolute;padding:9px 12px 10px 12px;background-color:#fff;border:solid 1px #dcdcde;border-radius:4px;box-shadow:none;min-width:220px;max-height:240px;height:auto;overflow:auto;z-index:1000}#likes-other-gravatars *{line-height:normal}#likes-other-gravatars .likes-text{color:#101517;font-size:12px;font-weight:500;padding-bottom:8px}#likes-other-gravatars ul,#likes-other-gravatars li{margin:0;padding:0;text-indent:0;list-style-type:none}#likes-other-gravatars li::before{content:""}#likes-other-gravatars ul.wpl-avatars{overflow:auto;display:block;max-height:190px}#likes-other-gravatars ul.wpl-avatars li{width:196px;height:28px;float:none;margin:0 0 4px 0}#likes-other-gravatars ul.wpl-avatars li a{margin:0 2px 0 0;border-bottom:none!important;display:flex;align-items:center;gap:8px;text-decoration:none}#likes-other-gravatars ul.wpl-avatars li a span{font-size:12px;color:#2C3338;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}#likes-other-gravatars ul.wpl-avatars li a img{background:none;border:none;border-radius:50%;margin:0!important;padding:1px!important;position:static;box-sizing:border-box}div.sd-box{border-top:1px solid #dcdcde;border-top:1px solid rgba(0,0,0,.13)}.jetpack-likes-widget-unloaded .likes-widget-placeholder,.jetpack-likes-widget-loading .likes-widget-placeholder,.jetpack-likes-widget-loaded iframe{display:block}.jetpack-likes-widget-loaded .likes-widget-placeholder,.jetpack-likes-widget-unloaded iframe,.jetpack-likes-widget-loading iframe{display:none}.entry-content .post-likes-widget,.post-likes-widget,.comment-likes-widget{margin:0;border-width:0}.post-likes-widget-placeholder,.comment-likes-widget-placeholder{margin:0;border-width:0;position:relative}.comment-likes-widget-placeholder{height:18px;position:absolute;display:flex;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif}.comment-likes-widget-placeholder::before{color:#2EA2CC;width:16px;height:16px;content:'';display:inline-block;position:relative;top:3px;padding-right:5px;background-repeat:no-repeat;background-size:16px 16px;// stylelint-disable-next-line function-url-quotes -- quotes are required here. Bug report:https://github.com/stylelint/stylelint/issues/8544 background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Crect x='0' fill='none' width='24' height='24'/%3E%3Cg%3E%3Cpath fill='%232EA2CC' d='M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304'/%3E%3C/g%3E%3C/svg%3E")}.post-likes-widget-placeholder .button{display:none}.post-likes-widget-placeholder .loading,.comment-likes-widget-placeholder .loading{color:#999;font-size:12px}.comment-likes-widget-placeholder .loading{padding-left:5px;margin-top:4px;align-self:center;color:#4E4E4E}div.sharedaddy.sd-like-enabled .sd-like h3{display:none}div.sharedaddy.sd-like-enabled .sd-like .post-likes-widget{width:100%;float:none;position:absolute;top:0}.comment-likes-widget{width:100%}.pd-rating,.cs-rating{display:block!important}.sd-gplus .sd-title{display:none}@media print{.jetpack-likes-widget-wrapper{display:none}}